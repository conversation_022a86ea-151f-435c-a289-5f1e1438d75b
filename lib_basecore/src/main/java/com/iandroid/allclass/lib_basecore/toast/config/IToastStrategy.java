package com.iandroid.allclass.lib_basecore.toast.config;

import android.app.Application;

import com.iandroid.allclass.lib_basecore.toast.ToastParams;

/**
 * time   : 2019/05/19
 * desc   : Toast 处理策略
 */
public interface IToastStrategy {

    /**
     * 注册策略
     */
    void registerStrategy(Application application);

    /**
     * 创建 Toast
     */
    IToast createToast(ToastParams params);

    /**
     * 显示 Toast
     */
    void showToast(ToastParams params);

    /**
     * 取消 Toast
     */
    void cancelToast();
}