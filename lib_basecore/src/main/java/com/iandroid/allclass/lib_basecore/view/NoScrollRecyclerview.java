package com.iandroid.allclass.lib_basecore.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.widget.ListView;

import androidx.recyclerview.widget.RecyclerView;

@SuppressLint("ClickableViewAccessibility")
public class NoScrollRecyclerview extends RecyclerView {
    public NoScrollRecyclerview(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    public NoScrollRecyclerview(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public NoScrollRecyclerview(Context context) {
        super(context);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        int expandSpec = MeasureSpec.makeMeasureSpec(Integer.MAX_VALUE >> 2,
            MeasureSpec.AT_MOST);
        super.onMeasure(widthMeasureSpec, expandSpec);
    }

}
