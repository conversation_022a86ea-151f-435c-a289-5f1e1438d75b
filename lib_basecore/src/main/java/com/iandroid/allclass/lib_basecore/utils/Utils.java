package com.iandroid.allclass.lib_basecore.utils;

import android.content.ContentResolver;
import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.content.res.AssetManager;
import android.content.res.Resources;
import android.net.Uri;
import android.os.Build;
import android.util.Base64;
import android.util.DisplayMetrics;
import android.view.WindowManager;

import com.iandroid.allclass.lib_basecore.R;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/**
 * created by wangkm
 * on 2020/7/14.
 */
public class Utils {
    public static final String WEIXIN_PKG_NAME = "com.tencent.mm";

    /**
     * 获得状态栏的高度
     *
     * @param context
     * @return
     */
    public static int getStatusHeight(Context context) {

        int statusHeight = -1;
        try {
            int result = 0;
            int resourceId = context.getResources().getIdentifier("status_bar_height", "dimen", "android");
            if (resourceId > 0) {
                result = context.getResources().getDimensionPixelSize(resourceId);
            }
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return statusHeight;
    }


    /**
     * 获得屏幕高度
     *
     * @param context
     * @return
     */
    public static int getScreenHeight(Context context) {
        if (context == null)
            return 0;
        WindowManager wm = (WindowManager) context
                .getSystemService(Context.WINDOW_SERVICE);
        DisplayMetrics outMetrics = new DisplayMetrics();
        wm.getDefaultDisplay().getMetrics(outMetrics);
        return outMetrics.heightPixels;
    }

    /**
     * 获得屏幕宽度
     *
     * @param context
     * @return
     */
    public static int getScreenWidth(Context context) {
        if (context == null)
            return 0;
        WindowManager wm = (WindowManager) context
                .getSystemService(Context.WINDOW_SERVICE);
        DisplayMetrics outMetrics = new DisplayMetrics();
        wm.getDefaultDisplay().getMetrics(outMetrics);
        return outMetrics.widthPixels;
    }

    public static boolean checkSpannableRange(int totalLength, int startIndex, int endIndex) {
        return 0 < totalLength
                && 0 <= startIndex && startIndex <= totalLength
                && 0 <= endIndex && endIndex <= totalLength
                && startIndex < endIndex;
    }

    public static boolean validPhoneNumberLength(int areaCode, String phone) {
        if (areaCode <= 0 || phone == null) return false;
        int length = phone.length();
        boolean suffice = false;
        switch (areaCode) {
            case 86:
                suffice = phone.startsWith("1") && length == 11; //大陆CN +86 号码是1开头的11位数字
                break;
            case 886:
                suffice = phone.startsWith("0") ? length == 10 : length == 9; //台湾TW +886 号码是09开头的10位数字（也有人不输入0，就是9位）
                break;
            case 852://香港HK +852 号码是5/6/9开头的8位数字
            case 853://澳门MC +853 号码是6开头的8位数字
            case 65://新加坡SG  +65  8或9开头的8位数字
                suffice = length == 8;
                break;
            case 60:
                suffice = length == 9 || length == 10; //马来西亚MY +60 【10-19开头（10、11、12、13….19）】+7位或8位数字【一共9位数字或者10位数字】
                break;
            case 81:
                suffice = length == 11;  //日本JP+81 号码是以080、dao090开头，号码有11位
                break;
            default:
                suffice = length >= 8;
                break;
        }
        return suffice;
    }

    public static String decryptAES(String decryptString, String decryptKey, String iv) {
        try {
            byte[] raw = decryptKey.getBytes("ASCII");
            SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            IvParameterSpec ivParameterSpec = new IvParameterSpec(iv.getBytes());
            cipher.init(Cipher.DECRYPT_MODE, skeySpec, ivParameterSpec);
            byte[] encrypted1 = Base64.decode(decryptString, Base64.DEFAULT);// 先用base64解密
            byte[] original = cipher.doFinal(encrypted1);
            String originalString = new String(original, "utf-8");
            return originalString;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static long covertLongToIntSafely(long value) {
        if (value > Integer.MAX_VALUE) {
            return Integer.MAX_VALUE;
        } else if (value < Integer.MIN_VALUE) {
            return Integer.MIN_VALUE;
        }
        return value;
    }

    public static boolean isApkInstalled(Context context, String packageName) {
        if (packageName == null || packageName.isEmpty())
            return false;
        try {
            context.getPackageManager().getApplicationInfo(
                    packageName, PackageManager.GET_SHARED_LIBRARY_FILES); // GET_UNINSTALLED_PACKAGES
            return true;
        } catch (PackageManager.NameNotFoundException e) {
            return false;
        }
    }


    public static boolean isWeChatInstalled(Context context) {
        return isApkInstalled(context, WEIXIN_PKG_NAME);
    }

    /**
     * 去掉空白字符，这里只去掉了尾部的空白字符
     *
     * @param sequence 需处理的字符
     * @return 处理过的字符
     */
    public static CharSequence trimFrom(CharSequence sequence) {
        int len = sequence.length();
        int first = 0;
        int last;

        for (last = len - 1; last > first; last--) {
            if (!matches(sequence.charAt(last))) {
                break;
            }
        }

        return sequence.subSequence(first, last + 1);
    }

    private static boolean matches(char c) {
        switch (c) {
            case '\t':
            case '\n':
            case '\013':
            case '\f':
            case '\r':
            case ' ':
            case '\u0085':
            case '\u1680':
            case '\u2028':
            case '\u2029':
            case '\u205f':
            case '\u3000':
                return true;
            case '\u2007':
                return false;
            default:
                return c >= '\u2000' && c <= '\u200a';
        }
    }

    public static String readAssetFileString(Context context, String file) {
        StringBuilder sb = new StringBuilder();
        try {
            if (context == null) return null;
            AssetManager manager = context.getResources().getAssets();
            InputStream inputStream = manager.open(file);
            InputStreamReader isr = new InputStreamReader(inputStream, "UTF-8");
            BufferedReader bufferedReader = new BufferedReader(isr);
            String s;
            while ((s = bufferedReader.readLine()) != null) {
                sb.append(s).append('\n');
            }
            bufferedReader.close();
            return sb.toString();
        } catch (IOException e) {
            return null;
        }
    }

    public static final int ANDROID_TIRAMISU = 33;

    public static boolean checkSDKVersionAndTargetIsTIRAMISU(Context context) {
        ApplicationInfo applicationInfo = context.getApplicationInfo();
        return Build.VERSION.SDK_INT >= ANDROID_TIRAMISU
                && applicationInfo != null
                && applicationInfo.targetSdkVersion >= ANDROID_TIRAMISU;
    }

    public static Uri getUriFromDrawableRes(Context context, int id) {
        Resources resources = context.getResources();
        String path =
                ContentResolver.SCHEME_ANDROID_RESOURCE
                        + "://"
                        + resources.getResourcePackageName(id)
                        + "/"
                        + resources.getResourceTypeName(id)
                        + "/"
                        + resources.getResourceEntryName(id);
        return Uri.parse(path);
    }
}
