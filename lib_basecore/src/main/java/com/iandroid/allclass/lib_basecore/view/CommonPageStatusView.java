package com.iandroid.allclass.lib_basecore.view;

import android.content.Context;
import android.graphics.drawable.AnimationDrawable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.iandroid.allclass.lib_basecore.R;

/**
 * <AUTHOR>
 * @date 2020/10/23.
 */
public class CommonPageStatusView extends FrameLayout {

    private ImageView ivIcon;
    private TextView tvTitle;
    private TextView tvDesc;
    public TextView tvReloadBtn;

    public CommonPageStatusView(@NonNull Context context) {
        this(context, null);
    }

    public CommonPageStatusView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    private void init() {
        LayoutInflater.from(this.getContext()).inflate(R.layout.layout_com_status, this, true);
        ivIcon = findViewById(R.id.exceptionIcon);
        tvTitle = findViewById(R.id.exceptionTitle);
        tvDesc = findViewById(R.id.exceptionDesc);
        tvReloadBtn = findViewById(R.id.retryActionBtn);
        ivIcon.setVisibility(View.GONE);
        tvReloadBtn.setVisibility(View.GONE);
        tvDesc.setVisibility(View.GONE);
        tvTitle.setVisibility(View.GONE);
    }

    public void showException(int resImgId, String msg) {
        if (resImgId == 0)
            ivIcon.setImageResource(R.drawable.ic_exception);
        else {
            ivIcon.setImageResource(resImgId);
        }
        ivIcon.setBackgroundResource(0);

        if (msg != null && !TextUtils.isEmpty(msg))
            tvTitle.setText(msg);
        else {
            tvTitle.setText(R.string.exception_title);
        }

        ivIcon.setVisibility(View.VISIBLE);
        tvTitle.setVisibility(View.VISIBLE);
        tvDesc.setVisibility(View.VISIBLE);
        tvReloadBtn.setVisibility(View.VISIBLE);
    }

    public void showLoading(String loadMsg) {
        ivIcon.setImageResource(0);
        ivIcon.setBackgroundResource(R.drawable.ani_loading);
        AnimationDrawable animationDrawable = (AnimationDrawable) ivIcon.getBackground();
        if (animationDrawable != null) animationDrawable.start();
        if (loadMsg == null || loadMsg.isEmpty())
            tvTitle.setText(R.string.page_loading);
        else {
            tvTitle.setText(loadMsg);
        }
        tvTitle.setVisibility(View.VISIBLE);
        ivIcon.setVisibility(View.VISIBLE);
    }

    public void hideLoading() {
        AnimationDrawable animationDrawable = (AnimationDrawable) ivIcon.getBackground();
        if (animationDrawable != null) animationDrawable.stop();
        ivIcon.setImageResource(0);
        ivIcon.setBackgroundResource(0);
    }
}
