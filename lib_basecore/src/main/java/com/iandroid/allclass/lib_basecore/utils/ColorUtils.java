package com.iandroid.allclass.lib_basecore.utils;

import android.graphics.Color;
import android.text.TextUtils;

/**
 * created by wangkm
 * on 2020/10/20.
 */
public class ColorUtils {

    public static int str2color(String colorStr, int failColor) {
        int color = failColor;
        if (!TextUtils.isEmpty(colorStr)) {
            try {
                if (colorStr.startsWith("0x")) {
                    color = Color.parseColor(colorStr.replace("0x", "#"));
                } else if (colorStr.startsWith("#")) {
                    color = Color.parseColor(colorStr);
                } else {
                    color = Color.parseColor("#" + colorStr);
                }
            } catch (IllegalArgumentException e) {
                e.printStackTrace();
            }
        }
        return color;
    }
}
