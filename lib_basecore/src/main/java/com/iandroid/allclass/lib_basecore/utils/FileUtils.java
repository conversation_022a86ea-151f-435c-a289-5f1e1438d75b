package com.iandroid.allclass.lib_basecore.utils;

import android.os.Environment;
import android.text.TextUtils;

import java.io.BufferedInputStream;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

/**
 * created by wangkm
 * on 2021/2/2.
 */
public class FileUtils {

    public static String getLocalFilePath(String subPathName, boolean isCache) {
        String result;
        try {
            result = Environment.getExternalStorageDirectory().getPath();
            if (TextUtils.isEmpty(result)) {
                String sdPath = getSDCardPath();
                if (!TextUtils.isEmpty(sdPath)) {
                    result = sdPath;
                }
            }
            if (result != null && !result.isEmpty()) {
                if (!result.trim().endsWith("/")) {
                    result += "/";
                }

                // Check default root folder: LangLive exists or not.
                result += "seeuing";
                File rootFile = new File(result);
                if (!rootFile.exists()) {
                    rootFile.mkdir();
                }

                // Check the sub path which you want.
                result += "/" + subPathName;
                File subFile = new File(result);
                if (!subFile.exists()) {
                    subFile.mkdir();
                }
                result += "/";
            }
        } catch (Exception ex) {
            result = "";
        }
        return result;
    }


    public static String getSDCardPath() {
        String cmd = "cat /proc/mounts";
        Runtime run = Runtime.getRuntime();
        BufferedInputStream in = null;
        BufferedReader inBr = null;
        String result = "";
        try {
            Process p = run.exec(cmd);
            in = new BufferedInputStream(p.getInputStream());
            inBr = new BufferedReader(new InputStreamReader(in));

            String lineStr;
            while ((lineStr = inBr.readLine()) != null) {
                if (lineStr.contains("sdcard") && lineStr.contains(".android_secure")) {
                    String[] strArray = lineStr.split(" ");
                    if (strArray != null && strArray.length >= 5) {
                        result = strArray[1].replace("/.android_secure", "");
                        break;
                    }
                }
                if (p.waitFor() != 0 && p.exitValue() == 1) {
                    // p.exitValue()==0 indicate exit normally, 1 indicate failed.
                }
            }
        } catch (Exception e) {
            //return Environment.getExternalStorageDirectory().getPath();
        } finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            try {
                if (inBr != null) {
                    inBr.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return result;
    }


    public static List<File> getAllFiles(File folder) {
        List<File> fileList = new ArrayList<>();
        if (folder.exists() && folder.isDirectory()) {
            File[] files = folder.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isFile()) {
                        fileList.add(file);
                    } else if (file.isDirectory()) {
                        fileList.addAll(getAllFiles(file));
                    }
                }
            }
        }
        return fileList;
    }
}
