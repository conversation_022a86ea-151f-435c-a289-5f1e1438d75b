package com.iandroid.allclass.lib_basecore.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.concurrent.TimeUnit;

public class DateUtils {
    private static final String TAG = DateUtils.class.getSimpleName();
    private SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd_HHmmssSS");

    private DateUtils() {
        // default implementation ignored
    }

    private static class SingletonHolder {
        static DateUtils sInstance = new DateUtils();
    }

    public static DateUtils getInstance() {
        return SingletonHolder.sInstance;
    }

    /**
     * 判断两个时间戳相差多少秒
     *
     * @param d
     * @return
     */
    public int dateDiffer(long d) {
        try {
            long l1 = Long.parseLong(String.valueOf(System.currentTimeMillis()).substring(0, 10));
            long interval = l1 - d;
            return (int) Math.abs(interval);
        } catch (Exception e) {
            // RLog.e(TAG, e.getMessage());
            return -1;
        }
    }

    /**
     * 时间戳转换成时间格式
     *
     * @param duration
     * @return
     */
    public String formatDurationTime(long duration) {
        return String.format(
                Locale.getDefault(),
                "%02d:%02d",
                TimeUnit.MILLISECONDS.toMinutes(duration),
                TimeUnit.MILLISECONDS.toSeconds(duration)
                        - TimeUnit.MINUTES.toSeconds(TimeUnit.MILLISECONDS.toMinutes(duration)));
    }

    /**
     * 根据时间戳创建文件名
     *
     * @param prefix 前缀名
     * @return
     */
    public String getCreateFileName(String prefix) {
        long millis = System.currentTimeMillis();
        return prefix + sf.format(millis);
    }

    /**
     * 计算两个时间间隔
     *
     * @param sTime
     * @param eTime
     * @return
     */
    public String cdTime(long sTime, long eTime) {
        long diff = eTime - sTime;
        return diff > 1000 ? diff / 1000 + "s" : diff + "ms";
    }

    /**
     * 格式化日期
     *
     * @param timeMillis 时间戳（毫秒值）
     * @param format     要展示的格式（eg：yyyy.MM.dd HH:mm:ss）
     * @return 格式化后的日期字符串
     */
    public static String formatDate(long timeMillis, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(new Date(timeMillis));
    }

    /**
     * 日期转时间戳
     *
     * @param time   时间戳（毫秒值）
     * @param format 要展示的格式（eg：yyyy.MM.dd HH:mm:ss）
     * @return 格式化后的日期字符串
     */
    public static Long formatDate(String time, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format, Locale.getDefault());
        Date date = null; // 解析日期字符串
        try {
            date = sdf.parse(time);
            return date.getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return -1L;
    }

    // 获取当前日期，返回格式化的日期字符串（例如：yyyy-MM-dd）
    public static String getCurrentDate() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        Date currentDate = new Date();
        return dateFormat.format(currentDate);
    }

    /**
     * 获取当前月份1号0点0分0秒的时间戳
     * @return 时间戳（毫秒）
     */
    public static long getFirstDayOfMonthTimestamp() {
        Calendar calendar = Calendar.getInstance();
        // 设置为当前月份的1号
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        // 设置小时、分钟、秒和毫秒为 0
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        // 返回时间戳（毫秒）
        return calendar.getTimeInMillis();
    }

    public static long getLastDayOfMonthFromTimestamp(long timestamp) {
        Calendar calendar = Calendar.getInstance();
        // 设置时间戳
        calendar.setTimeInMillis(timestamp);

        // 获取当前时间戳对应的月份的最后一天
        int lastDay = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        calendar.set(Calendar.DAY_OF_MONTH, lastDay);

        // 设置时间为 23:59:59
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);

        // 返回最后一天 23:59:59 的时间戳
        return calendar.getTimeInMillis();
    }
}
