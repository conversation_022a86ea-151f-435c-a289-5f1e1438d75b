package com.iandroid.allclass.lib_basecore.view;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;

import androidx.fragment.app.FragmentManager;

import com.iandroid.allclass.lib_basecore.R;
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemView;
import com.iandroid.allclass.lib_basecore.view.recyclerview.RvItem;

/**
 * created by wangkm
 * on 2020/10/19.
 */
@RvItem(id = NomoreDataItemView.itemView_nomoreData, spanCount = 1)
public class NomoreDataItemView extends BaseRvItemView {
    public static final int itemView_nomoreData = -10000;

    public NomoreDataItemView(Context context, FragmentManager fm, ViewGroup parent) {
        super(context, fm, parent);
    }

    @Override
    protected int attachLayoutId() {
        return R.layout.view_footer_ui;
    }

    @Override
    protected void initView(Context context, View view) {

    }

    @Override
    protected void setView() {

    }
}
