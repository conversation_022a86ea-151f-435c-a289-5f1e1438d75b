package com.fascin.chatter

import android.app.Dialog
import android.content.Intent
import android.os.Bundle
import android.view.Gravity
import androidx.appcompat.app.AppCompatActivity
import com.iandroid.allclass.lib_basecore.utils.DeviceUtils
import com.iandroid.allclass.lib_basecore.utils.StatusBarUtils
import com.iandroid.allclass.lib_basecore.utils.ToastUtils
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.AppController
import com.iandroid.allclass.lib_common.GlideLoader.loadImage
import com.iandroid.allclass.lib_common.UserController
import com.iandroid.allclass.lib_common.Values
import com.iandroid.allclass.lib_common.beans.event.UIOpenAdEvent
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.bean.ActionEntity
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.exts.jsonToObj
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.views.CommonAlertDialog2
import com.iandroid.allclass.lib_common.views.CommonWebAlertDialog2
import com.iandroid.allclass.lib_thirdparty.PassportSDK
import com.iandroid.allclass.lib_thirdparty.push.PushControl
import io.reactivex.Flowable
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import kotlinx.android.synthetic.main.content_splash.ll_timer
import kotlinx.android.synthetic.main.content_splash.sd_ad_img
import kotlinx.android.synthetic.main.content_splash.tv_timer
import java.util.concurrent.TimeUnit

/**
created by wangkm
on 2020/10/12.
 */
class SplashActivity : AppCompatActivity() {
    var compositeDisposable = CompositeDisposable()
    var timer: Disposable? = null
    var action: ActionEntity? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        AppController.applicationBootFlag = true
        if (AppContext.getActivitySize() > 1 && (intent.flags and Intent.FLAG_ACTIVITY_BROUGHT_TO_FRONT) != 0) {
            consumePushIntentNow()
            finish()
            return
        }
        setContentView(R.layout.content_splash)
        StatusBarUtils.immersiveForWindow(this)
        StatusBarUtils.setLightMode(this)
        initBaseContent()
    }

    fun initBaseContent() {
        if (!DeviceUtils.isNetworkConnected(this)) {
            ToastUtils.showText(R.string.splash_no_net)
        } else {
            sd_ad_img.setOnClickListener {
                action?.also {
                    jumpNext(it)
                }
            }
            startTimer()
            ll_timer?.show(true)
            ll_timer?.setOnClickListener { jumpNext(null) }
            startTimer(Values.welComeMaxTime)

            compositeDisposable?.add(SimpleRxBus.observe(UIOpenAdEvent::class) {
                sd_ad_img.loadImage(applicationContext, it.openADData.banner?.cover.orEmpty(), 0)
                action = it.openADData.action
                ll_timer?.show(true)
                startTimer(Values.welComeMaxTime.coerceAtLeast(it.openADData.adStayTime))
            })
        }
    }

    private fun startTimer(time: Long = Values.welComeMaxTime) {
        timer?.dispose()
        tv_timer?.text = time.toString()
        timer = Flowable.intervalRange(
            1,
            time,
            1,
            1,
            TimeUnit.SECONDS
        )
            .onBackpressureLatest()
            .observeOn(AndroidSchedulers.mainThread())
            .doOnNext {
                tv_timer.text = "${time - it}"
            }
            .doOnComplete {
                jumpNext(null)
            }
            .subscribe()
    }

    private fun consumePushIntentNow() {
        if (PushControl.isFromNotificationIntent(intent)) {
            jumpNext(null)
        }
    }

    private fun jumpNext(action: ActionEntity?) {
        clearTask()
        if (!AppController.hasAgreedPrivacyAgreement()) {
            showAgreedPrivacyAgreement(action)
            return
        } else {
            jumpAction()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        clearTask()
    }

    private fun clearTask() {
        timer?.dispose()
        compositeDisposable.dispose()
    }

    private fun showAgreedPrivacyAgreement(action: ActionEntity?) {
        CommonWebAlertDialog2.Builder()
            .setTitle(getString(R.string.app_start_agree))
            .setUrl(
                AppController.getGlobalDataConfig()?.html?.privacy.orEmpty()
            )
            .setCancel(getString(R.string.disagree)) {
                showDisAgreeSecondTipDiaog(it, action)
            }
            .setConfirm(getString(R.string.agreeandgoon)) {
                it.dismiss()
                afterAgreedPrivacyAgreement()
            }
            .create(this)
            .show()
    }

    private fun showDisAgreeSecondTipDiaog(umPrivacyDialog: Dialog, action: ActionEntity?) {
        CommonAlertDialog2.Builder()
            .setContext(getString(R.string.app_start_agree_tip))
            .setContextGravity(Gravity.LEFT)
            .setCancel(getString(R.string.disagree_exit)) {
                //退回桌面
                val home = Intent(Intent.ACTION_MAIN)
                home.addCategory(Intent.CATEGORY_HOME)
                startActivity(home)
                finish()
            }
            .setConfirm(getString(R.string.agreeandgoon)) {
                umPrivacyDialog.dismiss()
                afterAgreedPrivacyAgreement()
            }
            .create(this)
            .show()
    }

    private fun afterAgreedPrivacyAgreement() {
        PassportSDK.instance.initActual()
        (application as? ToolApplication)?.initThirdParty()
        jumpAction()
    }

    private fun jumpAction() {
        if (UserController.hasLoggedIn()) {
            routeAction(ActionType.actionTypeMain) {
                it.param = PushControl.getPushMessageFromIntent(intent) ?: Any()
            }
        } else {
            routeAction(ActionType.actionToLogin)
            //非登录模式下检测是否是来自离线推送并带有traceid的行为
            PushControl.getPushMessageFromIntent(intent)?.action?.jsonToObj<ActionEntity>()
                ?.takeIf { it.trace_id > 0 }?.also {
                    AppContext.iRouteParser!!.handleActionTrace(it)
                }
        }
        finish()
    }
}