package com.fascin.chatter.account.newbie.navigation

import com.fascin.chatter.bean.NewAnchorEntity
import com.fascin.chatter.bean.TaskData
import java.io.Serializable

/**
 * 任务导航图 - 定义任务之间的导航关系和顺序
 * @author: AI Assistant
 * @date: 2025-07-31
 */
data class TaskNavigationGraph(
    val tasks: List<TaskNode>,
    val currentTaskIndex: Int = 0
) : Serializable {

    companion object {
        /**
         * 根据NewAnchorEntity创建导航图
         */
        fun create(entity: NewAnchorEntity): TaskNavigationGraph {
            val taskNodes = mutableListOf<TaskNode>()

            // 按照业务逻辑定义任务顺序和依赖关系
            entity.bindCodeTask?.let { task ->
                taskNodes.add(
                    TaskNode(
                        taskType = TaskType.BIND_CODE,
                        taskData = task,
                        order = 1,
                    )
                )
            }

            entity.courseTask.let { task ->
                taskNodes.add(
                    TaskNode(
                        taskType = TaskType.COURSE,
                        taskData = task,
                        order = 2,
                    )
                )
            }

            entity.bankAccountTask.let { task ->
                taskNodes.add(
                    TaskNode(
                        taskType = TaskType.BANK_ACCOUNT,
                        taskData = task,
                        order = 3,
                    )
                )
            }

            entity.selectModelTask.let { task ->
                taskNodes.add(
                    TaskNode(
                        taskType = TaskType.SELECT_MODEL,
                        taskData = task,
                        order = 4,
                    )
                )
            }

            entity.selectShiftTask.let { task ->
                taskNodes.add(
                    TaskNode(
                        taskType = TaskType.SELECT_SHIFT,
                        taskData = task,
                        order = 5,
                    )
                )
            }

            // 按order排序
            taskNodes.sortBy { it.order }

            // 找到当前应该执行的任务索引
            val currentIndex = findCurrentTaskIndex(taskNodes)

            return TaskNavigationGraph(taskNodes, currentIndex)
        }

        /**
         * 找到当前应该执行的任务索引
         */
        private fun findCurrentTaskIndex(tasks: List<TaskNode>): Int {
            // 找到第一个未完成的任务
            val uncompletedIndex = tasks.indexOfFirst { it.taskData.status == 0 }
            return if (uncompletedIndex >= 0) uncompletedIndex else tasks.size - 1
        }
    }

    /**
     * 获取当前任务
     */
    fun getCurrentTask(): TaskNode? {
        if (currentTaskIndex < 0) return null
        return if (currentTaskIndex < tasks.size) tasks[currentTaskIndex] else null
    }

    /**
     * 获取下一个任务
     */
    fun getNextTask(): TaskNode? {
        val nextIndex = tasks.indexOfFirst { it.taskData.status == 0 }
        if (nextIndex < 0) return null
        return if (nextIndex < tasks.size) tasks[nextIndex] else null
    }

    /**
     * 检查指定任务是否可以执行（依赖是否满足）
     */
    fun canExecuteTask(taskType: TaskType): Boolean {
        val task = tasks.find { it.taskType == taskType } ?: return false
        
        // 检查所有依赖是否已完成
        return task.dependencies.all { dependencyType ->
            tasks.find { it.taskType == dependencyType }?.taskData?.status == 1
        }
    }

    /**
     * 获取所有未完成的任务
     */
    fun getUncompletedTasks(): List<TaskNode> {
        return tasks.filter { it.taskData.status == 0 }
    }

    /**
     * 获取所有已完成的任务
     */
    fun getCompletedTasks(): List<TaskNode> {
        return tasks.filter { it.taskData.status == 1 }
    }

    /**
     * 更新导航图中的任务状态
     */
    fun updateTaskStatus(taskType: TaskType, status: Int): TaskNavigationGraph {
        val updatedTasks = tasks.map { task ->
            if (task.taskType == taskType) {
                task.copy(taskData = task.taskData.copy(status = status))
            } else {
                task
            }
        }
        
        return copy(tasks = updatedTasks)
    }

    /**
     * 获取任务完成进度
     */
    fun getProgress(): TaskProgress {
        val completedCount = getCompletedTasks().size
        val totalCount = tasks.size
        val requiredTasks = tasks.filter { it.isRequired }
        val completedRequiredCount = requiredTasks.count { it.taskData.status == 1 }
        
        return TaskProgress(
            completedCount = completedCount,
            totalCount = totalCount,
            completedRequiredCount = completedRequiredCount,
            totalRequiredCount = requiredTasks.size,
            progressPercentage = if (totalCount > 0) (completedCount * 100) / totalCount else 0
        )
    }
}

/**
 * 任务节点
 */
data class TaskNode(
    val taskType: TaskType,
    val taskData: TaskData,
    val order: Int,
    val isRequired: Boolean = true,
    val dependencies: List<TaskType> = emptyList()
) : Serializable

/**
 * 任务类型枚举
 */
enum class TaskType {
    BIND_CODE,      // 绑定邀请码
    COURSE,         // 课程任务
    BANK_ACCOUNT,   // 银行账户任务
    SELECT_MODEL,   // 选择模型任务
    SELECT_SHIFT    // 选择排班任务
}

/**
 * 任务进度信息
 */
data class TaskProgress(
    val completedCount: Int,
    val totalCount: Int,
    val completedRequiredCount: Int,
    val totalRequiredCount: Int,
    val progressPercentage: Int
) : Serializable {
    fun isAllRequiredCompleted(): Boolean {
        return completedRequiredCount == totalRequiredCount
    }
}
