package com.fascin.chatter.account.newbie

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.bean.AnchorShiftItemEntity
import com.fascin.chatter.databinding.ItemviewNewAnchorShiftBinding
import com.iandroid.allclass.lib_common.utils.ToastUtils
import com.iandroid.allclass.lib_common.utils.clickWithTrigger

/**
 * @Desc: 新主播流程班次选择列表
 * @Created: Quan
 * @Date: 2024/11/26
 */
class AnchorShiftAdapter : RecyclerView.Adapter<AnchorShiftAdapter.ViewHolder>() {

    companion object {
        private const val MAX_SELECTED_COUNT = 5
    }

//    var selectedShiftId = -1 // 选中的item

    private var selectedShiftIds: MutableList<Int> = mutableListOf()

//    private var selectHolder: ViewHolder? = null
    private val dataList = mutableListOf<AnchorShiftItemEntity>()
    private var onItemClickListener: ((AnchorShiftItemEntity) -> Unit)? = null

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val binding: ItemviewNewAnchorShiftBinding = ItemviewNewAnchorShiftBinding.bind(itemView)
    }

    fun setOnItemClickListener(listener: (AnchorShiftItemEntity) -> Unit) {
        this.onItemClickListener = listener
    }

    fun getSelectedShiftIds(): List<Int> {
        return selectedShiftIds
    }

    fun updateData(data: List<AnchorShiftItemEntity>) {
//        selectedShiftId = -1
//        selectHolder = null
        dataList.clear()
        selectedShiftIds.clear()
        if (data.isNotEmpty()) {
            dataList.addAll(data)
        }
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.itemview_new_anchor_shift, parent, false)
        )

    }

    override fun getItemCount(): Int = dataList.size

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = dataList[position]
        holder.binding.also { binding ->
            binding.tvLocalTime.text = item.shiftDate
            binding.tvDisplayTime.text = item.zoneShiftDate

            val isSelected = selectedShiftIds.contains(item.shiftId)
            binding.ivSelect.isSelected = isSelected

//            if (selectedShiftId == item.shiftId) {
//                selectHolder = holder
//                binding.ivSelect.isSelected = true
//            } else {
//                binding.ivSelect.isSelected = false
//            }
        }
        holder.itemView.clickWithTrigger {
//            if (selectedShiftId != item.shiftId) {
//                selectedShiftId = item.shiftId
//                selectHolder?.also { select ->
//                    select.binding.ivSelect.isSelected = false
//                }
//                holder.binding.ivSelect.isSelected = true
//                selectHolder = holder
//                onItemClickListener?.invoke(item)
//            }

            if (selectedShiftIds.size >= MAX_SELECTED_COUNT && !selectedShiftIds.contains(item.shiftId)) {
                ToastUtils.showCenterToast("You can select up to $MAX_SELECTED_COUNT shifts")
                return@clickWithTrigger
            }

            if (!selectedShiftIds.contains(item.shiftId)) {
                selectedShiftIds.add(item.shiftId)
            } else {
                selectedShiftIds.remove(item.shiftId)
            }
            notifyDataSetChanged()
            onItemClickListener?.invoke(item)
        }
    }

}