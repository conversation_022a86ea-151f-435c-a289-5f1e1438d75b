package com.fascin.chatter.account.newbie

import android.os.Bundle
import android.view.KeyEvent
import androidx.recyclerview.widget.LinearLayoutManager
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.account.newbie.navigation.BaseTaskActivity
import com.fascin.chatter.account.newbie.navigation.TaskType
import com.fascin.chatter.component.views.SUButtonStatus
import com.fascin.chatter.databinding.ActivityNewAnchorShiftBinding
import com.iandroid.allclass.lib_common.utils.ToastUtils
import com.iandroid.allclass.lib_common.utils.clickWithTrigger

/**
 * @Desc: 新主播流程班次选择列表
 * @Created: Quan
 * @Date: 2024/11/26
 */
class NewAnchorShiftActivity : BaseTaskActivity() {

    private lateinit var binding: ActivityNewAnchorShiftBinding
    private var adapter: AnchorShiftAdapter? = null

    override fun determineCurrentTaskType(): TaskType? = TaskType.SELECT_SHIFT

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityNewAnchorShiftBinding.inflate(layoutInflater)
        setContentView(binding.root)


        AppModule.userActive()
        setViewUI()
        setModel()
        setListener()
    }

    private fun setViewUI() {
        binding.btnSubmit.setText("Confirm")
        binding.btnSubmit.setViewHeight(56)
        setButtonStatus(SUButtonStatus.Disabled)
        binding.rvSelectShift.layoutManager = LinearLayoutManager(this)
        adapter = AnchorShiftAdapter()
        binding.rvSelectShift.adapter = adapter
    }

    private fun setModel() {
        accountViewModel.anchorShitResult.observe(this) {
            binding.tv1.text = buildString {
                append("Your Local Time")
                if (it.location.isNotEmpty()) {
                    append("\n")
                    append(it.location)
                }
            }
            adapter?.updateData(it.shiftList)
        }
        accountViewModel.anchorShitSubmitResult.observe(this) {
            if (it) {
                ToastUtils.showToast(getString(R.string.balance_success))
                onTaskCompleted()
            } else {
                binding.btnSubmit.setButtonStatus(
                    SUButtonStatus.Activated,
                    activatedBg = R.drawable.ic_anchor_btn_act
                )
            }
        }
        accountViewModel.getAnchorShiftList()
    }

    private fun setListener() {
        adapter?.setOnItemClickListener {
            if (binding.btnSubmit.isLoadingStatus()) return@setOnItemClickListener
            setButtonStatus(if (adapter?.getSelectedShiftIds().isNullOrEmpty()) SUButtonStatus.Disabled else SUButtonStatus.Activated,)
        }
        binding.btnSubmit.clickWithTrigger {
            val selectedShiftIds = adapter?.getSelectedShiftIds() ?: return@clickWithTrigger
            if (selectedShiftIds.isEmpty()) return@clickWithTrigger

            setButtonStatus(SUButtonStatus.Loading)
            accountViewModel.submitAnchorShift(selectedShiftIds)
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        return if (keyCode == KeyEvent.KEYCODE_BACK) {
            true
        } else {
            super.onKeyDown(keyCode, event)
        }
    }

    override fun immersiveIntoStatusBar(): Boolean {
        return true
    }

    override fun isShowTitleBar(): Boolean {
        return false
    }

    private fun setButtonStatus(status: SUButtonStatus) {
        if (!this::binding.isInitialized) {
            return
        }

        binding.btnSubmit.setButtonStatus(
            status = status,
            activatedBg = R.drawable.bg_new_anchor_guide_btn,
            disabledBg = R.drawable.bg_new_anchor_guide_btn_disable,
            disabledColor = "#ffffff",
            loadingBg = R.drawable.bg_new_anchor_guide_btn
        )
    }
}