package com.fascin.chatter.account

//import kotlinx.android.synthetic.main.content_login.createAccountBtn
import android.os.Bundle
import android.text.InputType
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.R
import com.fascin.chatter.account.utils.DevelopUtils
import com.fascin.chatter.bean.RegisterEnableEntity
import com.fascin.chatter.bean.event.LoginFinishEvent
import com.fascin.chatter.component.views.SUButtonStatus
import com.fascin.chatter.im.IMModule
import com.fascin.chatter.isValidAccount
import com.fascin.chatter.isValidPasswordLength
import com.fascin.chatter.loginReturn
import com.iandroid.allclass.lib_basecore.base.BaseActivity
import com.iandroid.allclass.lib_common.event.EventKey
import com.iandroid.allclass.lib_common.repository.CommonRepository
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.SPConstants
import com.iandroid.allclass.lib_common.utils.SPUtils
import com.iandroid.allclass.lib_common.utils.keyboard.KeyboardUtils
import com.iandroid.allclass.lib_common.views.CommonCloseVerticalDialog
import kotlinx.android.synthetic.main.content_login.edViewAccount
import kotlinx.android.synthetic.main.content_login.edViewPwd
import kotlinx.android.synthetic.main.content_login.loginActionBtn
import kotlinx.android.synthetic.main.content_login.login_logo
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch


class LoginActivity : BaseActivity() {
    private lateinit var accountViewModel: AccountViewModel
    private var isAccountValid = false
    private var isPwdValid: Boolean = false
    private var fillAccountData: RegisterEnableEntity? = null

    private var checkJob: Job? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.content_login)
    }

    override fun initBaseContent() {
        super.initBaseContent()
        SPUtils.put(this, SPConstants.KEY_SORT_MODE, "")
        edViewPwd.setPasswordToggle(true, R.drawable.password_toggle_drawable)
        edViewAccount.setInputType(InputType.TYPE_TEXT_VARIATION_EMAIL_ADDRESS)
        setBtnDisabled()
        accountViewModel = ViewModelProvider(
            this, AccountViewModel.ViewModeFactory()
        )[AccountViewModel::class.java]

        accountViewModel.loginResult.observe(this) {
            when (it.regStatus) {
                1 -> {
                    CommonCloseVerticalDialog.Builder()
                        .showCloseBtn(true)
                        .setTitle("Registration Under Review")
                        .setContext("We have received your registration application and it is under review. Please wait patiently.")
                        .setCancel { setBtnActivated() }
                        .setConfirm("Ok, I got it") {
                            setBtnActivated()
                        }
                        .create()
                        .show(supportFragmentManager, CommonCloseVerticalDialog::class.java.name)
                }

                2 -> {
                    CommonCloseVerticalDialog.Builder()
                        .showCloseBtn(true)
                        .setTitle("Rejection Notice")
                        .setContext("Your application has been rejected")
                        .setCancel { setBtnActivated() }
                        .setConfirm("Ok, I got it") {
                            setBtnActivated()
                        }
                        .create()
                        .show(supportFragmentManager, CommonCloseVerticalDialog::class.java.name)
                }

                else -> {
                    traceNullAdId(it.userId, true)
                    loginReturn(false)
                    finish()
                }
            }
        }

        accountViewModel.loginError.observe(this) {
            traceNullAdId(edViewAccount.getText(), false)
            checkMainBtnEnable()
        }

        accountViewModel?.compositeDisposable?.add(SimpleRxBus.observe(LoginFinishEvent::class) {
            finish()
        })

        login_logo.setOnLongClickListener {
            DevelopUtils.showEnvSwitchMenulist(this)
            true
        }

        loginActionBtn.setText(getText(R.string.email_sign_in))

        edViewAccount.afterTextChangeBlock = {
            isAccountValid = edViewAccount.getText().isValidAccount()
            if (isAccountValid) {
                edViewAccount.errorHintMsg(null)
            } else {
                edViewAccount.errorHintMsg(getString(R.string.email_error))
            }
            checkMainBtnEnable()
        }

        edViewPwd.afterTextChangeBlock = {
            val password = edViewPwd.getText()
            isPwdValid = password.isValidPasswordLength()
            if (isPwdValid) {
                edViewPwd.errorHintMsg(null)
                isPwdValid = true
            } else {
                edViewPwd.errorHintMsg(getString(R.string.reg_create_password_error))
            }
            checkMainBtnEnable()
        }



        IMModule.instance.lastQueryTimeMap.clear()
        loginActionBtn.setOnClickListener {
            KeyboardUtils.hideKeyboard(this)
            setBtnLoading()
            accountViewModel.accountLogin(edViewAccount.getText(), edViewPwd.getText())
//            accountViewModel.accountLogin("487948", "9jypfrzrysjv")
        }

        createAccount()
    }

    /**
     * 登录时获取不到adjust Adid时上报
     */
    private fun traceNullAdId(userId: String, isSuccess: Boolean) {
        val curAdId = SPUtils.getString(application, SPConstants.KEY_ADID, "")
        if (curAdId.isNullOrEmpty() || curAdId == "null") {
            CommonRepository.eventTrace(EventKey.login_adjust_adid_null) {
                "chatterID" to userId
                "curAdId" to curAdId
                "isSuccess" to isSuccess
            }
        }
    }

    private fun checkMainBtnEnable() {
        val isEnabled = isPwdValid && isAccountValid
        if (isEnabled) {
            setBtnActivated()
        } else {
            setBtnDisabled()
        }
    }

    private fun setBtnActivated() {
        loginActionBtn.setButtonStatus(
            status = SUButtonStatus.Activated,
            disabledBg = R.drawable.bg_progress_button_login_disable,
            loadingBg = R.drawable.bg_progress_button_login_loading,
            activatedBg = R.drawable.bg_progress_button_login_active,
            disabledColor = "#FFFFFF"
        )
    }

    private fun setBtnLoading() {
        loginActionBtn.setButtonStatus(
            status = SUButtonStatus.Loading,
            disabledBg = R.drawable.bg_progress_button_login_disable,
            loadingBg = R.drawable.bg_progress_button_login_loading,
            activatedBg = R.drawable.bg_progress_button_login_active,
            disabledColor = "#FFFFFF"
        )
    }

    private fun setBtnDisabled() {
        loginActionBtn.setButtonStatus(
            status = SUButtonStatus.Disabled,
            disabledBg = R.drawable.bg_progress_button_login_disable,
            loadingBg = R.drawable.bg_progress_button_login_loading,
            activatedBg = R.drawable.bg_progress_button_login_active,
            disabledColor = "#FFFFFF"
        )
    }

    /**
     * 自主创号
     */
    private fun createAccount() {
        startCheckingAdId()
        accountViewModel.checkAdIdResult.observe(this) {
            fillAccountData = it
//            createAccountBtn.show(it.canReg == 1)
            if (it.cid.isNotEmpty()) {
                edViewAccount.setText(it.cid)
            }
        }
//        createAccountBtn.clickWithTrigger {
//            routeAction(ActionType.actionCreateAccount)
//        }
    }

    /**
     * 检查是否可以创建账号
     */
    private fun startCheckingAdId() {
        if (SPUtils.getString(this@LoginActivity, SPConstants.KEY_ADID, "").isEmpty()) {
            checkJob = CoroutineScope(Dispatchers.Main).launch {
                while (isActive) {
                    val adId = SPUtils.getString(this@LoginActivity, SPConstants.KEY_ADID, "")
                    if (adId.isNotEmpty()) {
                        accountViewModel.checkAdId(adId)
                        break
                    }
                    delay(1000L) // 每隔 1 秒检查一次
                }
            }
        } else {
            accountViewModel.checkAdId(
                SPUtils.getString(
                    this@LoginActivity,
                    SPConstants.KEY_ADID,
                    ""
                )
            )
        }
    }

    override fun onRestart() {
        super.onRestart()
        startCheckingAdId()
    }

    override fun immersiveIntoStatusBar(): Boolean {
        return true
    }

    override fun isShowTitleBar(): Boolean {
        return false
    }

    override fun onDestroy() {
        super.onDestroy()
        checkJob?.cancel()
    }
}
