package com.fascin.chatter.account.newbie.navigation

import android.app.Activity
import android.content.Context
import com.fascin.chatter.bean.CourseListIntent
import com.fascin.chatter.main.profile.invite.BindInviteCodeDialog
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.routeAction
import androidx.fragment.app.FragmentActivity
import com.fascin.chatter.bean.BindModelIntent
import com.fascin.chatter.bean.NewAnchorEntity
import com.iandroid.allclass.lib_common.beans.WebIntent

/**
 * 任务导航管理器 - 管理任务间的导航逻辑
 * @date: 2025-07-31
 */
object TaskNavigationManager {
    
    private var navigationGraph: TaskNavigationGraph? = null

    private var entity: NewAnchorEntity? = null

    private val navigationListeners = mutableListOf<TaskNavigationListener>()

    fun setEntity(entity: NewAnchorEntity) {
        this.entity = entity
    }

    fun getEntity(): NewAnchorEntity? = entity

    /**
     * 设置导航图
     */
    fun setNavigationGraph(graph: TaskNavigationGraph) {
        if (entity == null) throw IllegalStateException("Entity is null, use setEntity() first")

        this.navigationGraph = graph
        notifyNavigationGraphChanged(graph)
    }

    /**
     * 获取当前导航图
     */
    fun getNavigationGraph(): TaskNavigationGraph? = navigationGraph

    /**
     * 添加导航监听器
     */
    fun addNavigationListener(listener: TaskNavigationListener) {
        if (!navigationListeners.contains(listener)) {
            navigationListeners.add(listener)
        }
    }

    /**
     * 移除导航监听器
     */
    fun removeNavigationListener(listener: TaskNavigationListener) {
        navigationListeners.remove(listener)
    }

    /**
     * 执行指定类型的任务
     */
    fun executeTask(context: Context, taskType: TaskType): Boolean {
        val graph = navigationGraph ?: return false
        
        // 检查任务是否可以执行
        if (!graph.canExecuteTask(taskType)) {
            notifyTaskExecutionFailed(taskType, "Task dependencies not met")
            return false
        }

        navigationGraph = graph.copy(currentTaskIndex = graph.tasks.indexOfFirst { it.taskType == taskType })

        val taskNode = graph.tasks.find { it.taskType == taskType } ?: return false
        
        return when (taskType) {
            TaskType.BIND_CODE -> executeBindCodeTask(context, taskNode)
            TaskType.COURSE -> executeCourseTask(context, taskNode)
            TaskType.BANK_ACCOUNT -> executeBankAccountTask(context, taskNode)
            TaskType.SELECT_MODEL -> executeSelectModelTask(context, taskNode)
            TaskType.SELECT_SHIFT -> executeSelectShiftTask(context, taskNode)
        }
    }

    fun executeTask(context: Context): Boolean {
        val graph = navigationGraph ?: return false
        val currentTask = graph.getCurrentTask() ?: return false

        return executeTask(context, currentTask.taskType)
    }

    /**
     * 执行下一个任务
     */
    fun executeNextTask(context: Context): Boolean {
        val graph = navigationGraph ?: return false
        val nextTask = graph.getNextTask() ?: return false
        return executeTask(context, nextTask.taskType)
    }

    /**
     * 更新任务状态
     */
    fun updateTaskStatus(taskType: TaskType, status: Int) {
        navigationGraph?.let { graph ->
            val updatedGraph = graph.updateTaskStatus(taskType, status)
            setNavigationGraph(updatedGraph)
            notifyTaskStatusChanged(taskType, status)
        }
    }

    /**
     * 获取任务进度
     */
    fun getTaskProgress(): TaskProgress? {
        return navigationGraph?.getProgress()
    }

    /**
     * 检查是否所有必需任务都已完成
     */
    fun areAllRequiredTasksCompleted(): Boolean {
        val graph = navigationGraph ?: return false
        return graph.tasks.filter { it.isRequired }.all { it.taskData.status == 1 }
    }

    /**
     * 清除导航图
     */
    fun clearNavigationGraph() {
        navigationGraph = null
        navigationListeners.clear()
    }

    // 私有方法 - 执行具体任务

    private fun executeBindCodeTask(context: Context, taskNode: TaskNode): Boolean {
        return try {
            context.routeAction(ActionType.actionTaskBindCode)
            notifyTaskExecutionStarted(TaskType.BIND_CODE)
            true
        } catch (e: Exception) {
            notifyTaskExecutionFailed(TaskType.BIND_CODE, e.message ?: "execute failed")
            false
        }
    }

    private fun executeCourseTask(context: Context, taskNode: TaskNode): Boolean {
        return try {
            if (context is Activity) {
                context.routeAction(ActionType.actionTaskCourseActive)
                notifyTaskExecutionStarted(TaskType.COURSE)
                true
            } else {
                false
            }
        } catch (e: Exception) {
            notifyTaskExecutionFailed(TaskType.COURSE, e.message ?: "execute failed")
            false
        }
    }

    private fun executeBankAccountTask(context: Context, taskNode: TaskNode): Boolean {
        return try {
            if (context is Activity) {
                context.routeAction(ActionType.actionTaskBankAccountActive)
                notifyTaskExecutionStarted(TaskType.BANK_ACCOUNT)
                true
            } else {
                false
            }
        } catch (e: Exception) {
            notifyTaskExecutionFailed(TaskType.BANK_ACCOUNT, e.message ?: "execute failed")
            false
        }
    }

    private fun executeSelectModelTask(context: Context, taskNode: TaskNode): Boolean {
        return try {
            if (context is Activity) {
                context.routeAction(ActionType.actionSelectModel) {
                    it.param = BindModelIntent().apply {
                        from = ActionType.actionNewAnchor // 从新手课程来
                    }
                }
                notifyTaskExecutionStarted(TaskType.SELECT_MODEL)
                true
            } else {
                false
            }
        } catch (e: Exception) {
            notifyTaskExecutionFailed(TaskType.SELECT_MODEL, e.message ?: "execute failed")
            false
        }
    }

    private fun executeSelectShiftTask(context: Context, taskNode: TaskNode): Boolean {
        return try {
            if (context is Activity) {
                context.routeAction(ActionType.actionAnchorShift)
                notifyTaskExecutionStarted(TaskType.SELECT_SHIFT)
                true
            } else {
                false
            }
        } catch (e: Exception) {
            notifyTaskExecutionFailed(TaskType.SELECT_SHIFT, e.message ?: "execute failed")
            false
        }
    }

    // 通知方法

    private fun notifyNavigationGraphChanged(graph: TaskNavigationGraph) {
        navigationListeners.forEach { it.onNavigationGraphChanged(graph) }
    }

    private fun notifyTaskExecutionStarted(taskType: TaskType) {
        navigationListeners.forEach { it.onTaskExecutionStarted(taskType) }
    }

    private fun notifyTaskExecutionFailed(taskType: TaskType, reason: String) {
        navigationListeners.forEach { it.onTaskExecutionFailed(taskType, reason) }
    }

    private fun notifyTaskStatusChanged(taskType: TaskType, status: Int) {
        navigationListeners.forEach { it.onTaskStatusChanged(taskType, status) }
    }
}

/**
 * 任务导航监听器接口
 */
interface TaskNavigationListener {
    /**
     * 导航图发生变化
     */
    fun onNavigationGraphChanged(graph: TaskNavigationGraph) {}

    /**
     * 任务开始执行
     */
    fun onTaskExecutionStarted(taskType: TaskType) {}

    /**
     * 任务执行失败
     */
    fun onTaskExecutionFailed(taskType: TaskType, reason: String) {}

    /**
     * 任务状态发生变化
     */
    fun onTaskStatusChanged(taskType: TaskType, status: Int) {}
}
