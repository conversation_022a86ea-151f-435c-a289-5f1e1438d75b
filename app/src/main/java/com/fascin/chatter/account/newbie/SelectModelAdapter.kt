package com.fascin.chatter.account.newbie

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.Adapter
import com.fascin.chatter.R
import com.fascin.chatter.bean.SelectModelEntity
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.GlideLoader.loadImage
import com.iandroid.allclass.lib_common.beans.UserEntity
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.routeActionByParam
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.setViewMargin
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.toPx
import kotlinx.android.synthetic.main.itemview_select_model.view.ivPortrait
import kotlinx.android.synthetic.main.itemview_select_model.view.ivSelected
import kotlinx.android.synthetic.main.itemview_select_model.view.markView
import kotlinx.android.synthetic.main.itemview_select_model.view.tvAge
import kotlinx.android.synthetic.main.itemview_select_model.view.tvNickName

/**
 * @Desc:新手引导model选择adapter
 * @Created: Quan
 * @Date: 2024/8/15
 */
class SelectModelAdapter : Adapter<SelectModelAdapter.ViewHolder>() {

    private val dataList = mutableListOf<SelectModelEntity>()
    private var selectedCount: Int = 0

    fun setData(data: List<SelectModelEntity>) {
        dataList.clear()
        dataList.addAll(data)
        notifyDataSetChanged()
    }

    fun setSelectedCount(count: Int) {
        selectedCount = count
    }

    /**
     * model选中
     */
    fun modelSelected(id: String) {
        dataList.forEachIndexed { index, item ->
            if (item.userId == id) {
                item.binded = 1
                notifyItemChanged(index)
                return
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutInflater.from(parent.context).inflate(
                R.layout.itemview_select_model,
                parent,
                false
            )
        )
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = dataList[position]
        holder.itemView.run {
            val margin = if (item.isBind()) 4.toPx else 0
            ivPortrait.setViewMargin(margin, margin, margin, margin)
            val radio = if (item.isBind()) 9f else 12f
            ivPortrait.setRadius(radio)
            markView.setRadius(radio)
            ivPortrait.loadImage(AppContext.context, item.avatarUrl)
            ivSelected.show(item.isBind())
            tvNickName.text = item.nickname
            tvAge.text = buildString {
                append(", ")
                append(item.age)
            }
            clickWithTrigger {
                if (!item.isBind()) {
                    context?.routeActionByParam<UserEntity>(ActionType.actionOtherProfile) {
                        it.userId = item.userId
                        // 跳转来源 -100:新手引导
                        it.from = -100
                        it.selectBind = item.isBind()
                    }
                }
            }
        }
    }

    override fun getItemCount(): Int {
        return dataList.size
    }

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)
}