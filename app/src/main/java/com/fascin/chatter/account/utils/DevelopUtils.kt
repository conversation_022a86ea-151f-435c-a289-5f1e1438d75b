package com.fascin.chatter.account.utils

import android.content.Intent
import androidx.appcompat.app.AppCompatActivity
import com.iandroid.allclass.lib_common.Values
import com.iandroid.allclass.lib_common.beans.OptionSelectEntity
import com.iandroid.allclass.lib_common.network.DomainProvider
import com.iandroid.allclass.lib_common.utils.ToastUtils
import com.iandroid.allclass.lib_common.views.OptionSelectDialog
import io.reactivex.Observable
import io.reactivex.android.schedulers.AndroidSchedulers
import java.util.concurrent.TimeUnit

class DevelopUtils {

    companion object {
        fun showEnvSwitchMenulist(activity: AppCompatActivity) {
            if (Values.api_env == Values.env_product) return
            var curEnv = DomainProvider.getEnv()
            var envList = arrayListOf(
                OptionSelectEntity(if (curEnv == Values.env_dev) "开发环境【当前正在使用】" else "开发环境") {
                    swtichEnv(activity, Values.env_dev)
                },
                OptionSelectEntity(if (curEnv == Values.env_test) "测试环境【当前正在使用】" else "测试环境") {
                    swtichEnv(activity, Values.env_test)
                },
                OptionSelectEntity(if (curEnv == Values.env_product) "生产环境【当前正在使用】" else "生产环境") {
                    swtichEnv(activity, Values.env_product)
                })
            val optionSelectDialog =
                OptionSelectDialog.Builder()
                    .setOptionList(envList)
                    .create()
            optionSelectDialog?.show(
                activity.supportFragmentManager,
                OptionSelectDialog::class.java.name
            )
        }

        private fun swtichEnv(activity: AppCompatActivity, toEnv: Int) {
            if (toEnv == DomainProvider.getEnv()) return
            // restart
            DomainProvider.switchEnv(toEnv)
            ToastUtils.showToast("restart app...")

            Observable.just(1)
                .delay(3, TimeUnit.SECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe {
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.CUPCAKE) {
                        activity.packageManager.getLaunchIntentForPackage(activity.packageName)
                            ?.run {
                                this.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                                activity.startActivity(this)
                                Runtime.getRuntime().exit(0)
                            }
                    }
                }
        }
    }
}