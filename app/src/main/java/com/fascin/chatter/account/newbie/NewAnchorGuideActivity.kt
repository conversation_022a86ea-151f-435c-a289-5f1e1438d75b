package com.fascin.chatter.account.newbie

import android.os.Bundle
import android.os.CountDownTimer
import android.view.KeyEvent
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.R
import com.fascin.chatter.account.AccountViewModel
import com.fascin.chatter.bean.CourseListIntent
import com.fascin.chatter.bean.CustomerServiceIntent
import com.fascin.chatter.bean.NewAnchorEntity
import com.fascin.chatter.config.Config
import com.fascin.chatter.dialog.NewAnchorFinishTaskDialog
import com.fascin.chatter.main.MainActivity
import com.fascin.chatter.main.profile.invite.BindInviteCodeDialog
import com.fascin.chatter.repository.AppRepository
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.AppController
import com.iandroid.allclass.lib_common.UserController
import com.iandroid.allclass.lib_common.base.FasBaseActivity
import com.iandroid.allclass.lib_common.beans.WebIntent
import com.iandroid.allclass.lib_common.event.UINewAnchorTaskStatusEvent
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.bean.ActionEntity
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.SPConstants
import com.iandroid.allclass.lib_common.utils.SPUtils
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.views.CommonCloseVerticalDialog
import kotlinx.android.synthetic.main.activity_new_anchor_guide.btnAccountTaskStatus
import kotlinx.android.synthetic.main.activity_new_anchor_guide.btnFinishTask
import kotlinx.android.synthetic.main.activity_new_anchor_guide.btnModeTaskStatus
import kotlinx.android.synthetic.main.activity_new_anchor_guide.btnQuizTaskStatus
import kotlinx.android.synthetic.main.activity_new_anchor_guide.ivNewAnchorQa
import kotlinx.android.synthetic.main.activity_new_anchor_guide.marquee
import kotlinx.android.synthetic.main.activity_new_anchor_guide.tvAccountMoney
import kotlinx.android.synthetic.main.activity_new_anchor_guide.tvModeMoney
import kotlinx.android.synthetic.main.activity_new_anchor_guide.tvQuizMoney
import kotlinx.android.synthetic.main.activity_new_anchor_guide.tvTaskCountDown
import kotlinx.android.synthetic.main.activity_new_anchor_guide.tvBindCode
import kotlinx.android.synthetic.main.activity_new_anchor_guide.btnBindCode
import kotlinx.android.synthetic.main.activity_new_anchor_guide.llBindCode
import kotlinx.android.synthetic.main.activity_new_anchor_guide.tvScheduling
import kotlinx.android.synthetic.main.activity_new_anchor_guide.btnScheduling
import java.util.concurrent.TimeUnit
import com.iandroid.allclass.lib_common.utils.exts.show


/**
 *  @author: LXL
 *  @description: 新主播引导页
 *  @date: 2024/8/15 13:43
 */
class NewAnchorGuideActivity : FasBaseActivity() {
    lateinit var viewModel: AccountViewModel
    private var countDownTimer: CountDownTimer? = null
    private var isFirstMarquee = true
    private var isCountDowning = true  //是否在倒计时

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_new_anchor_guide)
    }

    override fun initBaseContent() {
        super.initBaseContent()
        viewModel = ViewModelProvider(this, AccountViewModel.ViewModeFactory())[AccountViewModel::class.java]
        viewModel.newAnchorGuide()
        viewModel.newAnchorResult.observe(this) {
            initMarquee(it.marquee)
            setTaskRewardStatus(it)
            setTaskStatus(it)
        }
        viewModel.newAnchorError.observe(this) {
            viewModel.newAnchorGuide()
        }
        AppRepository.statusChange(Config.statusNoActive)
        ivNewAnchorQa.clickWithTrigger {
            routeAction(ActionType.actionOnlineSupport) {
                it.param = CustomerServiceIntent().also { intent ->
                    intent.title = getString(R.string.customer_service_qa)
                    intent.accountId = AppController.getQAAccount()
                }
            }
        }

        //刷新接口更新任务状态
        viewModel.compositeDisposable?.add(
            SimpleRxBus.observe(UINewAnchorTaskStatusEvent::class) {
                viewModel.newAnchorGuide()
            }
        )
    }

    /**
     * 跑马灯
     */
    private fun initMarquee(data: MutableList<String>) {
        //TODO 接口返回null，空指针异常 modify by mask
        if(data == null){
            isFirstMarquee = false
            AppContext.finishActivity(MainActivity::class.java)
            return
        }
        if (isFirstMarquee) {
            val mAdapter = MarqueeViewAdapter(data, this)
            marquee.setAdapter(mAdapter)
            isFirstMarquee = false
            AppContext.finishActivity(MainActivity::class.java)
        }
    }

    /**
     * 任务奖励状态
     */
    private fun setTaskRewardStatus(reward: NewAnchorEntity) {
        val tasks = listOfNotNull(
            reward.courseTask,
            reward.bankAccountTask,
            reward.selectModelTask,
            //TODO 关闭排班 by mask
//            reward.selectShiftTask,
            reward.bindCodeTask, //邀请码填写
        )

        val taskFinishCount = tasks.count { it.status == 1 }

        if (isCountDowning) {
            val totalTime = reward.expireDuration * 1000L //倒计时
            countDownTimer = object : CountDownTimer(totalTime, 1000) { // 每秒更新一次
                override fun onTick(millisUntilFinished: Long) {
                    val days = TimeUnit.MILLISECONDS.toDays(millisUntilFinished)
                    val hours = TimeUnit.MILLISECONDS.toHours(millisUntilFinished) % 24
                    val minutes = TimeUnit.MILLISECONDS.toMinutes(millisUntilFinished) % 60
                    val seconds = TimeUnit.MILLISECONDS.toSeconds(millisUntilFinished) % 60

                    val timeLeftFormatted = String.format("%dd:%dh:%02dm:%02ds", days, hours, minutes, seconds)
                    tvTaskCountDown.text = "Remaining time: $timeLeftFormatted"
                }

                override fun onFinish() {
                    tvTaskCountDown.text = "Remaining time: 0d:0h:0m:0s"
                    if (taskFinishCount < 3) {
                        UserController.tickOffline()
                    }
                }
            }.start()
            isCountDowning = false
        }

        if (taskFinishCount == tasks.size) {
            btnFinishTask.isEnabled = true
            btnFinishTask.text = getString(R.string.new_anchor_activate)
            btnFinishTask.clickWithTrigger {
                viewModel.newReadCongrats {
                    if (it.actived == 0) {
                        CommonCloseVerticalDialog.Builder()
                            .showCloseBtn(true)
                            .setTitle("Try Again Tomorrow")
                            .setContext("Too many accounts are being created today. Continuing may affect your experience. Please try again tomorrow.")
                            .setConfirm("Ok, I got it") {
                            }
                            .create().show(supportFragmentManager, CommonCloseVerticalDialog::class.java.name)
                    } else {
                        NewAnchorFinishTaskDialog.showNewAnchorFinishTaskDialog(reward.totalReward) {
                            SPUtils.put(this, UserController.attachAccount(SPConstants.KEY_IS_NEW_CHATTER), false)
                            routeAction(ActionType.actionTypeMain)
                            finish()
                        }
                    }
                }
            }
        }
    }

    /**
     * 任务完成状态
     */
    private fun setTaskStatus(reward: NewAnchorEntity) {
        tvQuizMoney.text = reward.courseTask.title
        tvAccountMoney.text = reward.bankAccountTask.title
        tvModeMoney.text = reward.selectModelTask.title
        //TODO close 排班 by mask
        tvScheduling.text = reward.selectShiftTask?.title
        tvBindCode.text = reward.bindCodeTask?.title

        btnQuizTaskStatus.isEnabled = reward.courseTask.status == 0
        btnQuizTaskStatus.text =
            if (reward.courseTask.status == 0) getString(R.string.new_anchor_pending) else getString(R.string.new_anchor_completed)

        btnAccountTaskStatus.isEnabled = reward.bankAccountTask.status == 0
        btnAccountTaskStatus.text =
            if (reward.bankAccountTask.status == 0) getString(R.string.new_anchor_pending) else getString(R.string.new_anchor_completed)

        btnModeTaskStatus.isEnabled = reward.selectModelTask.status == 0
        btnModeTaskStatus.text =
            if (reward.selectModelTask.status == 0) getString(R.string.new_anchor_pending) else getString(R.string.new_anchor_completed)

        //TODO 关闭排班 by mask
        btnScheduling.isEnabled = reward.selectShiftTask?.status == 0
        btnScheduling.text =
            if (reward.selectShiftTask?.status == 0) getString(R.string.new_anchor_pending) else getString(R.string.new_anchor_completed)

        //填写邀请码
        llBindCode.show(AppController.isShowInviteEntry() == 1)
        btnBindCode.isEnabled = reward.bindCodeTask?.status == 0
        btnBindCode.text =
            if (reward.bindCodeTask?.status == 0) getString(R.string.new_anchor_pending) else getString(R.string.new_anchor_completed)

        //回答Quiz
        btnQuizTaskStatus.clickWithTrigger {
            routeAction(ActionEntity().apply {
                id = ActionType.actionCourseList
                param = CourseListIntent().apply {
                    id = reward.courseTask.courseId
                    name = reward.courseTask.courseName
                }
            })
        }
        //填写收款账号
        btnAccountTaskStatus.clickWithTrigger {
            routeAction(ActionType.actionTypeToWebActivity) {
                it.param = WebIntent().also { webIntent ->
                    webIntent.showTitle = true
                    webIntent.url = reward.bankAccountTask.payrollUrl
                    webIntent.pageFrom = ActionType.actionPayrollAccount
                }
            }
        }
        //选择Mode
        btnModeTaskStatus.clickWithTrigger {
            routeAction(ActionType.actionSelectModel)
        }

        //TODO 关闭排班 by mask
        //选择排班
        btnScheduling.clickWithTrigger {
            routeAction(ActionType.actionAnchorShift)
        }

        //填写邀请码
        btnBindCode.clickWithTrigger {
            BindInviteCodeDialog
                .newInstance(from = BindInviteCodeDialog.FROM_TASK)
                .show(supportFragmentManager, BindInviteCodeDialog::class.java.name)
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        return if (keyCode == KeyEvent.KEYCODE_BACK) {
            true
        } else {
            super.onKeyDown(keyCode, event)
        }
    }

    override fun immersiveIntoStatusBar(): Boolean {
        return true
    }

    override fun isShowTitleBar(): Boolean {
        return false
    }

    override fun onDestroy() {
        super.onDestroy()
        countDownTimer?.cancel()
    }
}