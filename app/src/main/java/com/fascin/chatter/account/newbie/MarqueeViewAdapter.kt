package com.fascin.chatter.account.newbie

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.text.HtmlCompat
import com.fascin.chatter.R
import com.iandroid.allclass.lib_basecore.view.marqueeview.XMarqueeView
import com.iandroid.allclass.lib_basecore.view.marqueeview.XMarqueeViewAdapter

/**
 *  @author: LXL
 *  @description: description
 *  @date: 2024/8/15 20:18
 */
class MarqueeViewAdapter(datas: List<String>, private val context: Context) : XMarqueeViewAdapter<String>(datas) {

    override fun onCreateView(parent: XMarqueeView): View {
        // 跑马灯单个显示条目布局，自定义
        return LayoutInflater.from(parent.context).inflate(R.layout.item_marqueeview, null)
    }

    override fun onBindView(parent: View, view: View, position: Int) {
        // 布局内容填充
        val tvOne = view.findViewById<AppCompatTextView>(R.id.marquee_tv_one)
        tvOne.text = HtmlCompat.fromHtml(mDatas[position], HtmlCompat.FROM_HTML_MODE_LEGACY)
    }
}
