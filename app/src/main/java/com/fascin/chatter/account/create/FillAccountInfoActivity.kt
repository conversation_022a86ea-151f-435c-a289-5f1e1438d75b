package com.fascin.chatter.account.create

import android.content.pm.PackageManager
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.R
import com.fascin.chatter.account.AccountViewModel
import com.fascin.chatter.bean.CheckCodeEntity
import com.fascin.chatter.component.views.SUButtonStatus
import com.fascin.chatter.isValidPasswordLength
import com.fascin.chatter.loginReturn
import com.fascin.chatter.utils.GoogleLocationUtils
import com.fascin.chatter.utils.PermissionConst
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.GlideLoader.loadImage
import com.iandroid.allclass.lib_common.base.ChatterBaseActivity
import com.iandroid.allclass.lib_common.event.EventKey
import com.iandroid.allclass.lib_common.repository.CommonRepository
import com.iandroid.allclass.lib_common.utils.SPConstants
import com.iandroid.allclass.lib_common.utils.SPUtils
import com.iandroid.allclass.lib_common.utils.ToastUtils
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.keyboard.KeyboardUtils
import kotlinx.android.synthetic.main.activity_fill_account_info.btnNext
import kotlinx.android.synthetic.main.activity_fill_account_info.createAccountConfirmPwd
import kotlinx.android.synthetic.main.activity_fill_account_info.createAccountID
import kotlinx.android.synthetic.main.activity_fill_account_info.createAccountPwd
import kotlinx.android.synthetic.main.activity_fill_account_info.etEnterTrainingPw
import kotlinx.android.synthetic.main.activity_fill_account_info.etEnterUserName
import kotlinx.android.synthetic.main.activity_fill_account_info.indicatorOne
import kotlinx.android.synthetic.main.activity_fill_account_info.indicatorTwo
import kotlinx.android.synthetic.main.activity_fill_account_info.ivStep
import kotlinx.android.synthetic.main.activity_fill_account_info.llPassword
import kotlinx.android.synthetic.main.activity_fill_account_info.scrollView
import kotlinx.android.synthetic.main.activity_fill_account_info.tvLocation
import kotlinx.android.synthetic.main.activity_fill_account_info.tvTelegram
import kotlinx.android.synthetic.main.activity_fill_account_info.tvTitle

class FillAccountInfoActivity : ChatterBaseActivity() {

    private var viewModel: AccountViewModel? = null
    private var isReqPermission = true  //是否可以请求权限
    private var isPermanentGranted = false
    private var codeEntity: CheckCodeEntity? = null
    private var tmpCode: String? = ""
    private var step: Int = 1
    private var isPwdValid: Boolean = false
    private var isConfirmPwdValid: Boolean = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_fill_account_info)
    }

    override fun initBaseContent() {
        super.initBaseContent()
        initViewModel()
        initView()
        setListener()
        eventTrace()
    }

    private fun initViewModel() {
        viewModel = ViewModelProvider(this).get(AccountViewModel::class.java)
        viewModel?.locationResult?.observe(this) {
            isReqPermission = false
            tvLocation.text = buildString {
                append("1. Your location: ")
                append(it.country)
            }
            tmpCode = it.tmp_code
            tvTelegram.text = String.format(getString(R.string.text_training_name_desc), it.socialChannel)
            ivStep.loadImage(AppContext.context, it.smUrl, isCenterCrop = false)
            Log.e("tmp_code", "tmp_code: ${it.tmp_code}")
            checkMainBtnEnable()
        }

        viewModel?.checkCodeResult?.observe(this) { entity ->
            setBtnDisabled()
            entity?.let {
                codeEntity = it
                if (it.cid.isNotEmpty()) {
                    showSetPsw()
                }
            }
        }
        viewModel?.checkCodeError?.observe(this) {
            ToastUtils.showToast(it)
            btnNext.setButtonStatus(SUButtonStatus.Activated)
        }

        viewModel?.checkRegResult?.observe(this) {
            loginReturn(true)
        }

        viewModel?.checkRegError?.observe(this) {
            btnNext.setButtonStatus(SUButtonStatus.Activated)
        }
    }

    private fun initView() {
        setBtnDisabled()
        btnNext.setText(getString(R.string.next))
        tvLocation.text = buildString {
            append("1. Your location: ")
        }
        GoogleLocationUtils.checkLocationPermission(this) { latitude, longitude ->
            viewModel?.getLocation(latitude, longitude)
        }

        createAccountPwd.setHintEnabled(false)
        createAccountPwd.setPasswordToggle(true, R.drawable.password_toggle_drawable)
        createAccountPwd.afterTextChangeBlock = {
            val password = createAccountPwd.getText()
            isPwdValid = password.isValidPasswordLength()
            if (isPwdValid) {
                createAccountPwd.errorHintMsg(null)
                isPwdValid = true
            } else {
                createAccountPwd.errorHintMsg(getString(R.string.reg_create_password_error))
            }
            checkMainBtnEnable()
        }

        createAccountConfirmPwd.setHintEnabled(false)
        createAccountConfirmPwd.setPasswordToggle(true, R.drawable.password_toggle_drawable)
        createAccountConfirmPwd.afterTextChangeBlock = {
            val password = createAccountConfirmPwd.getText()
            isConfirmPwdValid = password.isValidPasswordLength()
            if (isConfirmPwdValid && password == createAccountPwd.getText()) {
                createAccountConfirmPwd.errorHintMsg(null)
                isConfirmPwdValid = true
            } else if (isConfirmPwdValid && password != createAccountPwd.getText()) {
                createAccountConfirmPwd.errorHintMsg(getString(R.string.reg_password_confirm_error))
            } else {
                createAccountConfirmPwd.errorHintMsg(getString(R.string.reg_create_password_error))
            }
            checkMainBtnEnable()
        }
    }

    private fun setListener() {
        tvLocation.clickWithTrigger {
            if (isReqPermission) {
                GoogleLocationUtils.checkLocationPermission(this) { latitude, longitude ->
                    viewModel?.getLocation(latitude, longitude)
                }
            }
        }

        //下一步
        btnNext.clickWithTrigger {
            if (step == 1) {
                KeyboardUtils.hideKeyboard(this)
                btnNext.setButtonStatus(SUButtonStatus.Loading)
                viewModel?.regCheckCode(
                    tmpCode.orEmpty(),
                    etEnterTrainingPw.text?.trim().toString(),
                    etEnterUserName.text?.trim().toString()
                )
            } else {
                btnNext.setButtonStatus(SUButtonStatus.Loading)
                viewModel?.finishRegSubmit(
                    codeEntity?.tmpCode ?: "",
                    createAccountPwd.getText().trim()
                )
            }
        }

        etEnterTrainingPw.addTextChangedListener(
            object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

                }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                    val isValid = !s.isNullOrEmpty() && s.isNotEmpty()
                    val isValidName = etEnterUserName.text.toString().isNotEmpty()
                    if (isValid && isValidName && tmpCode?.isNotEmpty() == true) {
                        btnNext.setButtonStatus(SUButtonStatus.Activated)
                    } else {
                        setBtnDisabled()
                    }
                }

                override fun afterTextChanged(s: Editable?) {

                }
            })

        etEnterUserName.addTextChangedListener(
            object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

                }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {

                }

                override fun afterTextChanged(s: Editable?) {
                    val isValidPw = etEnterTrainingPw.text.toString().isNotEmpty()
                    val isValid = !s.isNullOrEmpty() && s.isNotEmpty()
                    if (isValidPw && isValid && tmpCode?.isNotEmpty() == true) {
                        btnNext.setButtonStatus(SUButtonStatus.Activated)
                    } else {
                        setBtnDisabled()
                    }
                }
            })
    }

    private fun showSetPsw() {
        codeEntity?.let {
            scrollView.show(false)
            llPassword.show(true)
            btnNext.setText(getString(R.string.login_email_sign_up))
            createAccountID.text = it.cid
            indicatorOne.setBackgroundResource(com.iandroid.allclass.lib_common.R.color.color_d9d9d9)
            indicatorTwo.setBackgroundResource(com.iandroid.allclass.lib_basecore.R.color.cl_9370DB)
            tvTitle.text = getString(R.string.text_fill_title_password)
            step = 2
            CommonRepository.eventTrace(EventKey.sign_up_password) {
                "uid" to it.cid
            }
        }
    }

    private fun checkMainBtnEnable() {
        val isEnabled =
            isPwdValid && isConfirmPwdValid && createAccountPwd.getText() == createAccountConfirmPwd.getText()
        if (isEnabled) {
            btnNext.setButtonStatus(SUButtonStatus.Activated)
        } else {
            setBtnDisabled()
        }
    }

    private fun setBtnDisabled() {
        btnNext.setButtonStatus(
            SUButtonStatus.Disabled,
            disabledBg = R.drawable.bg_progress_button_login_disable,
            disabledColor = "#FFFFFF"
        )
    }

    private fun eventTrace() {
        val curAdId = SPUtils.getString(this, SPConstants.KEY_ADID, "")
        if (curAdId.isNotEmpty()) {
            CommonRepository.eventTrace(EventKey.sign_up_info) {
                "adid" to curAdId
            }
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int, permissions: Array<out String>, grantResults: IntArray,
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == PermissionConst.REQUEST_CODE_LOCATION) {
            for (result in grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    if (isPermanentGranted) {
                        GoogleLocationUtils.needJumpSetting(this)
                    }
                    isPermanentGranted = true
                    return
                }
            }
            GoogleLocationUtils.userEnableLocation(true)
        }
    }
}