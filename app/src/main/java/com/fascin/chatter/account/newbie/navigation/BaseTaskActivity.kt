package com.fascin.chatter.account.newbie.navigation

import android.os.Bundle
import android.view.View
import android.widget.TextView
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import com.fascin.chatter.R
import com.fascin.chatter.account.AccountViewModel
import com.fascin.chatter.dialog.NewAnchorFinishTaskDialog
import com.iandroid.allclass.lib_common.UserController
import com.iandroid.allclass.lib_common.base.FasBaseActivity
import com.iandroid.allclass.lib_common.event.EventProfileUpdate
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.SPConstants
import com.iandroid.allclass.lib_common.utils.SPUtils
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.views.CommonCloseVerticalDialog
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 任务Activity基类 - 提供导航功能的基础实现
 * @date: 2025-07-31
 */
abstract class BaseTaskActivity : FasBaseActivity(), TaskNavigationListener {

    protected val accountViewModel: AccountViewModel by viewModels()

    protected var currentTaskType: TaskType? = null
    private var navigationGraph: TaskNavigationGraph? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 注册导航监听器
        TaskNavigationManager.addNavigationListener(this)
        
        // 获取当前导航图
        navigationGraph = TaskNavigationManager.getNavigationGraph()
        
        // 确定当前任务类型
        currentTaskType = determineCurrentTaskType()
    }

    override fun onDestroy() {
        super.onDestroy()
        // 移除导航监听器
        TaskNavigationManager.removeNavigationListener(this)
    }

    /**
     * 子类需要实现此方法来确定当前任务类型
     */
    protected abstract fun determineCurrentTaskType(): TaskType?

    /**
     * 任务完成时调用此方法
     */
    protected fun onTaskCompleted() {
        currentTaskType?.let { taskType ->
            TaskNavigationManager.updateTaskStatus(taskType, 1)
            
            // 检查是否有下一个任务
            val graph = TaskNavigationManager.getNavigationGraph()
            val nextTask = graph?.getNextTask()
            
            if (nextTask != null) {
                showNextTaskOption(nextTask)
            } else {
                onAllTasksCompleted()
            }
        }
    }

    /**
     * 导航到下一个任务
     */
    protected fun navigateToNextTask() {
        TaskNavigationManager.executeNextTask(this)
    }

    /**
     * 导航到指定任务
     */
    protected fun navigateToTask(taskType: TaskType) {
        TaskNavigationManager.executeTask(this, taskType)
    }

    /**
     * 获取当前任务进度
     */
    protected fun getCurrentProgress(): TaskProgress? {
        return TaskNavigationManager.getTaskProgress()
    }

    /**
     * 检查当前任务是否可以跳过
     */
    protected fun canSkipCurrentTask(): Boolean {
        currentTaskType?.let { taskType ->
            val graph = TaskNavigationManager.getNavigationGraph()
            return graph?.tasks?.find { it.taskType == taskType }?.isRequired == false
        }
        return false
    }

    /**
     * 显示下一个任务选项
     */
    protected fun showNextTaskOption(nextTask: TaskNode) {
        // 子类可以重写此方法来自定义下一个任务的提示UI
        navigateToTask(nextTask.taskType)
        finish()
    }

    /**
     * 所有任务完成时的处理
     */
    protected open fun onAllTasksCompleted() {
        val entity = TaskNavigationManager.getEntity() ?: return

        // 子类可以重写此方法来处理所有任务完成的情况
        accountViewModel.newReadCongrats {
            if (it.actived == 0) {
                CommonCloseVerticalDialog.Builder()
                    .showCloseBtn(true)
                    .setTitle("Try Again Tomorrow")
                    .setContext("Too many accounts are being created today. Continuing may affect your experience. Please try again tomorrow.")
                    .setConfirm("Ok, I got it") {
                        SimpleRxBus.post(EventProfileUpdate())
                        routeAction(ActionType.actionTypeMain)
                        finish()
                    }
                    .create().show(supportFragmentManager, CommonCloseVerticalDialog::class.java.name)
            } else {
                NewAnchorFinishTaskDialog.showNewAnchorFinishTaskDialog(entity.totalReward) {
                    SPUtils.put(this, UserController.attachAccount(SPConstants.KEY_IS_NEW_CHATTER), false)
                    SimpleRxBus.post(EventProfileUpdate())
                    routeAction(ActionType.actionTypeMain)
                    finish()
                }
            }
        }
    }

    // TaskNavigationListener 实现

    override fun onNavigationGraphChanged(graph: TaskNavigationGraph) {
        navigationGraph = graph
    }

    override fun onTaskExecutionStarted(taskType: TaskType) {
        // 子类可以重写此方法来处理任务开始执行的情况
    }

    override fun onTaskExecutionFailed(taskType: TaskType, reason: String) {
        // 子类可以重写此方法来处理任务执行失败的情况
    }

    override fun onTaskStatusChanged(taskType: TaskType, status: Int) {
        // 子类可以重写此方法来处理任务执行失败的情况
    }
}
