package com.fascin.chatter.account.newbie

import android.os.Bundle
import android.view.KeyEvent
import androidx.activity.viewModels
import com.fascin.chatter.AppModule
import com.fascin.chatter.account.newbie.navigation.BaseTaskActivity
import com.fascin.chatter.account.newbie.navigation.TaskNavigationManager
import com.fascin.chatter.account.newbie.navigation.TaskType
import com.fascin.chatter.bean.CourseEntity
import com.fascin.chatter.databinding.ActivityCourseActiveBinding
import com.fascin.chatter.main.course.CourseViewModel
import com.iandroid.allclass.lib_common.beans.WebIntent
import com.iandroid.allclass.lib_common.event.CourseCategoryPassEvent
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.ToastUtils
import com.iandroid.allclass.lib_common.web.WebFragmentV2
import io.reactivex.disposables.CompositeDisposable

class CourseActiveActivity : BaseTaskActivity() {

    private lateinit var binding: ActivityCourseActiveBinding
    private val viewModel: CourseViewModel by viewModels()
    private lateinit var fragment: WebFragmentV2

    private var compositeDisposable: CompositeDisposable? = null

    override fun determineCurrentTaskType(): TaskType? = TaskType.COURSE

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCourseActiveBinding.inflate(layoutInflater)
        setContentView(binding.root)

        compositeDisposable = CompositeDisposable()

        initData()
    }

    private fun initData() {
        val id = TaskNavigationManager.getEntity()?.courseTask?.courseId ?: 0 // 课程id

        viewModel.coursesResult.observe(this) { courses ->
            val course = courses.firstOrNull()?.courses?.first()
            if (course == null) {
                ToastUtils.showToast("Course content url error")
                return@observe
            }

            updateCourseWebview(course)
        }

        compositeDisposable?.add(SimpleRxBus.observe(CourseCategoryPassEvent::class) {
            // 课程答题成功了，直接进行下一步
            onTaskCompleted()
        })

        viewModel.getCourses(
            lastId = 0, //默认为0
            categoryId = id
        )
    }

    private fun updateCourseWebview(course: CourseEntity) {
        AppModule.userActive()
        fragment = WebFragmentV2.newInstance(
            WebIntent().also { webIntent ->
                webIntent.showTitle = false //不显示标题
                webIntent.url = course.h5Url
                webIntent.actionParam = course
                if (course.hasPaper == 1) {
                    // 有试卷时，web页显示答题按钮
                    webIntent.pageFrom = ActionType.actionCourseList
                }
            }
        )

        supportFragmentManager.beginTransaction()
            .replace(binding.fragmentContainerView.id, fragment)
            .commit()
    }

    override fun onDestroy() {
        super.onDestroy()
        compositeDisposable?.clear()
        compositeDisposable?.dispose()
        compositeDisposable = null
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        return if (keyCode == KeyEvent.KEYCODE_BACK) {
            true
        } else {
            super.onKeyDown(keyCode, event)
        }
    }

    override fun immersiveIntoStatusBar(): Boolean {
        return true
    }

    override fun isShowTitleBar(): Boolean {
        return false
    }
}