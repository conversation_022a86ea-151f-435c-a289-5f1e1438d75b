package com.fascin.chatter.account.newbie

import android.os.Bundle
import android.view.KeyEvent
import com.fascin.chatter.account.newbie.navigation.BaseTaskActivity
import com.fascin.chatter.account.newbie.navigation.TaskNavigationManager
import com.fascin.chatter.account.newbie.navigation.TaskType
import com.fascin.chatter.databinding.ActivityPaymentAccountActiveBinding
import com.iandroid.allclass.lib_common.beans.WebIntent
import com.iandroid.allclass.lib_common.event.PaymentAccountSavedEvent
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.web.WebFragmentV2
import io.reactivex.disposables.CompositeDisposable

class PaymentAccountActiveActivity : BaseTaskActivity() {

    private lateinit var binding: ActivityPaymentAccountActiveBinding
    private var compositeDisposable: CompositeDisposable? = null

    override fun determineCurrentTaskType(): TaskType? = TaskType.BANK_ACCOUNT

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPaymentAccountActiveBinding.inflate(layoutInflater)
        setContentView(binding.root)
        compositeDisposable = CompositeDisposable()

        initView()
        initData()
    }

    private fun initView() {
        val data = TaskNavigationManager.getEntity()?.bankAccountTask
        val fragment = WebFragmentV2.newInstance(
            WebIntent().also { webIntent ->
                webIntent.showTitle = false //不显示标题
                webIntent.url = data?.payrollUrl
                webIntent.pageFrom = ActionType.actionPayrollAccount
            }
        )

        supportFragmentManager.beginTransaction()
            .replace(binding.fragmentContainerView.id, fragment)
            .commit()
    }

    private fun initData() {
        compositeDisposable?.add(SimpleRxBus.observe(PaymentAccountSavedEvent::class) {
            onTaskCompleted()
        })
    }

    override fun onDestroy() {
        super.onDestroy()
        compositeDisposable?.clear()
        compositeDisposable?.dispose()
        compositeDisposable = null
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        return if (keyCode == KeyEvent.KEYCODE_BACK) {
            true
        } else {
            super.onKeyDown(keyCode, event)
        }
    }

    override fun immersiveIntoStatusBar(): Boolean {
        return true
    }

    override fun isShowTitleBar(): Boolean {
        return false
    }
}