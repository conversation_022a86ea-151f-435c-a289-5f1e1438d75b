package com.fascin.chatter.account.newbie

import android.os.Bundle
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.account.AccountViewModel
import com.fascin.chatter.bean.CourseListIntent
import com.fascin.chatter.bean.LessonEntity
import com.fascin.chatter.bean.NewbieCenterEntity
import com.fascin.chatter.bean.QAItemEntity
import com.fascin.chatter.main.adapter.TrialGoalsAdapter
import com.iandroid.allclass.lib_common.UserController
import com.iandroid.allclass.lib_common.base.FasBaseActivity
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.bean.ActionEntity
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.textSpanValue
import kotlinx.android.synthetic.main.activity_newbie_center.errorStatusView
import kotlinx.android.synthetic.main.activity_newbie_center.flQA
import kotlinx.android.synthetic.main.activity_newbie_center.goalsRv
import kotlinx.android.synthetic.main.activity_newbie_center.lessonRv
import kotlinx.android.synthetic.main.activity_newbie_center.llGuide
import kotlinx.android.synthetic.main.activity_newbie_center.llLesson
import kotlinx.android.synthetic.main.activity_newbie_center.llTrialGoals
import kotlinx.android.synthetic.main.activity_newbie_center.preView
import kotlinx.android.synthetic.main.activity_newbie_center.retryButton
import kotlinx.android.synthetic.main.activity_newbie_center.rvQA
import kotlinx.android.synthetic.main.activity_newbie_center.titleBack
import kotlinx.android.synthetic.main.activity_newbie_center.tvDayDesc
import kotlinx.android.synthetic.main.activity_newbie_center.tvGoalsStatus
import kotlinx.android.synthetic.main.activity_newbie_center.tvLessonStatus
import kotlinx.android.synthetic.main.activity_newbie_center.tvQA
import kotlinx.android.synthetic.main.activity_newbie_center.tvRetry
import kotlinx.android.synthetic.main.activity_newbie_center.tvRetryLoading
import kotlinx.android.synthetic.main.activity_newbie_center.tvTrialJob
import kotlinx.android.synthetic.main.activity_newbie_center.tvUid

/**
 * @Desc: 新主播收口页面
 * @Created: Quan
 * @Date: 2024/12/30
 */
class NewbieCenterActivity : FasBaseActivity() {

    private var viewModel: AccountViewModel? = null
    private var goalsAdapter: TrialGoalsAdapter? = null
    private var lessonAdapter: TrialGoalsAdapter? = null
    private var qaAdapter: NCQAAdapter? = null
    private var entity: NewbieCenterEntity? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_newbie_center)
        initViewData()
        setListener()
    }

    private fun initViewData() {
        preView.show(true)
        tvUid.text = buildString {
            append("Hi,")
            append(UserController.getUserId())
        }

        goalsAdapter = TrialGoalsAdapter()
        goalsRv.layoutManager = LinearLayoutManager(this)
        goalsRv.adapter = goalsAdapter

        lessonAdapter = TrialGoalsAdapter()
        lessonRv.layoutManager = LinearLayoutManager(this)
        lessonRv.adapter = lessonAdapter

        qaAdapter = NCQAAdapter()
        rvQA.layoutManager = LinearLayoutManager(this)
        rvQA.adapter = qaAdapter

        viewModel = ViewModelProvider(this)[AccountViewModel::class.java]
        viewModel?.getNewbieCenterData()
    }

    private fun setListener() {
        titleBack.clickWithTrigger {
            finish()
        }

        viewModel?.newbieCenterResult?.observe(this) {
            entity = it
            setGoals(it)
            setLesson(it.lesson)
            setQA(it.qaList)
            setLoadingUI(false)
            preView.show(false)
            errorStatusView.show(false)
        }

        viewModel?.newbieCenterError?.observe(this) {
            setLoadingUI(false)
            errorStatusView.show(true)
        }

        //TODO close mpc by mask
//        llTrialGoals.clickWithTrigger {
//            routeAction(ActionType.actionMPC)
//        }

        llLesson.clickWithTrigger {
            entity?.lesson?.let {
                routeAction(ActionEntity().apply {
                    id = ActionType.actionCourseList
                    param = CourseListIntent().apply {
                        id = it.courseId
                        name = it.courseName
                    }
                })
            }
            AppModule.userActive()
        }

        llGuide.clickWithTrigger {
            routeAction(ActionType.actionFeatureGuide)
            AppModule.userActive()
        }

        retryButton.clickWithTrigger {
            setLoadingUI(true)
            viewModel?.getNewbieCenterData()
            AppModule.userActive()
        }
    }

    private fun setGoals(entity: NewbieCenterEntity?) {
        entity?.let {
            tvTrialJob.text = String.format(getString(R.string.text_trial_job), it.countDay)
            if (it.inKeepDay.isNotEmpty()) {
                val desc = String.format(getString(R.string.text_newbie_day_desc), it.inKeepDay)
                tvDayDesc.text = textSpanValue(this, desc, it.inKeepDay, R.color.black, true)
            }
            it.trialGoals?.let { goals ->
                if (goals.status == 1) {
                    tvGoalsStatus.text = "Done"
                    tvGoalsStatus.setBackgroundResource(R.color.color_8fe673)
                } else {
                    tvGoalsStatus.text = "Undone"
                    tvGoalsStatus.setBackgroundResource(com.iandroid.allclass.lib_common.R.color.cl_595959)
                }
                goalsAdapter?.updateData(goals.progressList)
            }
        }
    }

    private fun setLesson(lesson: LessonEntity?) {
        llLesson.show(lesson != null)
        lesson?.let {
            if (it.status == 1) {
                tvLessonStatus.text = "Done"
                tvLessonStatus.setBackgroundResource(R.color.color_8fe673)
            } else {
                tvLessonStatus.text = "Undone"
                tvLessonStatus.setBackgroundResource(com.iandroid.allclass.lib_common.R.color.cl_595959)
            }
            lessonAdapter?.updateData(it.getList())
        }
    }

    private fun setQA(qaList: List<QAItemEntity>?) {
        tvQA.show(qaList?.isNotEmpty() == true)
        flQA.show(qaList?.isNotEmpty() == true)
        qaAdapter?.setData(qaList)
    }

    private fun setLoadingUI(isLoading: Boolean) {
        if (isLoading) {
            retryButton.isEnabled = false
            tvRetryLoading.show(true)
            tvRetry.text = getString(R.string.loading_ing)
        } else {
            retryButton.isEnabled = true
            tvRetryLoading.show(false)
            tvRetry.text = getString(R.string.click_to_retry)
        }
    }

    override fun immersiveIntoStatusBar(): Boolean {
        return false
    }

    override fun isShowTitleBar(): Boolean {
        return false
    }

}