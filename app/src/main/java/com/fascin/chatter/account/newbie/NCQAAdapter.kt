package com.fascin.chatter.account.newbie

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.bean.QAItemEntity
import kotlinx.android.synthetic.main.itemview_ncqa.view.mQaView

class NCQAAdapter : RecyclerView.Adapter<NCQAAdapter.ViewHolder>() {

    private val dataList = mutableListOf<QAItemEntity>()

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

    fun setData(data: List<QAItemEntity>?) {
        dataList.clear()
        dataList.addAll(data.orEmpty())
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.itemview_ncqa, parent, false)
        )
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = dataList[position]
        holder.itemView.mQaView.setQaItem(position, item.question, item.answer)
    }

    override fun getItemCount(): Int {
        return dataList.size
    }

}