package com.fascin.chatter.account.newbie

import android.os.Bundle
import android.text.SpannableString
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.view.KeyEvent
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.GridLayoutManager
import com.fascin.chatter.R
import com.fascin.chatter.account.newbie.navigation.BaseTaskActivity
import com.fascin.chatter.account.newbie.navigation.TaskType
import com.fascin.chatter.bean.BindModelIntent
import com.fascin.chatter.bean.SelectModelEntity
import com.fascin.chatter.bean.event.UISelectModelEvent
import com.fascin.chatter.bean.event.UISelectModelFailedEvent
import com.fascin.chatter.databinding.ActivitySelectModelBinding
import com.iandroid.allclass.lib_common.event.UINewAnchorTaskStatusEvent
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.ToastUtils
import com.iandroid.allclass.lib_common.utils.clickWithTrigger


/**
 * @Desc: 新手引导任务：选择model
 * @Created: Quan
 * @Date: 2024/8/15
 */
class SelectModelActivity : BaseTaskActivity() {

    private lateinit var binding: ActivitySelectModelBinding
    private var intentData: BindModelIntent? = null
    private var adapter: SelectModelAdapter? = null
    private var selectedCount: Int = 0
    private var maxCount: Int = 3

    private val foregroundColorSpan by lazy {
        ForegroundColorSpan(ContextCompat.getColor(this, R.color.cl_ffa500))
    }

    private val absoluteSizeSpan by lazy {
        AbsoluteSizeSpan(30, true)
    }

    override fun determineCurrentTaskType(): TaskType? = TaskType.SELECT_MODEL

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySelectModelBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initStatusBarMode()
        initViewData()
        setListener()
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        return if (keyCode == KeyEvent.KEYCODE_BACK && intentData?.from == ActionType.actionNewAnchor) { //激活流程来的不允许返回
            true
        } else {
            super.onKeyDown(keyCode, event)
        }
    }


    private fun initViewData() {
        intentData = parseJsonParams<BindModelIntent>()
        intentData?.let {
            if (it.from == 1) maxCount = it.maxNum

            //激活流程来的不显示标题栏
            binding.baseTitleView.isVisible = it.from != ActionType.actionNewAnchor
        }
        adapter = SelectModelAdapter()
        binding.rvSelectModel.layoutManager = GridLayoutManager(this, 3)
        binding.rvSelectModel.adapter = adapter


        accountViewModel.getSelectModelList()
    }

    private fun setListener() {
        binding.titleBack.clickWithTrigger {
            finish()
        }

        accountViewModel.selectModelResult.observe(this) {
            if (it.isNullOrEmpty()) {
                ToastUtils.showToast("Request data is empty!")
            }
            setPageData(it)
        }

        accountViewModel.selectModelError.observe(this) {
            ToastUtils.showToast(it)
        }

        accountViewModel.compositeDisposable?.add(SimpleRxBus.observe(UISelectModelFailedEvent::class) {
            // 此model不可绑定，绑定失败,刷新数据
            accountViewModel.getSelectModelList()
        })

        accountViewModel.compositeDisposable?.add(SimpleRxBus.observe(UISelectModelEvent::class) {
            selectedCount++
            adapter?.setSelectedCount(selectedCount)
            adapter?.modelSelected(it.modelId)
            updateSelectText()
            if (selectedCount >= maxCount) {
                if (intentData?.from == ActionType.actionNewAnchor) { //激活流程过来的
                    onTaskCompleted()
                } else {
                    finish()
                }
            }
        })
    }

    private fun setPageData(data: ArrayList<SelectModelEntity>) {
        selectedCount = 0
        data.filter { it.isBind() }.let {
            selectedCount = if (it.isEmpty()) 0 else it.size
            updateSelectText()
        }
        adapter?.setData(data)
    }

    private fun updateSelectText() {
        if (selectedCount == 0) {
            val originContent = String.format(getString(R.string.model_active_normal), maxCount)

            val start = originContent.indexOf(maxCount.toString())
            val end = start + maxCount.toString().length
            val spannable = SpannableString(originContent)
            spannable.setSpan(foregroundColorSpan, start, end, SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE)
            spannable.setSpan(absoluteSizeSpan, start, end, SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE)
            binding.tvSubtitle.text = spannable
        } else {
            val originContent = String.format(
                getString(R.string.model_active_selected),
                selectedCount,
                maxCount
            )

            val start = originContent.indexOf(selectedCount.toString())
            val end = originContent.indexOf(maxCount.toString()) + maxCount.toString().length
            val spannable = SpannableString(originContent)
            spannable.setSpan(foregroundColorSpan, start, end, SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE)
            spannable.setSpan(absoluteSizeSpan, start, end, SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE)
            binding.tvSubtitle.text = spannable
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        SimpleRxBus.post(UINewAnchorTaskStatusEvent())
    }



    override fun isShowTitleBar(): Boolean {
        return false
    }

    override fun immersiveIntoStatusBar(): Boolean {
        return true
    }
}