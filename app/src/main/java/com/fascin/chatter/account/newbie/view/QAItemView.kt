package com.fascin.chatter.account.newbie.view

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import com.fascin.chatter.R
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.show
import kotlinx.android.synthetic.main.view_qa_item.view.ivArrow
import kotlinx.android.synthetic.main.view_qa_item.view.line
import kotlinx.android.synthetic.main.view_qa_item.view.llQuestion
import kotlinx.android.synthetic.main.view_qa_item.view.tvAnswer
import kotlinx.android.synthetic.main.view_qa_item.view.tvQuestion
import kotlinx.android.synthetic.main.view_qa_item.view.tvQuestionNum

/**
 * 新主播收口：QA item
 */
class QAItemView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private var isExpanded = true

    init {
        View.inflate(context, R.layout.view_qa_item, this)
        llQuestion.clickWithTrigger {
            if (isExpanded) {
                isExpanded = false
                line.show(false)
                tvAnswer.show(false)
                ivArrow.setImageResource(R.mipmap.ic_arrow_off)
            } else {
                isExpanded = true
                line.show(true)
                tvAnswer.show(true)
                ivArrow.setImageResource(R.mipmap.ic_arrow_on)
            }
        }
    }

    fun setQaItem(position: Int, question: String, answer: String) {
        tvQuestionNum.text = buildString {
            append(position + 1)
            append(". ")
        }
        tvQuestion.text = question
        isExpanded = true
        line.show(true)
        tvAnswer.show(true)
        ivArrow.setImageResource(R.mipmap.ic_arrow_on)
        tvAnswer.text = answer
    }
}