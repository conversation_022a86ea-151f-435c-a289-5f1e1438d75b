package com.fascin.chatter

import android.app.Activity
import com.fascin.chatter.account.LoginActivity
import com.fascin.chatter.bean.event.LoginFinishEvent
import com.fascin.chatter.config.Config
import com.fascin.chatter.main.ActiveAlertDialog
import com.fascin.chatter.repository.AppRepository
import com.fascin.chatter.utils.StatusHeartbeatManager
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.AppController
import com.iandroid.allclass.lib_common.UserController
import com.iandroid.allclass.lib_common.beans.SysPushEntity
import com.iandroid.allclass.lib_common.beans.UserEntity
import com.iandroid.allclass.lib_common.beans.event.UILoggedInEvent
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.SPConstants
import com.iandroid.allclass.lib_common.utils.SPUtils
import io.reactivex.Observable
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit
import java.util.regex.Pattern

object AppModule {
    var compositeDisposable: CompositeDisposable = CompositeDisposable()
    var activeCheckTimerDisposable: Disposable? = null
    var activeDeadTime: Long = 0
    var isMatchTabShow = false
    var selectEmojiId = ""
    fun init() {

    }

    fun exit() {
        compositeDisposable.dispose()
        compositeDisposable.clear()
        closeActiveCheckTimer()
    }

    fun getRongCloudeKey(): String {
        return if (AppController.isProdEnv()) {
            Config.rongCloudAppKey_Prod
        } else {
            Config.rongCloudAppKey_Dev
        }
    }

    fun userActive() {
        if (activeCheckTimerDisposable == null) {
            AppRepository.statusChange(Config.statusActive)
            startUserActive()
        } else {
            activeDeadTime =
                System.currentTimeMillis() + AppController.getActiveTimeInternal() * 1000
        }
    }

    fun startUserActive() {
        var timeInternal = AppController.getActiveTimeInternal() * 1000
        if (timeInternal <= 0) return
        closeActiveCheckTimer()
        activeDeadTime = System.currentTimeMillis() + timeInternal
        activeCheckTimerDisposable =
            Observable.interval(0, 2, TimeUnit.SECONDS).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread()).subscribe({ _ ->
                    if (System.currentTimeMillis() > activeDeadTime + 10 * 1000) {
                        //具体超时已经超过10秒钟，通知后台不活跃
                        AppRepository.statusChange(Config.statusNoActive)
                        StatusHeartbeatManager.stopHeartbeat()
                        closeActiveCheckTimer()
                    } else if (System.currentTimeMillis() >= activeDeadTime &&
                        !ActiveAlertDialog.isActiveDialogShow
                    ) {
                        val isNewChatter = SPUtils.getBoolean(
                            AppContext.context,
                            UserController.attachAccount(SPConstants.KEY_IS_NEW_CHATTER),
                            false
                        )
                        if (AppContext.getTopActivity()?.javaClass == LoginActivity::class.java || isNewChatter) return@subscribe
                        //弹框
                        AppContext.getTopFragmentActivity()?.also { fragmentActivity ->
                            ActiveAlertDialog().show(
                                fragmentActivity.supportFragmentManager,
                                ActiveAlertDialog::javaClass.name
                            )
                        }
                    }
                }) { }
    }

    fun closeActiveCheckTimer() {
        activeCheckTimerDisposable?.dispose()
        activeCheckTimerDisposable = null
        activeDeadTime = 0
    }
}

fun String?.isValidPassword(): Boolean {
//    return this?.takeIf { !it.isNullOrEmpty() && it.matches(Regex("^(?![0-9]+\$)(?![a-zA-Z]+\$)[0-9A-Za-z._]{8,16}\$")) }
    return this?.takeIf { !it.isNullOrEmpty() && it.matches(Regex("^.{6,}\$")) }
        ?.run {
            true
        } ?: false
}

fun String?.isValidPasswordLength(): Boolean {
    return this?.takeIf { !it.isNullOrEmpty() && it.matches(Regex("^.{8,}\$")) }
        ?.run {
            true
        } ?: false
}

fun String?.isValidCode(): Boolean {
    return this?.takeIf { !it.isNullOrEmpty() && it.length == 4 }?.run {
        true
    } ?: false
}

fun String?.isValidEmail(): Boolean {
    return this?.takeIf { !it.isNullOrEmpty() && it.length >= 5 }?.run {
        Pattern.compile(Config.email_reg).matcher(this).matches()
    } ?: false
}

fun String?.isValidAccount(): Boolean {
    return this?.takeIf { !it.isNullOrEmpty() && it.length >= 5 }?.run {
        return true
    } ?: false
}

fun Activity.loginReturn(needPostFinishEvent: Boolean = false) {
    UserController.getLocalUser()?.also {
        routeAction(ActionType.actionTypeMain)
        SimpleRxBus.post(UILoggedInEvent(it))
    }
    if (needPostFinishEvent) SimpleRxBus.post(LoginFinishEvent(true))
    finish()
}

fun UserEntity?.getUserNickName(): String {
    return this?.takeIf { !it.nickname.isNullOrEmpty() }?.run {
        this.nickname
    } ?: "YourMatch"
}


fun SysPushEntity?.getUserNickName(): String {
    return this?.takeIf { !it.nickname.isNullOrEmpty() }?.run {
        this.nickname
    } ?: "YourMatch"
}

fun Int?.isShowOnline(): Boolean {
    return this != null && this > 0
}

fun String.getUserId(): String {
    if (!this.isNullOrEmpty()) {
        val ids = this.split("_")
        return if (ids.size > 1) ids[1] else ids[0]
    }
    return ""
}

fun String?.getModelId(): String {
    if (!this.isNullOrEmpty()) {
        val targetIdIndex = this.indexOf("_")
        if (targetIdIndex > 0) {
            return this.substring(0, targetIdIndex)
        }
    }
    return ""
}

//站内在线推送开关 ture开 false 关
fun inAppOnlineSwitch(): Boolean {
    return (SPUtils.getInt(
        AppContext.context,
        SPConstants.KEY_PUSH_CONFIG,
        0
    ) and 0x01) != Config.closeInAppPush
}

//发送消息的时候上报选择的EmojiId格式 1,2,3 去重
fun Int.addEmojiID() {
    if (!AppModule.selectEmojiId.split("、").contains("$this")) {
        if (AppModule.selectEmojiId.isEmpty()) {
            AppModule.selectEmojiId += "$this"
        } else {
            AppModule.selectEmojiId += ",$this"
        }
    }
}