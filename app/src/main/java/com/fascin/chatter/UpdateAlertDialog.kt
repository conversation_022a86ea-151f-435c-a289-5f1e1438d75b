package com.fascin.chatter

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.fragment.app.FragmentActivity
import com.fascin.chatter.route.ActionParser
import com.iandroid.allclass.lib_basecore.utils.DeviceUtils
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.Values
import com.iandroid.allclass.lib_common.base.BaseDialogFragment
import com.iandroid.allclass.lib_common.beans.AppUpdateEntity
import com.iandroid.allclass.lib_common.download.UpdateManager
import com.iandroid.allclass.lib_common.utils.exts.castObject
import com.iandroid.allclass.lib_common.utils.exts.objFromBundleParam
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.toJsonString
import kotlinx.android.synthetic.main.dialog_update_alert.alert_dialog_cancel
import kotlinx.android.synthetic.main.dialog_update_alert.alert_dialog_confirm
import kotlinx.android.synthetic.main.dialog_update_alert.alert_dialog_content

/**
created by wangkm
on 2020/12/7.
 */
class UpdateAlertDialog : BaseDialogFragment() {
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_update_alert, container, false)
    }

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?
    ) {
        arguments?.objFromBundleParam<AppUpdateEntity>(Values.intentJsonParam)?.run {
            alert_dialog_content.text = this.content.replace("\\n", "\n")
            alert_dialog_confirm.setOnClickListener {
                if (this.app_down > 0) {
                    AppContext.getTopActivity()?.takeIf { it is FragmentActivity }?.also {
                        UpdateManager.downloadApk(
                            it as FragmentActivity,
                            this.url,
                            "${AppContext.getString(R.string.app_name)}_${this.version}"
                        )
                    }
                } else {
                    kotlin.runCatching {
                        context?.startActivity(Intent(Intent.ACTION_VIEW, Uri.parse(url)))
                    }
                }
                if (this.type != AppUpdateEntity.FORCE_UPDATE) {
                    dismissAllowingStateLoss()
                } else if (this.app_down > 0) {
                    alert_dialog_confirm.isEnabled = false
                }
            }

            alert_dialog_cancel.setOnClickListener {
                dismissAllowingStateLoss()
            }

            if (this.type == AppUpdateEntity.FORCE_UPDATE) {
                isCancelable = false
                alert_dialog_cancel.show(false)
            } else if (this.type == AppUpdateEntity.REC_UPDATE || this.type == AppUpdateEntity.NO_TIP_UPDATE) {
                alert_dialog_cancel.show(true)
            }
        }
    }

    override fun onDetach() {
        super.onDetach()
        ActionParser.instance.showNextMainPagePopDialogAction()
    }

    override fun onStart() {
        super.onStart()
        setCenterPopupAttr(
            (DeviceUtils.getScreenWidth(context) * 0.85f).toInt(),
            WindowManager.LayoutParams.WRAP_CONTENT
        )
    }

    companion object {
        const val KEY_UPDATEDIALOG_POP = "key_updateinfo_id"

        fun newInstance(block: (bundle: Bundle) -> Any): UpdateAlertDialog = UpdateAlertDialog()
            .apply {
                var bundle = Bundle()
                block.invoke(bundle)
                arguments = bundle
            }

        fun showAppUpdateDialog(updateInfo: AppUpdateEntity, forceShow: Boolean = false) {
            if (!canshowAppUpdateDialog(updateInfo, forceShow)) return
            var fragmentActivity =
                AppContext.getTopActivity()?.castObject<FragmentActivity>()
            fragmentActivity?.let {
                newInstance { bundle ->
                    bundle.putString(Values.intentJsonParam, updateInfo.toJsonString())
                }.show(it.supportFragmentManager, UpdateAlertDialog::javaClass.name)
            }
        }

        fun canshowAppUpdateDialog(
            updateInfo: AppUpdateEntity,
            forceShow: Boolean = false
        ): Boolean {
            if (updateInfo.type == AppUpdateEntity.NO_UPDATE) return false
            return true
        }
    }
}