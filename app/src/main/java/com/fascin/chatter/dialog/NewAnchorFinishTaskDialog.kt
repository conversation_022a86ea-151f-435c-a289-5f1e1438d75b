package com.fascin.chatter.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.fragment.app.FragmentActivity
import com.fascin.chatter.R
import com.iandroid.allclass.lib_basecore.utils.DeviceUtils
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.base.BaseDialogFragment
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.castObject
import kotlinx.android.synthetic.main.dialog_new_anchor_finish_task.tvFinishOk

/**
 *  @author: LXL
 *  @description: 新主播完成任务弹窗
 *  @date: 2024/8/16 10:54
 */
class NewAnchorFinishTaskDialog : BaseDialogFragment {
    private lateinit var rewardMoney: String
    private var isCancel: Boolean = true
    private var onOkClick: (() -> Unit)? = null

    // 无参构造函数
    constructor() : super()

    // 带参数的构造函数
    constructor(rewardMoney: String, isCancel: Boolean = true, onOkClick: (() -> Unit)? = null) : super() {
        this.rewardMoney = rewardMoney
        this.isCancel = isCancel
        this.onOkClick = onOkClick
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return inflater.inflate(R.layout.dialog_new_anchor_finish_task, container, false)
    }

    override fun onStart() {
        super.onStart()
        val screenWidth = DeviceUtils.getScreenWidth(context).toFloat()
        val width = screenWidth * 0.859
        setCenterPopupAttr(width.toInt(), WindowManager.LayoutParams.WRAP_CONTENT)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (!isCancel) {
            isCancelable = false
        }
        tvFinishOk.clickWithTrigger {
            onOkClick?.invoke()
            dismissAllowingStateLoss()
        }
    }

    companion object {
        fun showNewAnchorFinishTaskDialog(
            rewardMoney: String,
            isCancel: Boolean = true,
            onOkClick: (() -> Unit)? = null
        ) {
            val fragmentActivity = AppContext.getTopActivity()?.castObject<FragmentActivity>()
            fragmentActivity?.let {
                NewAnchorFinishTaskDialog(rewardMoney, isCancel, onOkClick).show(
                    it.supportFragmentManager, NewAnchorFinishTaskDialog::class.java.name
                )
            }
        }
    }
}