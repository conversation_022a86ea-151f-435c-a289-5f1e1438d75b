package com.fascin.chatter.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import com.fascin.chatter.R
import com.iandroid.allclass.lib_basecore.utils.DeviceUtils
import com.iandroid.allclass.lib_common.base.BaseDialogFragment
import com.iandroid.allclass.lib_common.beans.PassChatterWithdrawEntity
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import kotlinx.android.synthetic.main.dialog_un_passed_job.ivClose
import kotlinx.android.synthetic.main.dialog_un_passed_job.tvGot

/**
 * 试岗未通过去提现的提示弹窗
 */
class UnPassedJobDialog() : BaseDialogFragment() {

    private var entity: PassChatterWithdrawEntity? = null

    constructor(entity: PassChatterWithdrawEntity) : this() {
        this.entity = entity
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_un_passed_job, container, false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_FRAME, com.iandroid.allclass.lib_common.R.style.com_anim_dialog)
    }

    override fun onStart() {
        super.onStart()
        setCenterPopupAttr(
            (DeviceUtils.getScreenWidth(context) * 0.86).toInt(),
            WindowManager.LayoutParams.WRAP_CONTENT
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {


        ivClose.clickWithTrigger {
            dismissAllowingStateLoss()
        }

        tvGot.clickWithTrigger {
            // 跳转到体现页面
            entity?.let {
                if (it.propertyInfo != null) {
                    context.routeAction(ActionType.actionWithdraw) { action ->
                        action.param = it.propertyInfo!!.also { info ->
                            info.from = 1
                            info.ciphertext = it.ciphertext
                        }
                    }
                }
            }
            dismissAllowingStateLoss()
        }
    }
}