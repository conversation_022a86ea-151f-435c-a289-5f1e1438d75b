package com.fascin.chatter.dialog

import android.os.Bundle
import android.text.method.ScrollingMovementMethod
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.bean.NoticeEntity
import com.fascin.chatter.repository.AppRepository
import com.iandroid.allclass.lib_basecore.utils.DeviceUtils
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.GlideLoader.loadImage
import com.iandroid.allclass.lib_common.base.BaseDialogFragment
import com.iandroid.allclass.lib_common.event.EventKey
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.getCompatColor
import com.iandroid.allclass.lib_common.utils.exts.show
import kotlinx.android.synthetic.main.dialog_notice.btnNoticeGoIt
import kotlinx.android.synthetic.main.dialog_notice.ivNoticeImg
import kotlinx.android.synthetic.main.dialog_notice.tvNoticeContent
import kotlinx.android.synthetic.main.dialog_notice.tvNoticeTitle
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 *  @author: LXL
 *  @description: 通知类弹窗
 *  @date: 2024/5/6 13:57
 */
class NoticeDialog() : BaseDialogFragment() {
    private lateinit var noticeEntity: NoticeEntity

    // 带参数的构造函数
    constructor(noticeEntity: NoticeEntity) : this() {
        this.noticeEntity = noticeEntity
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_notice, container, false)
    }

    override fun onStart() {
        super.onStart()
        val screenWidth = DeviceUtils.getScreenWidth(context).toFloat()
        val width = screenWidth * 0.859
        setCenterPopupAttr(width.toInt(), WindowManager.LayoutParams.WRAP_CONTENT)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        AppModule.userActive()
        AppRepository.eventTrace(EventKey.no_popup_v) {
            "noticeId" to noticeEntity.id
        }
        isCancelable = false
        ivNoticeImg.show(noticeEntity.imgUrl.isNotEmpty())

        if (ivNoticeImg.isVisible) {
            tvNoticeContent.maxHeight = (DeviceUtils.getScreenHeight(context) * 0.4).toInt()
        } else {
            tvNoticeContent.maxHeight = (DeviceUtils.getScreenHeight(context) * 0.62).toInt()
        }

        context?.let { ivNoticeImg.loadImage(it, noticeEntity.imgUrl, roundedCorners = 0) }
        tvNoticeTitle.text = noticeEntity.title
        tvNoticeContent.text = noticeEntity.content

        if (noticeEntity.gravity == 0) {
            tvNoticeContent.gravity = Gravity.CENTER
        } else {
            tvNoticeContent.gravity = Gravity.START
        }
        noticeCountDown()
        tvNoticeContent.movementMethod = ScrollingMovementMethod()
        tvNoticeContent.isVerticalScrollBarEnabled = true
        tvNoticeContent.isHorizontalScrollBarEnabled = false
    }

    /**
     * 倒计时
     */
    private fun noticeCountDown() {
        if (noticeEntity.buttonDuration <= 0) {
            btnNoticeGoIt.isEnabled = true
            btnNoticeGoIt.text = noticeEntity.buttonLabel
            btnNoticeGoIt.setTextColor(requireActivity().getCompatColor(R.color.white))
            btnNoticeGoIt.setBackgroundColor(requireActivity().getCompatColor(com.iandroid.allclass.lib_common.R.color.cl_262626))
        } else {
            btnNoticeGoIt.text = "${noticeEntity.buttonLabel} (${noticeEntity.buttonDuration}s)"
            btnNoticeGoIt.isEnabled = false
            lifecycleScope.launch {
                withContext(Dispatchers.Default) {
                    for (i in noticeEntity.buttonDuration downTo 0) {
                        delay(1000)
                        withContext(Dispatchers.Main) {
                            btnNoticeGoIt.text = "${noticeEntity.buttonLabel} (${i}s)"
                        }
                    }
                    withContext(Dispatchers.Main) {
                        btnNoticeGoIt.isEnabled = true
                        btnNoticeGoIt.text = noticeEntity.buttonLabel
                        btnNoticeGoIt.setTextColor(requireActivity().getCompatColor(R.color.white))
                        btnNoticeGoIt.setBackgroundColor(requireActivity().getCompatColor(com.iandroid.allclass.lib_common.R.color.cl_262626))
                    }
                }
            }
        }
        btnNoticeGoIt.clickWithTrigger {
            AppRepository.announcementRead(noticeEntity.id)
            AppRepository.eventTrace(EventKey.no_popup_c) {
                "noticeId" to noticeEntity.id
            }
            if (noticeEntity.action != null) {
                AppContext.getTopActivity()?.routeAction(noticeEntity.action)
            }
            dismissAllowingStateLoss()
        }
    }
}