package com.fascin.chatter.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import com.fascin.chatter.R
import com.iandroid.allclass.lib_basecore.utils.DeviceUtils
import com.iandroid.allclass.lib_common.base.BaseDialogFragment
import kotlinx.android.synthetic.main.dialog_sel_date.alert_dialog_confirm
import kotlinx.android.synthetic.main.dialog_sel_date.nbMon
import kotlinx.android.synthetic.main.dialog_sel_date.nbYear
import java.util.Calendar

/**
 * @Desc: 选择年月
 * @Created: Quan
 * @Date: 2024/10/12
 */
class SelDateDialog(private val completeBlock: (Int, Int) -> Unit) : BaseDialogFragment() {

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return inflater.inflate(R.layout.dialog_sel_date, container, false)
    }

    override fun onStart() {
        super.onStart()
        val screenWidth = DeviceUtils.getScreenWidth(context).toFloat()
        val width = screenWidth * 0.86
        setCenterPopupAttr(width.toInt(), WindowManager.LayoutParams.WRAP_CONTENT)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        // 获取当前年份
        val currentYear = Calendar.getInstance().get(Calendar.YEAR)

        // 设置年份选择器，限制范围为当前年份及前一年
        nbYear.minValue = currentYear - 1
        nbYear.maxValue = currentYear
        nbYear.value = currentYear

        // 设置月份选择器
        nbMon.minValue = 1
        nbMon.maxValue = 12
        nbMon.value = Calendar.getInstance().get(Calendar.MONTH) + 1

        alert_dialog_confirm.setOnClickListener {
            completeBlock.invoke(nbYear.value, nbMon.value)
            dismiss()
        }
    }

}