package com.fascin.chatter.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.bean.CustomerServiceIntent
import com.fascin.chatter.bean.IMPenaltiesData
import com.iandroid.allclass.lib_basecore.utils.DeviceUtils
import com.iandroid.allclass.lib_common.AppController
import com.iandroid.allclass.lib_common.base.BaseDialogFragment
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import kotlinx.android.synthetic.main.dialog_chat_penalty.btnPenaltyContactUs
import kotlinx.android.synthetic.main.dialog_chat_penalty.btnPenaltyGoIt
import kotlinx.android.synthetic.main.dialog_chat_penalty.tvChatMsgPenalty
import kotlinx.android.synthetic.main.dialog_chat_penalty.tvChatPenaltiesNoticeTitle
import kotlinx.android.synthetic.main.dialog_chat_penalty.tvChatPenaltiesReason

/**
 *  @author: LXL
 *  @description: 聊天页质检处罚通知弹窗
 *  @date: 2024/6/12 18:33
 */
class ChatPenaltyDialog : BaseDialogFragment {
    private lateinit var penaltyEntity: IMPenaltiesData

    // 无参构造函数
    constructor() : super()

    // 带参数的构造函数
    constructor(penaltyEntity: IMPenaltiesData) : super() {
        this.penaltyEntity = penaltyEntity
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return inflater.inflate(R.layout.dialog_chat_penalty, container, false)
    }

    override fun onStart() {
        super.onStart()
        setCenterPopupAttr(
            (DeviceUtils.getScreenWidth(context) * 0.859).toInt(),
            WindowManager.LayoutParams.WRAP_CONTENT
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        AppModule.userActive()
        isCancelable = false
        tvChatPenaltiesNoticeTitle.text = penaltyEntity.title
        tvChatPenaltiesReason.text = penaltyEntity.penaltyReason
        tvChatMsgPenalty.text = penaltyEntity.penalty
        btnPenaltyGoIt.clickWithTrigger {
            dismissAllowingStateLoss()
        }
        btnPenaltyContactUs.clickWithTrigger {
            context.routeAction(ActionType.actionOnlineSupport) {
                it.param = CustomerServiceIntent().also { intent ->
                    intent.title = getString(R.string.customer_service_quality)
                    intent.accountId = AppController.getQualityAccount()
                }
            }
            dismissAllowingStateLoss()
        }
    }
}