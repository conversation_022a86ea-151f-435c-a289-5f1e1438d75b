package com.fascin.chatter

import android.content.Context
import android.media.AudioManager
import android.media.MediaPlayer
import android.os.Vibrator
import com.iandroid.allclass.lib_basecore.base.BaseActivity
import com.iandroid.allclass.lib_common.AppContext

/**
created by wangkm
on 2021/8/7.
/*
 **播放媒体音乐或者震动
*/
 */
class RemindAction {
    var mediaPlayer: MediaPlayer? = null
    var mVibrator: Vibrator? = null

    fun release() {
        mVibrator?.cancel()
        mVibrator = null
        mediaPlayer?.stop() //停止
        mediaPlayer?.reset() //重置
        mediaPlayer?.release() //释放资源
        mediaPlayer = null //赋空
    }

    fun startAction(
        isLoop: Boolean = true,
        voiceName: String = "voice_msg.mp3",
        context: Context = AppContext.context,
        isFilterVibrator: Boolean = false  //开启免打扰 过滤震动、声音
    ) {
        if (isFilterVibrator) return
        val mAudioManager = context.getSystemService(BaseActivity.AUDIO_SERVICE) as AudioManager
        //系统音量
        val current = mAudioManager.getStreamVolume(AudioManager.STREAM_SYSTEM)
        if (current > 1) {
            kotlin.runCatching {
                if (mediaPlayer != null) {
                    mediaPlayer?.start()
                } else {
                    mediaPlayer = MediaPlayer()?.also {
                        val fileDescriptor = context.assets.openFd(voiceName)
                        it.setDataSource(
                            fileDescriptor.fileDescriptor,
                            fileDescriptor.startOffset,
                            fileDescriptor.length
                        )
                        it.setVolume(
                            current.toString().toFloat() / 10,
                            current.toString().toFloat() / 10
                        )
                        //[3]准备播放
                        it.prepareAsync()
                        //[4]开始播放
                        it.start()
                        // 监听音频播放完的代码，实现音频的自动循环播放
                        it.setOnCompletionListener { player ->
                            if (isLoop) {
                                player.start()
                                player.isLooping = isLoop
                            }
                        }
                    }
                }
            }
        } else {
            mVibrator = context.getSystemService(BaseActivity.VIBRATOR_SERVICE) as Vibrator
            //设置震动周期，数组表示时间：等待+执行，单位是毫秒，下面操作代表:等待100，执行100，等待100，执行1000，
            //后面的数字如果为-1代表不重复，之执行一次，其他代表会重复，0代表从数组的第0个位置开始
            mVibrator?.vibrate(
                longArrayOf(
                    100,
                    100,
                    if (isLoop) 1000 else 100,
                    if (isLoop) 1000 else 100
                ), if (isLoop) 0 else -1
            )
        }
    }
}