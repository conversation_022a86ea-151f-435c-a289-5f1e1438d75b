package com.fascin.chatter.repository

import com.fascin.chatter.bean.MixBlockEntity
import com.iandroid.allclass.lib_common.beans.MessageSettingEntity
import com.iandroid.allclass.lib_common.beans.MsgContentEntity
import com.iandroid.allclass.lib_common.beans.base.HttpResult
import com.iandroid.allclass.lib_common.network.DomainProvider.DOMAIN_API
import io.reactivex.Single
import retrofit2.http.*

/**
created by wangkm
on 2020/9/15.
 */
interface HomeService {

    @Headers(DOMAIN_API)
    @POST("user/ban")
    @FormUrlEncoded
    fun userBan(
        @Field("user_id") userId: String,
        @Field("type") type: Int
    ): Single<HttpResult<Any>>

    //首页推荐
    @Headers(DOMAIN_API)
    @POST
    @FormUrlEncoded
    fun fetchMixData(
        @Url url: String,
        @Field("page") page: Int,
    ): Single<HttpResult<MixBlockEntity>>

    // MessageSetting 列表数据获取
    @Headers(DOMAIN_API)
    @POST
    fun fetchMessageSettingSetMixData(
        @Url url: String
    ): Single<HttpResult<List<MessageSettingEntity>>>

    // MessageSetting 指定model列表数据获取
    @Headers(DOMAIN_API)
    @POST
    @FormUrlEncoded
    fun fetchUserMessageSettingSetMixData(
        @Url url: String,
        @Field("user_id") userId:String
    ): Single<HttpResult<List<MsgContentEntity>>>

}