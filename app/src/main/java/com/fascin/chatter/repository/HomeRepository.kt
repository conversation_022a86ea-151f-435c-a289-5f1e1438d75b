package com.fascin.chatter.repository

import com.fascin.chatter.bean.MixBlockEntity
import com.iandroid.allclass.lib_common.beans.MessageSettingEntity
import com.iandroid.allclass.lib_common.beans.MsgContentEntity
import com.iandroid.allclass.lib_common.beans.base.checkData
import com.iandroid.allclass.lib_common.beans.base.checkDataWithRetmsg
import com.iandroid.allclass.lib_common.network.ServiceFactory
import io.reactivex.Single
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers

/**
created by wangkm
on 2020/9/15.
 */
object HomeRepository {
    private val api = ServiceFactory.get(HomeService::class.java)

    /**
     *列表混合数据
     */
    fun fetchMixData(
        url: String,
        page: Int
    ): Single<MixBlockEntity> {
        return api.fetchMixData(url, page)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .map { it.checkData() }
    }

    /**
     * 获取messageSet列表数据
     */
    fun fetchMessageSettingMixData(url: String): Single<List<MessageSettingEntity>> {
        return api.fetchMessageSettingSetMixData(url)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .map { it.checkData() }
    }

    /**
     * 获取messageSet指定model列表数据
     */
    fun fetchUserMessageSettingMixData(url: String,userId: String): Single<List<MsgContentEntity>> {
        return api.fetchUserMessageSettingSetMixData(url,userId)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .map { it.checkData() }
    }

    fun userBan(userId: String, type: Int): Single<String> {
        return api.userBan(userId, type)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .map { result ->
                result.checkDataWithRetmsg()
            }
    }

}