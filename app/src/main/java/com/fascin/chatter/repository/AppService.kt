package com.fascin.chatter.repository

import com.fascin.chatter.bean.ActiveStatusEntity
import com.fascin.chatter.bean.ActivitiesEntity
import com.fascin.chatter.bean.AiAutoChatEntity
import com.fascin.chatter.bean.AnchorShiftEntity
import com.fascin.chatter.bean.AppGuideEntity
import com.fascin.chatter.bean.BonusMatchHubEntity
import com.fascin.chatter.bean.BonusTipEntity
import com.fascin.chatter.bean.ChatFlagEntity
import com.fascin.chatter.bean.ChatNoticeEntity
import com.fascin.chatter.bean.ChatterMatchedEntity
import com.fascin.chatter.bean.CheckCodeEntity
import com.fascin.chatter.bean.CheckPosEntity
import com.fascin.chatter.bean.CheckRepossessionEntity
import com.fascin.chatter.bean.CourseClassEntity
import com.fascin.chatter.bean.CourseSortEntity
import com.fascin.chatter.bean.DailyRankEntity
import com.fascin.chatter.bean.ExaminationEntity
import com.fascin.chatter.bean.FcCountChangeEntity
import com.fascin.chatter.bean.GoalsEntity
import com.fascin.chatter.bean.IMPenaltiesData
import com.fascin.chatter.bean.ImproveEntity
import com.fascin.chatter.bean.InviteFriendEntity
import com.fascin.chatter.bean.InviteHistoryListEntity
import com.fascin.chatter.bean.IsBlockEntity
import com.fascin.chatter.bean.LeaveRecordsEntity
import com.fascin.chatter.bean.LeaveRuleEntity
import com.fascin.chatter.bean.MatchPolicyEntity
import com.fascin.chatter.bean.MineInfoEntity
import com.fascin.chatter.bean.ModelUserEntity
import com.fascin.chatter.bean.MpcEntity
import com.fascin.chatter.bean.MsgEmojiEntity
import com.fascin.chatter.bean.MsgGreetTabEntity
import com.fascin.chatter.bean.NewAnchorEntity
import com.fascin.chatter.bean.NewbieCenterEntity
import com.fascin.chatter.bean.NotMatchModelEntity
import com.fascin.chatter.bean.NoticeEntity
import com.fascin.chatter.bean.OnlineTagEntity
import com.fascin.chatter.bean.PenaltyHistoryEntity
import com.fascin.chatter.bean.PrivacyTabEntity
import com.fascin.chatter.bean.RegActivateEntity
import com.fascin.chatter.bean.RegisterEnableEntity
import com.fascin.chatter.bean.RevitalizeEntity
import com.fascin.chatter.bean.RevitalizeInEntity
import com.fascin.chatter.bean.SchedulePerContentEntity
import com.fascin.chatter.bean.SchedulePerRulesEntity
import com.fascin.chatter.bean.SchedulePerTabEntity
import com.fascin.chatter.bean.SelectModelEntity
import com.fascin.chatter.bean.ShiftChangeListEntity
import com.fascin.chatter.bean.ShiftChangeRuleEntity
import com.fascin.chatter.bean.ShiftEntranceEntity
import com.fascin.chatter.bean.SubmitAnswerEntity
import com.fascin.chatter.bean.TaskCatalogEntity
import com.fascin.chatter.bean.TaskEntity
import com.fascin.chatter.bean.TaskWeekTitleEntity
import com.fascin.chatter.bean.TodayLeaveEntity
import com.fascin.chatter.bean.WarningCheckEntity
import com.fascin.chatter.bean.chat.GiftListReq
import com.fascin.chatter.bean.chat.GiftOpenReq
import com.fascin.chatter.bean.chat.GiftEntity
import com.iandroid.allclass.lib_common.beans.AppUpdateEntity
import com.iandroid.allclass.lib_common.beans.AuthUserEntity
import com.iandroid.allclass.lib_common.beans.ConversationEntity
import com.iandroid.allclass.lib_common.beans.ExploreUserEntity
import com.iandroid.allclass.lib_common.beans.GreetListEntity
import com.iandroid.allclass.lib_common.beans.MediaEntity
import com.iandroid.allclass.lib_common.beans.MessageSettingEntity
import com.iandroid.allclass.lib_common.beans.MsgContentEntity
import com.iandroid.allclass.lib_common.beans.PassWithdrawReq
import com.iandroid.allclass.lib_common.beans.ProfileTagEntity
import com.iandroid.allclass.lib_common.beans.ProfitListEntity
import com.iandroid.allclass.lib_common.beans.PropertyInfoEntity
import com.iandroid.allclass.lib_common.beans.UserEntity
import com.iandroid.allclass.lib_common.beans.WithdrawListEntity
import com.iandroid.allclass.lib_common.beans.WithdrawNumEntity
import com.iandroid.allclass.lib_common.beans.WithdrawNumReq
import com.iandroid.allclass.lib_common.beans.WithdrawProfitReq
import com.iandroid.allclass.lib_common.beans.WithdrawReq
import com.iandroid.allclass.lib_common.beans.base.HttpResult
import com.iandroid.allclass.lib_common.network.DomainProvider.DOMAIN_API
import io.reactivex.Single
import retrofit2.http.Body
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.GET
import retrofit2.http.Headers
import retrofit2.http.POST

/**
created by wangkm
on 2020/9/15.
 */
interface AppService {

    //app弹框信息
    @Headers(DOMAIN_API)
    @POST("tool/updateinfo")
    fun fetchAppUpgradeInfo(): Single<HttpResult<AppUpdateEntity>>

    //登录
    @Headers(DOMAIN_API)
    @POST("tool/login")
    @FormUrlEncoded
    fun accountLogin(
        @Field("account") account: String,
        @Field("pwd") password: String
    ): Single<HttpResult<AuthUserEntity>>

    @Headers(DOMAIN_API)
    @POST("tool/getusersinfo")
    @FormUrlEncoded
    fun simpleUsersInfo(
        @Field("im_uids") userIds: String
    ): Single<HttpResult<List<UserEntity>>>

    //聊天页Chat Notice
    @Headers(DOMAIN_API)
    @POST("tool/chat/notice")
    @FormUrlEncoded
    fun chatNotice(
        @Field("im_uid") userId: String,
    ): Single<HttpResult<List<ChatNoticeEntity>>>

    //关闭指定Chat Notice 通知
    @Headers(DOMAIN_API)
    @POST("tool/chat/notice/close")
    @FormUrlEncoded
    fun chatNoticeClose(
        @Field("im_uid") userId: String,
        @Field("id") id: Int
    ): Single<HttpResult<Any>>

    //通知权限开关状态
    @Headers(DOMAIN_API)
    @POST("tool/pushfalg/update")
    @FormUrlEncoded
    fun pushFlagUpdate(
        @Field("push_flag") pushFlag: Int
    ): Single<HttpResult<Any>>

    @Headers(DOMAIN_API)
    @POST("tool/mineprofile")
    fun getMineInfo(): Single<HttpResult<MineInfoEntity>>

    @Headers(DOMAIN_API)
    @POST("tool/notifyupdate")
    @FormUrlEncoded
    fun notifyChange(
        @Field("config") config: Int
    ): Single<HttpResult<Any>>

    @Headers(DOMAIN_API)
    @POST("tool/otherprofile")
    @FormUrlEncoded
    fun getUserInfo(
        @Field("user_id") userId: String,
        @Field("from") from: Int
    ): Single<HttpResult<UserEntity>>


    @Headers(DOMAIN_API)
    @POST("system/gettaglist")
    fun getTagList(): Single<HttpResult<List<ProfileTagEntity>>>

    @Headers(DOMAIN_API)
    @POST("user/deleteaccount")
    fun delAccount(): Single<HttpResult<Any>>

    @Headers(DOMAIN_API)
    @POST("tool/model/media/list")
    @FormUrlEncoded
    fun getAlbumMediaList(
        @Field("im_id") imId: String,
        @Field("last_id") lastId: String,
        @Field("type") type: Int,
        @Field("from") from: Int,
        @Field("media_type") mediaType: Int,
        @Field("tag") tag: Int
    ): Single<HttpResult<ArrayList<MediaEntity>>>

    @Headers(DOMAIN_API)
    @POST("privatemedia/tag/list")
    fun getPrivacyTagList(): Single<HttpResult<List<PrivacyTabEntity>>>

    @Headers(DOMAIN_API)
    @POST("privatemedia/tool/favorite")
    @FormUrlEncoded
    fun collectMedia(
        @Field("id") mediaId: String,
        @Field("favorite") collect: Boolean
    ): Single<HttpResult<Any>>

    @Headers(DOMAIN_API)
    @POST("tool/getmatchedlist")
    @FormUrlEncoded
    fun getChatterMatchedList(
        @Field("online") online: Int,
        @Field("member") member: Int,
        @Field("last_id") lastId: Int,
        @Field("no_ice") noIce: Int
    ): Single<HttpResult<ChatterMatchedEntity>>

    @Headers(DOMAIN_API)
    @POST("tool/modellist")
    fun getChatterModelList(): Single<HttpResult<ArrayList<ModelUserEntity>>>

    @Headers(DOMAIN_API)
    @POST("tool/sayhi")
    @FormUrlEncoded
    fun sayHi(
        @Field("user_id") userId: String
    ): Single<HttpResult<Any>>

    @Headers(DOMAIN_API)
    @POST("tool/matechread")
    fun readNewMatch(): Single<HttpResult<Any>>

    @Headers(DOMAIN_API)
    @POST("tool/chatter/work_scheduling/read")
    fun readNewSchedule(): Single<HttpResult<Any>>

    /**
     * 添加model消息、问候语
     */
    @Headers(DOMAIN_API)
    @POST("tool/greetingadd")
    @FormUrlEncoded
    fun addMessageOrGreeting(
        @Field("m_id") userId: String,
        @Field("type") type: Int,
        @Field("content") content: String
    ): Single<HttpResult<MsgContentEntity>>

    /**
     * 修改model消息、问候语
     */
    @Headers(DOMAIN_API)
    @POST("tool/greetingedit")
    @FormUrlEncoded
    fun modifyMessageOrGreeting(
        @Field("id") msgID: Int,
        @Field("content") content: String
    ): Single<HttpResult<MsgContentEntity>>

    /**
     * 删除model消息、问候语
     */
    @Headers(DOMAIN_API)
    @POST("tool/greetingdel")
    @FormUrlEncoded
    fun delMessageOrGreeting(
        @Field("mid") userId: String,
        @Field("id") msgID: Int
    ): Single<HttpResult<MessageSettingEntity>>


    @Headers(DOMAIN_API)
    @POST("tool/privacylook")
    @FormUrlEncoded
    fun openPrivacyMsg(
        @Field("to_msgid") to_msgid: String,
        @Field("from_msgid") from_msgid: String,
        @Field("media_id") media_id: Int
    ): Single<HttpResult<Any>>

    @Headers(DOMAIN_API)
    @POST("tool/privacylook")
    @FormUrlEncoded
    fun openPrivacyMultipleMsg(
        @Field("to_msgid") to_msgid: String,
        @Field("from_msgid") from_msgid: String
    ): Single<HttpResult<Any>>

    @Headers(DOMAIN_API)
    @POST("tool/status")
    @FormUrlEncoded
    fun statusChange(
        @Field("status") status: Int,
        @Field("timestamp") timestamp: Long = System.currentTimeMillis(),
    ): Single<HttpResult<ActiveStatusEntity>>

    //客户端状态心跳检测
    @Headers(DOMAIN_API)
    @POST("tool/status/heartbeat")
    @FormUrlEncoded
    fun statusHeartbeat(
        @Field("timestamp") timestamp: Long = System.currentTimeMillis()
    ): Single<HttpResult<Any>>

    /**
     * 获取task数据
     */
    @Headers(DOMAIN_API)
    @POST("tool/v2/taskinfo")
    @FormUrlEncoded
    fun getTasksInfo(@Field("task_id") taskID: Int): Single<HttpResult<TaskEntity>>

    /**
     * 获取task模块数据
     */
    @Headers(DOMAIN_API)
    @POST("tool/mytask")
    fun getTaskCatalogInfo(): Single<HttpResult<List<TaskCatalogEntity>>>

    /**
     * 获取task模块历史数据
     */
    @Headers(DOMAIN_API)
    @POST("tool/task/history")
    fun getTaskHistoryList(): Single<HttpResult<List<TaskWeekTitleEntity>>>

    /**
     * 获取活动Banner
     */
    @Headers(DOMAIN_API)
    @POST("tool/activities/list")
    fun getActivitiesList(): Single<HttpResult<ArrayList<ActivitiesEntity>>>

    /**
     * 获取没有与user建联的model ID
     */
    @Headers(DOMAIN_API)
    @POST("tool/v2/fcmodellist")
    @FormUrlEncoded
    fun getNotMatchModel(@Field("user_id") userId: String?): Single<HttpResult<NotMatchModelEntity>>

    /**
     * 获取没有与user建联的model ID
     */
    @Headers(DOMAIN_API)
    @POST("tool/flashchat")
    @FormUrlEncoded
    fun sendFlashChat(
        @Field("user_id") userId: String?,
        @Field("model_id") modelId: String?,
        @Field("msg") msg: String?,
        @Field("from") from: Int?,
        @Field("stamp") stamp: String,
    ): Single<HttpResult<FcCountChangeEntity>>

    /**
     * 获取课程分类数据
     */
    @Headers(DOMAIN_API)
    @POST("tool/course/category/list")
    fun getCoursesSort(): Single<HttpResult<List<CourseSortEntity>>>

    /**
     * 获取课程列表数据
     */
    @Headers(DOMAIN_API)
    @POST("tool/course/list")
    @FormUrlEncoded
    fun getCourses(
        @Field("latest_id") lastId: Int,
        @Field("category") category: Int,
        @Field("page_size") size: Int
    ): Single<HttpResult<List<CourseClassEntity>>>

    /**
     * 获取课程列表数据
     */
    @Headers(DOMAIN_API)
    @POST("tool/course/exampaper/detail")
    @FormUrlEncoded
    fun getQuestions(
        @Field("course_id") courseId: Int
    ): Single<HttpResult<ExaminationEntity>>

    /**
     * 获取课程列表数据
     */
    @Headers(DOMAIN_API)
    @POST("tool/course/exampaper/submit")
    @FormUrlEncoded
    fun submitAnswers(
        @Field("id") id: Int,
        @Field("version") version: Int,
        @Field("answers") answers: String
    ): Single<HttpResult<SubmitAnswerEntity>>

    /**
     * 获取聊天招呼语
     */
    @Headers(DOMAIN_API)
    @POST("tool/greeting/list")
    @FormUrlEncoded
    fun requestGreet(@Field("imid") modelIdUserId: String?): Single<HttpResult<List<MsgGreetTabEntity>>>

    /**
     * 获取聊天招呼语
     */
    @Headers(DOMAIN_API)
    @POST("tool/model/emojimessage/list")
    fun requestEmojis(): Single<HttpResult<List<MsgEmojiEntity>>>

    /**
     * 获取Match招呼语
     */
    @Headers(DOMAIN_API)
    @POST("tool/sayhi/list")
    @FormUrlEncoded
    fun requestSayHiMsg(@Field("imid") modelIdUserId: String): Single<HttpResult<GreetListEntity>>


    @Headers(DOMAIN_API)
    @POST("tool/sayhi")
    @FormUrlEncoded
    fun matchDialogSayHi(
        @Field("user_id") userId: String,
        @Field("gid") gid: String,
        @Field("content") content: String
    ): Single<HttpResult<Any>>

    /**
     * 更新会话隐身、在线、免打扰开关状态
     */
    @Headers(DOMAIN_API)
    @POST("tool/updatechatflag")
    @FormUrlEncoded
    fun updateChatFlag(
        @Field("im_uid") imUid: String,
        @Field("chat_flag") chatFlag: Long
    ): Single<HttpResult<ChatFlagEntity>>

    /**
     * 更新联系人聊天tag
     */
    @Headers(DOMAIN_API)
    @POST("tool/updateusermark")
    @FormUrlEncoded
    fun updateContactTag(
        @Field("im_uid") imId: String,
        @Field("user_mark") tagId: Int
    ): Single<HttpResult<Any>>

    /**
     * 获取会话记录列表
     */
    @Headers(DOMAIN_API)
    @POST("tool/findvipchat")
    @FormUrlEncoded
    fun getFindChatsTabList(
        @Field("model_id") modelId: Int
    ): Single<HttpResult<ArrayList<ExploreUserEntity>>>

    /**
     * 获取VIP合并展示的数据
     */
    @Headers(DOMAIN_API)
    @POST("tool/findvipchat")
    fun getVIPConflateList(): Single<HttpResult<ArrayList<ExploreUserEntity>>>

    /**
     * 获取会话记录列表
     */
    @Headers(DOMAIN_API)
    @POST("/tool/savechat")
    @FormUrlEncoded
    fun getFindLostList(
        @Field("model_id") modelId: String
    ): Single<HttpResult<ArrayList<ExploreUserEntity>>>

    @Headers(DOMAIN_API)
    @POST("tool/event/trace")
    @FormUrlEncoded
    fun eventTrace(
        @Field("key") key: String,
        @Field("param") param: String
    ): Single<HttpResult<Any>>

    /**
     * 获取低流量配置
     */
    @Headers(DOMAIN_API)
    @POST("tool/match/policy/get")
    fun matchPolicyGet(): Single<HttpResult<MatchPolicyEntity>>

    /**
     * 设置低流量
     */
    @Headers(DOMAIN_API)
    @POST("tool/match/policy/set")
    @FormUrlEncoded
    fun matchPolicySet(
        @Field("enable") enable: Int  //1开，0关
    ): Single<HttpResult<Any>>

    /**
     * 主播绩效中心
     */
    @Headers(DOMAIN_API)
    @POST("tool/chatdata")
    fun chatMpcData(): Single<HttpResult<MpcEntity>>

    /**
     * MPC更新气泡通知检查
     */
    @Headers(DOMAIN_API)
    @POST("tool/chatdata/updatetime")
    fun checkHasMpcNotice(): Single<HttpResult<MpcEntity>>

    /**
     * MPC更新气泡通知检查
     */
    @Headers(DOMAIN_API)
    @POST("tool/trialtask/info")
    fun checkHasGoals(): Single<HttpResult<GoalsEntity>>

    /**
     * 检查model是否被用户拉黑
     */
    @Headers(DOMAIN_API)
    @POST("tool/connect/isblock")
    @FormUrlEncoded
    fun checkIsBlock(
        @Field("im_id") imID: String
    ): Single<HttpResult<IsBlockEntity>>

    /**
     * 获取定向建联剩余次数
     */
    @Headers(DOMAIN_API)
    @POST("tool/bonus_match_hub/time")
    fun bonusMatchHubTime(): Single<HttpResult<BonusMatchHubEntity>>

    /**
     * 定向建联
     */
    @Headers(DOMAIN_API)
    @POST("tool/bonus_match_hub/list")
    fun bonusMatchHubList(): Single<HttpResult<BonusMatchHubEntity>>

    /**
     * 定向建联 Like、Flash Chat 操作
     */
    @Headers(DOMAIN_API)
    @POST("tool/bonus_match_hub/choose")
    @FormUrlEncoded
    fun bonusMatchHubChoose(
        @Field("user_id") userId: String,
        @Field("mid") mid: String,
        @Field("choose_type") chooseType: Int,  //2 like,   3 flash chat
        @Field("msg") msg: String = "",
    ): Single<HttpResult<BonusMatchHubEntity>>

    /**
     * 获取improve method弹窗的数据
     */
    @Headers(DOMAIN_API)
    @POST("tool/chatdata/tips")
    @FormUrlEncoded
    fun getImproveMethod(
        @Field("tip_id") id: String
    ): Single<HttpResult<ImproveEntity>>


    /**
     * 获取所有未读公告
     */
    @Headers(DOMAIN_API)
    @POST("tool/announcement/get")
    fun getAnnouncement(): Single<HttpResult<ArrayList<NoticeEntity>>>

    /**
     * 标记公告已阅读
     */
    @Headers(DOMAIN_API)
    @POST("tool/announcement/read")
    @FormUrlEncoded
    fun announcementRead(
        @Field("announcement_id") id: Int
    ): Single<HttpResult<Any>>

    /**
     * 质检处罚历史
     */
    @Headers(DOMAIN_API)
    @POST("tool/penalty/history")
    fun getPenaltyHistory(): Single<HttpResult<PenaltyHistoryEntity>>

    /**
     * 获取质检处罚通知
     * @param penaltyId 不为空时，则获取指定id的质检处罚通知
     */
    @Headers(DOMAIN_API)
    @POST("tool/penalty/latest/notify")
    @FormUrlEncoded
    fun getPenaltyLatestNotify(
        @Field("imid") imID: String,
        @Field("user_penalty_id") penaltyId: String
    ): Single<HttpResult<IMPenaltiesData>>

    /**
     * 新主播流程
     */
    @Headers(DOMAIN_API)
    @POST("tool/newchatter/task")
    fun newAnchorTask(): Single<HttpResult<NewAnchorEntity>>

    /**
     * 新主播“恭喜”通知已读
     */
    @Headers(DOMAIN_API)
    @POST("tool/new/readcongrats")
    fun newReadCongrats(): Single<HttpResult<RegActivateEntity>>

    /**
     * 获取ai会话的id列表
     */
    @Headers(DOMAIN_API)
    @POST("tool/auto/imid")
    fun getAutoChatIds(): Single<HttpResult<ArrayList<AiAutoChatEntity>>>

    /**
     * 新账号新手任务选择model的列表
     */
    @Headers(DOMAIN_API)
    @POST("tool/new/model/bindlist")
    fun getSelectModelList(): Single<HttpResult<ArrayList<SelectModelEntity>>>

    /**
     * 新账号新手选择排班的列表
     */
    @Headers(DOMAIN_API)
    @GET("tool/chatter/shift_select_list")
    fun getAnchorShiftList(): Single<HttpResult<AnchorShiftEntity>>

    /**
     * 新账号新手选择排班的提交
     */
    @Headers(DOMAIN_API)
    @POST("tool/chatter/shift_select")
    @FormUrlEncoded
    fun submitAnchorShift(
        @Field("shift") shiftId: String
    ): Single<HttpResult<Any>>

    /**
     * 新账号新手任务选择model
     */
    @Headers(DOMAIN_API)
    @POST("tool/model/bind")
    @FormUrlEncoded
    fun selectModel(
        @Field("id") modelId: String
    ): Single<HttpResult<Any>>

    /**
     * 标记主播已经点击邀请好友Tab
     */
    @Headers(DOMAIN_API)
    @POST("tool/invitation/read")
    fun invitationRead(): Single<HttpResult<Any>>

    /**
     * 新主播绑定邀请码
     */
    @Headers(DOMAIN_API)
    @POST("tool/new/bindcode")
    @FormUrlEncoded
    fun newBindCode(
        @Field("code") code: String
    ): Single<HttpResult<Any>>

    @Headers(DOMAIN_API)
    @POST("tool/invitation/read")
    fun skipBindCode(): Single<HttpResult<Any>>

    /**
     * 老主播邀请好友
     */
    @Headers(DOMAIN_API)
    @POST("tool/invitationcode")
    fun inviteFriends(): Single<HttpResult<InviteFriendEntity>>

    /**
     * 历史邀请成功记录
     */
    @Headers(DOMAIN_API)
    @POST("tool/invitation/history")
    fun invitationHistory(): Single<HttpResult<InviteHistoryListEntity>>

    /**
     * 用户上线时，查询是否是高价值的会话
     */
    @Headers(DOMAIN_API)
    @POST("tool/bestconn")
    @FormUrlEncoded
    fun getOnlineTagUser(
        @Field("imids") imids: String
    ): Single<HttpResult<OnlineTagEntity>>

    /**
     * 获取主播薪资余额
     */
    @Headers(DOMAIN_API)
    @POST("tool/property/info")
    fun getPropertyInfo(): Single<HttpResult<PropertyInfoEntity>>

    /**
     * 查询剩余提现次数
     */
    @Headers(DOMAIN_API)
    @POST("tool/chatter/withdrawNum")
    fun queryWithdrawNum(
        @Body requestBean: WithdrawNumReq
    ): Single<HttpResult<WithdrawNumEntity>>

    /**
     * 主播提现
     */
    @Headers(DOMAIN_API)
    @POST("tool/chatter/withdraw")
    fun chatterWithdraw(
        @Body requestBean: WithdrawReq
    ): Single<HttpResult<Any>>

    /**
     * 试岗未通过的主播提现
     */
    @Headers(DOMAIN_API)
    @POST("tool/chatter/unlogin/withdraw")
    fun passChatterWithdraw(
        @Body requestBean: PassWithdrawReq
    ): Single<HttpResult<Any>>

    /**
     * 提现单列表（包含详情数据）
     */
    @Headers(DOMAIN_API)
    @POST("tool/chatter/withdrawList")
    fun withdrawList(
        @Body requestBean: WithdrawProfitReq
    ): Single<HttpResult<WithdrawListEntity>>

    /**
     * 收益流水单列表
     */
    @Headers(DOMAIN_API)
    @POST("/tool/chatter/profitList")
    fun profitList(
        @Body requestBean: WithdrawProfitReq
    ): Single<HttpResult<ProfitListEntity>>

    /**
     *  获得首页右下角vip召回入口图标数据
     */
    @Headers(DOMAIN_API)
    @POST("tool/user/recallinfo")
    fun getRevitalizeIn(): Single<HttpResult<RevitalizeInEntity>>

    /**
     *  获得首页右下角vip召回列表数据
     */
    @Headers(DOMAIN_API)
    @POST("tool/user/recalllist")
    fun getRevitalize(): Single<HttpResult<RevitalizeEntity>>

    /**
     *  排班入口数据
     */
    @Headers(DOMAIN_API)
    @POST("tool/chatter/work_scheduling/check")
    fun shiftEntranceData(): Single<HttpResult<ShiftEntranceEntity>>

    /**
     *  发送vip召回消息
     */
    @Headers(DOMAIN_API)
    @POST("tool/user/sendrecall")
    @FormUrlEncoded
    fun sentRevitalizeMsg(
        @Field("im_uids") ids: String,
        @Field("msg") msg: String
    ): Single<HttpResult<Any>>

    /**
     *  检查是否需要弹回收预警弹窗
     */
    @Headers(DOMAIN_API)
    @POST("tool/recycling/warn")
    fun checkShowRepossession(): Single<HttpResult<CheckRepossessionEntity>>

    /**
     * 通过自己服务器获取最近的会话列表数据
     */
    @Headers(DOMAIN_API)
    @POST("tool/connect/list")
    fun getInitConversations(): Single<HttpResult<List<ConversationEntity>>>

    /**
     * 删除自己服务器获的会话
     */
    @Headers(DOMAIN_API)
    @POST("tool/chat/list/removechat")
    @FormUrlEncoded
    fun removeConversation(@Field("imid") imId: String): Single<HttpResult<Any>>

    /**
     * 排班预览页获取tab最近三周时间
     */
    @Headers(DOMAIN_API)
    @GET("tool/three_weeks")
    fun getSchedulePreTab(): Single<HttpResult<SchedulePerTabEntity>>

    /**
     * 排班预览页内容
     */
    @Headers(DOMAIN_API)
    @POST("tool/chatter/scheduling_info")
    @FormUrlEncoded
    fun getSchedulePreContent(
        @Field("start_week_day") startDay: String,
        @Field("end_week_day") endDay: String
    ): Single<HttpResult<SchedulePerContentEntity>>

    /**
     *  获取排班预览页下方文案
     */
    @Headers(DOMAIN_API)
    @GET("tool/chatter/shift_config")
    fun getSchedulePreRule(): Single<HttpResult<SchedulePerRulesEntity>>

    /**
     * 获取请假页下方文案
     */
    @Headers(DOMAIN_API)
    @GET("tool/chatter/leave_rule")
    fun getLeaveRule(): Single<HttpResult<LeaveRuleEntity>>

    /**
     * 获取换班页下方文案
     */
    @Headers(DOMAIN_API)
    @GET("tool/chatter/change_shift_rule")
    fun getShiftChangeRule(): Single<HttpResult<ShiftChangeRuleEntity>>

    /**
     * 请假
     */
    @Headers(DOMAIN_API)
    @POST("tool/chatter/request_leave")
    @FormUrlEncoded
    fun requestLeave(
        @Field("start_date") startDay: String,
        @Field("end_date") endDay: String
    ): Single<HttpResult<Any>>


    /**
     * 换班申请
     */
    @Headers(DOMAIN_API)
    @POST("tool/chatter/request_change_shift")
    @FormUrlEncoded
    fun shiftChangeSubmit(
        @Field("week_start_date") startDate: String,
        @Field("week_end_date") endDate: String,
        @Field("change_day") weekDay: String,
        @Field("old_shift") oldShiftId: Int,
        @Field("new_shift") newShiftId: Int,
        @Field("change_type") type: Int
    ): Single<HttpResult<Any>>

    /**
     * 查询可换班的班次
     */
    @Headers(DOMAIN_API)
    @POST("tool/chatter/query_change_shift")
    @FormUrlEncoded
    fun getChangeShiftData(
        @Field("change_day") changeDay: String,
        @Field("change_type") type: Int
    ): Single<HttpResult<ShiftChangeListEntity>>

    /**
     * 请假记录
     */
    @Headers(DOMAIN_API)
    @POST("tool/chatter/leave_record")
    fun getLeaveRecords(): Single<HttpResult<LeaveRecordsEntity>>

    /**
     * 取消请假
     */
    @Headers(DOMAIN_API)
    @POST("tool/chatter/cancel_leave")
    @FormUrlEncoded
    fun submitLeaveCancel(@Field("leave_id") leaveId: Int): Single<HttpResult<Any>>

    /**
     * 中断请假
     */
    @Headers(DOMAIN_API)
    @POST("tool/chatter/suspend_leave")
    @FormUrlEncoded
    fun submitLeaveSuspend(
        @Field("leave_record_id") leaveId: Int
    ): Single<HttpResult<Any>>

    /**
     * 请假记录
     */
    @Headers(DOMAIN_API)
    @POST("tool/chatter/leave/check")
    fun checkTodayLeave(): Single<HttpResult<TodayLeaveEntity>>

    /**
     *  是否能注册
     */
    @Headers(DOMAIN_API)
    @POST("tool/check_adid")
    @FormUrlEncoded
    fun checkAdId(
        @Field("adid") adId: String
    ): Single<HttpResult<RegisterEnableEntity>>

    /**
     * 上传应用列表数据
     */
    @Headers(DOMAIN_API)
    @POST("tool/phone/app/report")
    @FormUrlEncoded
    fun uploadAppListInfo(
        @Field("cid") cid: String,
        @Field("content") content: String,
    ): Single<HttpResult<Any>>

    /**
     * 获取排行榜数据
     * @param type 1-新人榜，0-W2榜
     */
    @Headers(DOMAIN_API)
    @POST("tool/ranking/ppv")
    @FormUrlEncoded
    fun getRankList(@Field("is_new_chatter") type: Int): Single<HttpResult<DailyRankEntity>>

    /**
     * 获取引导页配置
     */
    @Headers(DOMAIN_API)
    @POST("tool/page_popup/status")
    fun checkAppGuidePage(): Single<HttpResult<AppGuideEntity>>

    /**
     * 首页BonusMatch提示条数据
     */
    @Headers(DOMAIN_API)
    @POST("tool/bonus_match_hub/info")
    fun getBonusTip(): Single<HttpResult<BonusTipEntity>>

    /**
     * 获取惩罚记录
     */
    @Headers(DOMAIN_API)
    @GET("tool/user/penaltyrecords")
    fun warningCheckInfo(): Single<HttpResult<WarningCheckEntity>>

    /**
     * 新主播收口页面数据
     */
    @Headers(DOMAIN_API)
    @POST("tool/newbie/center")
    fun getNewbieCenterData(): Single<HttpResult<NewbieCenterEntity>>

    /**
     * 通过位置获取具体地名
     */
    @Headers(DOMAIN_API)
    @POST("tool/reg/check_pos_v2")
    @FormUrlEncoded
    fun getLocation(
        @Field("latitude") latitude: Double,
        @Field("longitude") longitude: Double
    ): Single<HttpResult<CheckPosEntity>>

    /**
     * 校验密钥口令
     */
    @Headers(DOMAIN_API)
    @POST("tool/regv2")
    @FormUrlEncoded
    fun regCheckCode(
        @Field("tmp_code") tmpCode: String,
        @Field("train_code") code: String,
        @Field("sm_name") nickname: String
    ): Single<HttpResult<CheckCodeEntity>>

    /**
     * 完成注册
     */
    @Headers(DOMAIN_API)
    @POST("tool/regv2_step2")
    @FormUrlEncoded
    fun finishRegSubmit(
        @Field("tmp_code") tmpCode: String,
        @Field("pwd") pwd: String,
    ): Single<HttpResult<AuthUserEntity>>


    /**
     * 获取礼物兑换列表
     */
    @Headers(DOMAIN_API)
    @POST("/gift/list/tool")
    fun getGiftList(@Body requestBean: GiftListReq): Single<HttpResult<List<GiftEntity>>>

    /**
     * 解锁礼物
     */
    @Headers(DOMAIN_API)
    @POST("/tool/gift/unlock")
    fun openGift(@Body req: GiftOpenReq): Single<HttpResult<Any>>
}