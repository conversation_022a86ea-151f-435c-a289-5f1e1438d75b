package com.fascin.chatter.route

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.view.Gravity
import androidx.fragment.app.FragmentActivity
import com.fascin.chatter.R
import com.fascin.chatter.UpdateAlertDialog
import com.fascin.chatter.account.LoginActivity
import com.fascin.chatter.account.create.FillAccountInfoActivity
import com.fascin.chatter.account.newbie.BindInviteCodeActivity
import com.fascin.chatter.account.newbie.CourseActiveActivity
import com.fascin.chatter.account.newbie.NewAnchorGuideActivity
import com.fascin.chatter.account.newbie.NewAnchorGuideV2Activity
import com.fascin.chatter.account.newbie.NewAnchorShiftActivity
import com.fascin.chatter.account.newbie.NewbieCenterActivity
import com.fascin.chatter.account.newbie.PaymentAccountActiveActivity
import com.fascin.chatter.account.newbie.SelectModelActivity
import com.fascin.chatter.bean.HomeLieksIntent
import com.fascin.chatter.bean.IMChatIntent
import com.fascin.chatter.bean.IMPenaltiesData
import com.fascin.chatter.bean.MatchPolicyEntity
import com.fascin.chatter.bean.MsgAttentionIntent
import com.fascin.chatter.bean.NoticeEntity
import com.fascin.chatter.bean.PrivacyIntent
import com.fascin.chatter.bean.RiskRemindEntity
import com.fascin.chatter.bean.event.UIEventHomeLikesTab
import com.fascin.chatter.dialog.ChatPenaltyDialog
import com.fascin.chatter.dialog.NoticeDialog
import com.fascin.chatter.dialog.UnPassedJobDialog
import com.fascin.chatter.im.ChatActivity
import com.fascin.chatter.im.MessageEmojiDialog
import com.fascin.chatter.im.MessageGiftDialog
import com.fascin.chatter.im.MessageGreetDialog
import com.fascin.chatter.im.MsgAttentionDialog
import com.fascin.chatter.main.MainActivity
import com.fascin.chatter.main.chats.ChatsConfig
import com.fascin.chatter.main.chats.FindChatsActivity
import com.fascin.chatter.main.chats.VipContactSearchActivity
import com.fascin.chatter.main.course.CourseListActivity
import com.fascin.chatter.main.course.CourseSortActivity
import com.fascin.chatter.main.course.QuestionnaireActivity
import com.fascin.chatter.main.match.BonusMatchHubActivity
import com.fascin.chatter.main.profile.CustomerServiceActivity
import com.fascin.chatter.main.profile.MPCActivity
import com.fascin.chatter.main.profile.MessageSettingActivity
import com.fascin.chatter.main.profile.OnlineSupportActivity
import com.fascin.chatter.main.profile.OtherProfileActivity
import com.fascin.chatter.main.profile.PenaltiesActivity
import com.fascin.chatter.main.profile.TrafficActivity
import com.fascin.chatter.main.profile.edit.NotificationActivity
import com.fascin.chatter.main.profile.guide.FeatureGuideActivity
import com.fascin.chatter.main.profile.invite.InviteFriendsActivity
import com.fascin.chatter.main.profile.withdraw.DeductDetailActivity
import com.fascin.chatter.main.profile.withdraw.RewardDetailActivity
import com.fascin.chatter.main.profile.withdraw.WithdrawActivity
import com.fascin.chatter.main.profile.withdraw.WithdrawDetailActivity
import com.fascin.chatter.main.schedule.LeaveRecordsActivity
import com.fascin.chatter.main.schedule.RequestLeaveActivity
import com.fascin.chatter.main.schedule.ScheduleActivity
import com.fascin.chatter.main.schedule.ScheduleRequestActivity
import com.fascin.chatter.main.schedule.ShiftChangeActivity
import com.fascin.chatter.main.task.DailyRankActivity
import com.fascin.chatter.main.task.TaskHistoricalDetailActivity
import com.fascin.chatter.main.task.TaskHistoricalListActivity
import com.fascin.chatter.main.view.NewMatchDialog
import com.fascin.chatter.main.view.RepossessionDialog
import com.fascin.chatter.main.view.RiskRemindDialog
import com.fascin.chatter.main.view.TrafficRemindDialog
import com.fascin.chatter.pay.MediaPreviewActivity
import com.fascin.chatter.pay.PrivacyDialog
import com.fascin.chatter.pay.PrivacyImgActivity
import com.fascin.chatter.pay.PrivacyListActivity
import com.fascin.chatter.pay.PrivacyMultipleSendActivity
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.UserController
import com.iandroid.allclass.lib_common.Values
import com.iandroid.allclass.lib_common.beans.AppUpdateEntity
import com.iandroid.allclass.lib_common.beans.EventTaskFragment
import com.iandroid.allclass.lib_common.beans.PassChatterWithdrawEntity
import com.iandroid.allclass.lib_common.beans.ShareEntity
import com.iandroid.allclass.lib_common.beans.SysPushEntity
import com.iandroid.allclass.lib_common.repository.CommonRepository
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.ComRouteParser
import com.iandroid.allclass.lib_common.route.bean.ActionEntity
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.SPUtils
import com.iandroid.allclass.lib_common.utils.exts.castObject
import com.iandroid.allclass.lib_common.utils.exts.jsonToObj
import com.iandroid.allclass.lib_common.utils.exts.startActivityKtxForResult
import com.iandroid.allclass.lib_common.utils.exts.startActivityKtxWithParam
import com.iandroid.allclass.lib_common.utils.exts.toJsonString
import com.iandroid.allclass.lib_common.video.VideoPlayActivity
import com.iandroid.allclass.lib_common.views.CommonAlertDialog
import com.iandroid.allclass.lib_common.views.CommonShareDialog
import io.rong.imkit.utils.RouteUtils
import java.io.Serializable
import java.util.regex.Pattern

/**
created by wangkm
on 2020/10/1.
 */

/**
 * 路由
 *
 * 语音独立app路由协议（Action,  deeplink协议 H5协议， APP协议，push注册制协议）

主要三种跳转协议：
 * 服务器端接口Action数据
 * H5与Native协议
 * 第三方拉起app(scheme, app deeplink, push等 )



协议内容：
{
"id": 10,
"param": {}
}

注：param根据业务id场景自定义

举例子：

1、服务器数据接口：
"action": {
"id": 10,
"param": {
}
}

2、H5与native协议(jsonEncodeData为url加密的action，目前暂时明文)
webvoice://com.lang.***?action=jsonData

3、第三方拉起(jsonEncodeData为url加密的action，目前暂时明文)
ivoice://com.lang.***?action=jsonEncodeData  (app scheme)
http://com.lang.***?action=jsonEncodeData  (applink)
Https://com.lang.***?action=jsonEncodeData (applink)



Id规范：
1-1000：预留页面跳转协议
1000-2000：预留弹框协议
2000+：预留通知事件协议


 */
class ActionParser private constructor() : Serializable, ComRouteParser() {

    override fun getSchemePattern(): Pattern {
        return urlMatch
    }

    override fun isInMainPage(): Boolean {
        return AppContext.getTopActivity()?.castObject<MainActivity>() != null
    }

    override fun parserRouteAction(context: Context, action: ActionEntity?): Boolean {
        handleActionTrace(action)
        super.parserRouteAction(context, action)?.takeIf { !it }?.run {
            action?.run {
                var fragmentActivity =
                    AppContext.getTopActivity()?.castObject<Activity>()
                when (id) {
                    //登录首页
                    ActionType.actionToLogin -> {
                        when {
                            AppContext.getTopActivity() is LoginActivity -> {}
                            else -> {
                                context.startActivityKtxWithParam<LoginActivity> {
                                    it.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
                                    it.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                                }
                            }
                        }
                    }
                    //私密弹框
                    ActionType.actionPrivacyDialog -> {
                        param?.toJsonString()?.jsonToObj<PrivacyIntent>()?.apply {
                            fragmentActivity?.castObject<FragmentActivity>()?.let {
                                PrivacyDialog(this.status).show(
                                    it.supportFragmentManager,
                                    PrivacyDialog::class.java.name
                                )
                            }
                        }
                    }
                    //弹出分享面板
                    ActionType.actionTypeShareDialog -> {
                        param?.toJsonString()?.jsonToObj<ShareEntity>()?.apply {
                            fragmentActivity?.castObject<FragmentActivity>()?.let {
                                CommonShareDialog.newInstance { bundle ->
                                    bundle.putString(
                                        Values.intentJsonParam,
                                        param?.toJsonString()
                                    )
                                }.show(
                                    it.supportFragmentManager,
                                    CommonShareDialog.javaClass.name
                                )
                            }
                        }
                    }
                    //更新面板
                    ActionType.actionUpdateDialog -> {
                        param?.toJsonString()?.jsonToObj<AppUpdateEntity>()
                            ?.takeIf { it.type != AppUpdateEntity.NO_UPDATE && !it.url.isNullOrEmpty() }
                            ?.apply {
                                fragmentActivity?.let {
                                    if (UpdateAlertDialog.canshowAppUpdateDialog(
                                            this,
                                            this.forceShowDialog
                                        )
                                        && showMainPagePopDialogSoon(
                                            UpdateAlertDialog::javaClass.name,
                                            action
                                        )
                                    ) {
                                        UpdateAlertDialog.showAppUpdateDialog(
                                            this,
                                            this.forceShowDialog
                                        )
                                    }
                                }
                            }
                    }
                    // 聊天页面招呼语弹窗
                    ActionType.actionMsgGreetDialog -> {
                        param?.toJsonString()?.jsonToObj<IMChatIntent>()?.apply {
                            fragmentActivity?.castObject<FragmentActivity>()?.let {
                                MessageGreetDialog(user_id)
                                    .show(
                                        it.supportFragmentManager,
                                        MessageGreetDialog::class.java.name
                                    )
                            }
                        }
                    }
                    // 聊天页面emoji弹窗
                    ActionType.actionMsgEmojiDialog -> {
                        fragmentActivity?.castObject<FragmentActivity>()?.let {
                            MessageEmojiDialog()
                                .show(
                                    it.supportFragmentManager,
                                    MessageEmojiDialog::class.java.name
                                )
                        }
                    }

                    //聊天页Gift弹窗
                    ActionType.actionMsgGiftDialog -> {
                        param.toJsonString().jsonToObj<IMChatIntent>()?.let { intent ->
                            fragmentActivity?.castObject<FragmentActivity>()?.let {
                                MessageGiftDialog(intent.user_id, intent.channelId, intent.type)
                                    .show(
                                        it.supportFragmentManager,
                                        MessageGiftDialog::class.java.name
                                    )
                            }
                        }
                    }


                    ActionType.actionPrivacyImgPreview -> {
                        fragmentActivity?.also { activity ->
                            if (action.requestCode > 0) {
                                activity.startActivityKtxForResult<PrivacyImgActivity>({ intent ->
                                    intent.putExtra(Values.intentJsonParam, param?.toJsonString())
                                }, this.requestCode)
                            } else {
                                activity.startActivityKtxWithParam<PrivacyImgActivity> { intent ->
                                    intent.putExtra(Values.intentJsonParam, param.toJsonString())
                                }
                            }
                        }
                    }

                    ActionType.actionHomeLikes -> {
                        param?.toJsonString()?.jsonToObj<HomeLieksIntent>()?.also {
                            SimpleRxBus.post(UIEventHomeLikesTab(it.tab))
                        }
                    }

                    ActionType.actionPrivacyActivity -> {
                        fragmentActivity?.also {
                            if (action.requestCode > 0) {
                                it.startActivityKtxForResult<PrivacyListActivity>({ intent ->
                                    intent.putExtra(Values.intentJsonParam, param?.toJsonString())
                                }, this.requestCode)
                            } else {
                                context.startActivityKtxWithParam<PrivacyListActivity> { intent ->
                                    intent.putExtra(Values.intentJsonParam, param?.toJsonString())
                                }
                            }
                        }
                    }

                    ActionType.actionMediaPreview -> {
                        fragmentActivity?.startActivityKtxWithParam<MediaPreviewActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionTypeMain -> {
                        context.startActivityKtxWithParam<MainActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionTypeChat -> {
                        param?.toJsonString()?.jsonToObj<IMChatIntent>()
                            ?.takeIf { !it.user_id.isNullOrEmpty() }?.apply {
                                RouteUtils.routeToConversationActivity(
                                    context,
                                    type,
                                    user_id,
                                    false
                                );
                            }
                    }

                    ActionType.actionOtherProfile -> {
                        fragmentActivity?.startActivityKtxWithParam<OtherProfileActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionNewMatch -> {
                        param.toJsonString().jsonToObj<SysPushEntity>()?.run {
                            NewMatchDialog.showNotifyItem(this.userId)
                        }
                    }

                    ActionType.actionMessageSetting -> {
                        fragmentActivity?.startActivityKtxWithParam<MessageSettingActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionTaskHistoricalDetail -> {
                        fragmentActivity?.startActivityKtxWithParam<TaskHistoricalDetailActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionCourseList -> {
                        fragmentActivity?.startActivityKtxWithParam<CourseListActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionCourseSort -> {
                        fragmentActivity?.startActivityKtxWithParam<CourseSortActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionQuestionnaire -> {
                        fragmentActivity?.startActivityKtxWithParam<QuestionnaireActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionVideoPlay -> {
                        fragmentActivity?.startActivityKtxWithParam<VideoPlayActivity> {
                            it.putExtra(Values.intentStringParam, url)
                        }
                    }

                    ActionType.actionPrivacyMultipleSend -> {
                        fragmentActivity?.startActivityKtxWithParam<PrivacyMultipleSendActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionFindChats -> {
                        fragmentActivity?.startActivityKtxWithParam<FindChatsActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionTaskHistoricalList -> {
                        fragmentActivity?.startActivityKtxWithParam<TaskHistoricalListActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionNotificationSetting -> {
                        fragmentActivity?.startActivityKtxWithParam<NotificationActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionTrafficSetting -> {
                        fragmentActivity?.startActivityKtxWithParam<TrafficActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionTrafficRemindDialog -> {
                        param?.toJsonString()?.jsonToObj<MatchPolicyEntity>()?.apply {
                            fragmentActivity?.castObject<FragmentActivity>()?.let {
                                TrafficRemindDialog(this).show(
                                    it.supportFragmentManager,
                                    TrafficRemindDialog::javaClass.name
                                )
                            }
                        }
                    }

                    ActionType.actionRiskRemindDialog -> {
                        param?.toJsonString()?.jsonToObj<RiskRemindEntity>()?.apply {
                            fragmentActivity?.castObject<FragmentActivity>()?.let {
                                RiskRemindDialog(this).show(
                                    it.supportFragmentManager,
                                    RiskRemindDialog::javaClass.name
                                )
                            }
                        }
                    }

                    ActionType.actionRepossessionDialog -> {
                        fragmentActivity?.castObject<FragmentActivity>()?.let {
                            SPUtils.put(
                                AppContext.context,
                                UserController.attachAccount(Values.keyRepossessionNotice),
                                System.currentTimeMillis()
                            )
                            SPUtils.put(
                                AppContext.context,
                                UserController.attachAccount(Values.keyMPCNoticeRead),
                                true
                            )
                            RepossessionDialog().show(
                                it.supportFragmentManager,
                                RepossessionDialog::javaClass.name
                            )
                        }
                    }

                    ActionType.actionShiftStartDialog -> {
                        fragmentActivity?.castObject<FragmentActivity>()?.let {
                            CommonAlertDialog.Builder()
                                .setCancelable(true)
                                .setCancel("") {}
                                .setConfirm(it.getString(R.string.new_anchor_finish_ok)) {}
                                .setContextGravity(Gravity.CENTER)
                                .setTitle(it.getString(R.string.text_shift_start_title))
                                .setContext(it.getString(R.string.text_shift_start_content))
                                .create()
                                .show(it.supportFragmentManager, CommonAlertDialog::javaClass.name)
                        }
                    }

                    ActionType.actionMPC -> {
                        fragmentActivity?.startActivityKtxWithParam<MPCActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionPenalties -> {
                        fragmentActivity?.startActivityKtxWithParam<PenaltiesActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionCustomerService -> {
                        fragmentActivity?.startActivityKtxWithParam<CustomerServiceActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionOnlineSupport -> {
                        fragmentActivity?.startActivityKtxWithParam<OnlineSupportActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionVipSearch -> {
                        fragmentActivity?.startActivityKtxWithParam<VipContactSearchActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionSelectModel -> {
                        fragmentActivity?.startActivityKtxWithParam<SelectModelActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionBonusMatchHub -> {
                        fragmentActivity?.startActivityKtxWithParam<BonusMatchHubActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionNoticeDialog -> {
                        param?.toJsonString()?.jsonToObj<NoticeEntity>()?.apply {
                            fragmentActivity?.castObject<FragmentActivity>()?.let {
                                NoticeDialog(this).show(
                                    it.supportFragmentManager,
                                    null
                                )
                            }
                        }
                    }

                    ActionType.actionChatPenaltyDialog -> {
                        param?.toJsonString()?.jsonToObj<IMPenaltiesData>()?.apply {
                            fragmentActivity?.castObject<FragmentActivity>()?.let {
                                ChatPenaltyDialog(this).show(
                                    it.supportFragmentManager,
                                    null
                                )
                            }
                        }
                    }

                    ActionType.actionTaskFragment -> {
                        SimpleRxBus.post(EventTaskFragment())
                    }

                    ActionType.actionMsgAttentionDialog -> {
                        // 发送消息的被拦截时，弹窗提示
                        param?.toJsonString()?.jsonToObj<MsgAttentionIntent>()?.apply {
                            fragmentActivity?.castObject<FragmentActivity>()?.let {
                                if (it !is ChatActivity) return@apply
                                if (this.targetId.isNotEmpty() && this.targetId == ChatsConfig.currentChatImId) {
                                    MsgAttentionDialog(this).show(
                                        it.supportFragmentManager,
                                        MsgAttentionDialog::javaClass.name
                                    )
                                }
                            }
                        }
                    }

                    ActionType.actionNewAnchor -> {  //新主播引导
                        fragmentActivity?.startActivityKtxWithParam<NewAnchorGuideV2Activity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionTaskBindCode -> { //任务绑定邀请码
                        fragmentActivity?.startActivityKtxWithParam<BindInviteCodeActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionTaskCourseActive -> { //任务课程激活
                        fragmentActivity?.startActivityKtxWithParam<CourseActiveActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionTaskBankAccountActive -> { //薪资账号激活
                        fragmentActivity?.startActivityKtxWithParam<PaymentAccountActiveActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionInviteFriends -> {
                        fragmentActivity?.startActivityKtxWithParam<InviteFriendsActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionWithdraw -> {
                        fragmentActivity?.startActivityKtxWithParam<WithdrawActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionRewardDetail -> {
                        fragmentActivity?.startActivityKtxWithParam<RewardDetailActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionWithdrawDetail -> {
                        fragmentActivity?.startActivityKtxWithParam<WithdrawDetailActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionDeductDetail -> {
                        fragmentActivity?.startActivityKtxWithParam<DeductDetailActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionSchedule -> {
                        fragmentActivity?.startActivityKtxWithParam<ScheduleActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionScheduleRequest -> {
                        fragmentActivity?.startActivityKtxWithParam<ScheduleRequestActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionScheduleRequestLeave -> {
                        fragmentActivity?.startActivityKtxWithParam<RequestLeaveActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionScheduleShiftChange -> {
                        fragmentActivity?.startActivityKtxWithParam<ShiftChangeActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionScheduleLeaveRecords -> {
                        fragmentActivity?.startActivityKtxWithParam<LeaveRecordsActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionAnchorShift -> {
                        fragmentActivity?.startActivityKtxWithParam<NewAnchorShiftActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionCreateAccount -> {
                        fragmentActivity?.startActivityKtxWithParam<FillAccountInfoActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionDailyRank -> {
                        fragmentActivity?.startActivityKtxWithParam<DailyRankActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionFeatureGuide -> {
                        fragmentActivity?.startActivityKtxWithParam<FeatureGuideActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionNewbieCenter -> {
                        fragmentActivity?.startActivityKtxWithParam<NewbieCenterActivity> {
                            it.putExtra(Values.intentJsonParam, param.toJsonString())
                        }
                    }

                    ActionType.actionPassedWithdrawDialog -> {
                        // 试岗未通过去提现的提示弹窗
                        param?.toJsonString()?.jsonToObj<PassChatterWithdrawEntity>()?.apply {
                            fragmentActivity?.castObject<FragmentActivity>()?.let {
                                UnPassedJobDialog(this).show(
                                    it.supportFragmentManager,
                                    UnPassedJobDialog::javaClass.name
                                )
                            }
                        }
                    }

                    else -> {
                    }
                }
            }
        }

        return true
    }

    override fun handleActionTrace(action: ActionEntity?) {
        action?.takeIf { it.trace_id > 0 }?.also {
            //路由的回传
            CommonRepository.pushTrace(it.trace_id)
        }
    }

    companion object {
        val instance: ActionParser by lazy(LazyThreadSafetyMode.SYNCHRONIZED) { ActionParser() }
        val urlMatch: Pattern =
            Pattern.compile("^((art)|(http)|(https))://com\\.lianyu\\.dating\\?action=.*$")
    }

    private fun readResolve(): Any {
        return instance
    }

}