package com.fascin.chatter

import android.app.ActivityManager
import android.app.Application
import android.content.Context
import android.content.res.Configuration
import android.os.Process
import android.view.Gravity
import androidx.multidex.MultiDex
import com.fascin.chatter.component.player.SUHttpCacheServer
import com.fascin.chatter.im.IMModule
import com.fascin.chatter.route.ActionParser
import com.google.firebase.FirebaseApp
import com.iandroid.allclass.lib_basecore.ActivityStack
import com.iandroid.allclass.lib_basecore.dispatcher.AppStartUpDispatcher
import com.iandroid.allclass.lib_basecore.toast.Toaster
import com.iandroid.allclass.lib_basecore.utils.LanguageUtil
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.AppController
import com.iandroid.allclass.lib_common.upload.UploadImpl
import com.iandroid.allclass.lib_common.utils.exts.toPx
import com.iandroid.allclass.lib_thirdparty.AdjustSDK
import com.iandroid.allclass.lib_thirdparty.PassportSDK
import org.litepal.LitePal


/**
created by wangkm
on 2020/9/11.
 */
class ToolApplication : Application() {

    override fun attachBaseContext(base: Context?) {
        super.attachBaseContext(base)
        MultiDex.install(this)
    }

    override fun onCreate() {
        super.onCreate()
        AppContext.context = this.applicationContext
        FirebaseApp.initializeApp(this)
        if (isMainProcess(this)) {
            AppStartUpDispatcher.getInstance()
                .addBlocking("language_init", this::initLanguage)
                .addBlocking("application_init", this::initMainProcess)
                .addBlocking("im_init", this::initIM)
                .addBlocking("thridparty_init", this::initThirdParty)
                .addBlocking("httpCacheServer_init", this::initHttpCacheServer)
                .addBlocking("litePalDB_init", this::initLitePalDB)
                .addBlocking("toast_init", this::initToast)
                .execute()
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        // 如果是跟随系统需要重新设置
        if (LanguageUtil.onConfigurationChanged()) {
            ActivityStack.getInstance().reCreateAll()
        }
    }

    override fun onTerminate() {
        super.onTerminate()
        AppModule.exit()
        AppController.appExit()
        IMModule.instance.unInit()
    }

    //初始化IM
    private fun initIM() {
        IMModule.instance.initModule(this)
    }

    private fun initLanguage() {
        LanguageUtil.init(this)
    }

    private fun initMainProcess() {
        AppController.appStartup(
            this,
            162,
            "1.6.2",
            BuildConfig.CLIENT_NAME,
            BuildConfig.API_ENV,
            BuildConfig.APP_CHANNEL,
            IMModule.instance,
        )
        AppContext.iRouteParser = ActionParser.instance
        AppContext.iUploadHandle = UploadImpl.instance
        LifecycleMonitor.init()
        AppModule.init()

    }

    fun initThirdParty() {
        AdjustSDK.init(this)
        if (AppController.hasAgreedPrivacyAgreement()) {
            PassportSDK.instance.passportInit(this)
        }
    }

    private fun initHttpCacheServer() {
        SUHttpCacheServer.init(this)
    }

    private fun initLitePalDB() {
        LitePal.initialize(this)
    }

    private fun initToast() {
        Toaster.init(this)
        Toaster.setGravity(Gravity.BOTTOM, 0, 90.toPx)
    }

    /**
     * 包名判断是否为主进程
     */
    private fun isMainProcess(context: Context): Boolean {
        return context.packageName == getProcessName(context)
    }

    /**
     * 获取进程名称
     */
    private fun getProcessName(context: Context): String? {
        val am =
            context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val runningApps =
            am.runningAppProcesses ?: return null
        for (proInfo in runningApps) {
            if (proInfo.pid == Process.myPid()) {
                if (proInfo.processName != null) {
                    return proInfo.processName
                }
            }
        }
        return null
    }
}