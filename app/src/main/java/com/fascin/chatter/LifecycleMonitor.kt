package com.fascin.chatter

import android.util.Log
import androidx.fragment.app.FragmentActivity
import com.fascin.chatter.config.Config
import com.fascin.chatter.im.notice.FloatNoticeWindow
import com.fascin.chatter.repository.AppRepository
import com.fascin.chatter.utils.StatusHeartbeatManager
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.AppController
import com.iandroid.allclass.lib_common.LifecycleEvent
import com.iandroid.allclass.lib_common.beans.event.UIAppBackgroundEvent
import com.iandroid.allclass.lib_common.beans.event.UIAppForegroundEvent
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus

object LifecycleMonitor {

    private var activityStartedCount: Int = 0
    private var lastBackgroundTime: Long = -1

    fun init() {
        AppContext.register(LifecycleEvent.RESUME) { context ->
            (context as? FragmentActivity)?.also {
                FloatNoticeWindow.attach(it)
            }
        }
        AppContext.register(LifecycleEvent.RESUME) { context ->
            (context as? FragmentActivity)?.also { }
        }
        AppContext.register(LifecycleEvent.RESUME) { context ->
            (context as? FragmentActivity)?.also { }
        }

        AppContext.register(LifecycleEvent.CREATE) {
            Log.d("CommonWeb", "CREATE:" + it.javaClass.name)
        }

        AppContext.register(LifecycleEvent.DESTROY) {
            Log.d("CommonWeb", "DESTROY:" + it.javaClass.name)
        }

        AppContext.register(LifecycleEvent.STOP) {
            activityStartedCount--
            if (activityStartedCount == 0) {
                AppController.appInBackground = true
                lastBackgroundTime = System.currentTimeMillis()
                SimpleRxBus.post(UIAppBackgroundEvent())
                AppRepository.statusChange(Config.statusBackground)
                AppModule.closeActiveCheckTimer()
                StatusHeartbeatManager.stopHeartbeat()
            }
        }

        AppContext.register(LifecycleEvent.START) {
            AppController.appInBackground = false
            activityStartedCount++
            if (activityStartedCount == 1) {
                SimpleRxBus.post(UIAppForegroundEvent())
                AppRepository.statusChange(Config.statusforeground)
                AppModule.startUserActive()
                StatusHeartbeatManager.startHeartbeat()
            }
        }
    }
}