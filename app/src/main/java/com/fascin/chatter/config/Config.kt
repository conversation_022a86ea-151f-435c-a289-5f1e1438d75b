package com.fascin.chatter.config

object Config {
    //融云key
    const val rongCloudAppKey_Prod = "8brlm7uf8ygu3"
    const val rongCloudAppKey_Dev = "c9kqb3rdcftcj"

    const val email_reg =
        "^(?:[\\p{L}0-9!#$%\\&'*+/=?\\^_`{|}~-]+(?:\\.[\\p{L}0-9!#$%\\&'*+/=?\\^_`{|}~-]+)*|\"(?:[\\x01-\\x08\\x0b\\x0c\\x0e-\\x1f\\x21\\x23-\\x5b\\x5d-\\x7f]|\\\\[\\x01-\\x09\\x0b\\x0c\\x0e-\\x7f])*\")@(?:(?:[\\p{L}0-9](?:[a-z0-9-]*[\\p{L}0-9])?\\.)+[\\p{L}0-9](?:[\\p{L}0-9-]*[\\p{L}0-9])?|\\[(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|[\\p{L}0-9-]*[\\p{L}0-9]:(?:[\\x01-\\x08\\x0b\\x0c\\x0e-\\x1f\\x21-\\x5a\\x53-\\x7f]|\\\\[\\x01-\\x09\\x0b\\x0c\\x0e-\\x7f])+)\\])$"

    const val connectedSummary = "Your story started,chat now~"
    var userPass = 1        //用户pass
    var userLike = 2        //用户喜欢

    var userFromExplore = 1
    var userFromLikesWlm = 2
    var userFromLikeCupid = 3
    var userFromLikeILike = 4
    var userFromChat = 5
    var userFromUserPage = 6
    var userFromFlashChat = 7
    var userFromMatched = 8
    var userFromSysAction = 9

    var closeOnlinePush = 0x02  //端外
    var closeInAppPush = 0x01   //端内
    var closeLikeMsgPush = 0x04
    var closeCupidChatPush = 0x08

    var pushTypeWlm = 1
    var pushTypeCupidChat = 2
    var pushMatchedMsg = 3

    var RightTypeVip = 0
    var RightTypeFlashChat = 1
    var RightTypeVideo = 2

    var MediaImage = 0
    var MediaVideo = 1

    var privacyAllTabId = 0
    var privacyFavoriteTabId = -1

    //0 切到后台，1 切到前台 2激活，3 不活跃
    var statusBackground = 0
    var statusforeground = 1
    var statusActive = 2
    var statusNoActive = 3
    var statusExit = 4

    //接口返回活跃状态
    const val activeStatusNo = 0  //不活跃
    const val activeStatus = 1    //活跃
}