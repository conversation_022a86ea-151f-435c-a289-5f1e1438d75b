package com.fascin.chatter.utils

import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.concurrent.TimeUnit

object SUDateUtils {

    fun timestampToDateStr(timestamp: Long): String? = runCatching {
        val formatter = SimpleDateFormat("yyyy-MM-dd")
        val date = Date(timestamp * 1000)
        formatter.format(date)
    }.getOrNull()

    /**
     * 获取与当前时间的时间差
     * 如果时间差小于 1 小时（60 分钟），则以分钟为单位返回时间差。
     * 如果时间差小于 24 小时，则以小时为单位返回时间差。
     * 如果时间差小于 30 天，则以天为单位返回时间差。
     * 如果时间差大于或等于 30 天，则返回 "30d+"。
     */
    fun getTimeDifference(timestamp: Long): String {
        val currentTime = System.currentTimeMillis()
        val diff = currentTime - timestamp
        if (diff <= 0) return "1min"

        return when {
            diff < TimeUnit.HOURS.toMillis(1) -> {
                val minutes = TimeUnit.MILLISECONDS.toMinutes(diff).coerceAtLeast(1)
                buildString {
                    append(minutes)
                    if (minutes > 1) append(" mins")
                    else append(" min")
                }
            }

            diff < TimeUnit.DAYS.toMillis(1) -> {
                val hours = TimeUnit.MILLISECONDS.toHours(diff).coerceAtLeast(1)
                buildString {
                    append(hours)
                    if (hours > 1) append(" hours")
                    else append(" hour")
                }
            }

            diff < TimeUnit.DAYS.toMillis(30) -> {
                val days = TimeUnit.MILLISECONDS.toDays(diff).coerceAtLeast(1)
                buildString {
                    append(days)
                    if (days > 1) append(" days")
                    else append(" day")
                }
            }

            else -> {
                "30 days+"
            }
        }
    }

    /**
     * 秒时间戳转换为 09/08/2024 格式
     */
    fun convertTimestampToDate(timestamp: Long): String {
        val date = Date(timestamp * 1000)
        val sdf = SimpleDateFormat("MM/dd/yyyy", Locale.getDefault())
        return sdf.format(date)
    }
}