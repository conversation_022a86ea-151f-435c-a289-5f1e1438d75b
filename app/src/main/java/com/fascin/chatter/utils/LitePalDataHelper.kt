package com.fascin.chatter.utils

import com.fascin.chatter.bean.chat.PrivacyUnlockDBEntity
import com.fascin.chatter.bean.event.UIChatUnlockChangeEvent
import com.iandroid.allclass.lib_common.Values
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.exts.isSameDay
import org.litepal.LitePal
import java.util.concurrent.ConcurrentHashMap

/**
 * @Desc: LitePal数据操作
 * @Created: Quan
 * @Date: 2023/12/11
 */
object LitePalDataHelper {

    // 一级缓存
    private var unlockDataMaps: ConcurrentHashMap<String, PrivacyUnlockDBEntity> =
        ConcurrentHashMap<String, PrivacyUnlockDBEntity>()

    /**
     * 获取指定会话的解锁数据
     * 此为耗时操做，需在子线程中操作
     */
    suspend fun getPrivacyUnlockData(imId: String, block: (Exception) -> Unit = {}): PrivacyUnlockDBEntity {
        try {
            if (!unlockDataMaps.containsKey(imId)) {
                val dbEntity: List<PrivacyUnlockDBEntity> =
                    LitePal.where("targetId = ?", imId).find(PrivacyUnlockDBEntity::class.java)
                if (dbEntity.isNullOrEmpty()) {
                    unlockDataMaps[imId] = PrivacyUnlockDBEntity()
                } else {
                    unlockDataMaps[imId] = dbEntity[0]
                }
            }
        } catch (e: Exception) {
            block(e)
            unlockDataMaps[imId] = PrivacyUnlockDBEntity()
        }
        return unlockDataMaps[imId] ?: PrivacyUnlockDBEntity()
    }

    suspend fun savePrivacyUnlockData(
        todayPpvSend: Int,
        unlockNum: Int,
        yestUnlockNum: Int,
        todayPpvUnlock: Int,
        imId: String,
        isUpdateNew: Boolean = true
    ) {
        var privacyUnlockData = getPrivacyUnlockData(imId)
        if (privacyUnlockData == null)
            privacyUnlockData = PrivacyUnlockDBEntity()
        privacyUnlockData.also {
            it.targetId = imId
            if (isUpdateNew) {
                val isToday =
                    isSameDay(it.saveTime, System.currentTimeMillis(), Values.chatDefaultTimeZone)
                // 判断是否有新解锁:1.新解锁数比记录的大时 2.最后记录的是昨天时，又分为：a.新解锁数大于0时 b.昨天最近新解锁未查看时
                it.hasNewUnlock =
                    if (todayPpvUnlock > it.toDayUnlockPrivacyNum || (!isToday && (todayPpvUnlock > 0 || it.hasNewUnlock == 1))) 1 else 0
            }
            // -1代表有新解锁
            if (unlockNum == -1)
                it.unlockNum = it.unlockNum + (todayPpvUnlock - it.toDayUnlockPrivacyNum).coerceAtLeast(0)
            else
                it.unlockNum = unlockNum
            // 接口获取才更新
            if (unlockNum != -1)
                it.yestUnlockNum = yestUnlockNum
            it.toDayUnlockPrivacyNum = todayPpvUnlock
            it.toDaySendPrivacyNum = todayPpvSend
            it.saveTime = System.currentTimeMillis()
        }
        unlockDataMaps[imId] = privacyUnlockData
        privacyUnlockData.save()
        SimpleRxBus.post(UIChatUnlockChangeEvent(imId))
    }

    suspend fun resetHasNewUnlock(imId: String?) {
        imId?.let {
            val privacyUnlockData = getPrivacyUnlockData(it)
            privacyUnlockData?.also { data ->
                if (data.targetId.isNotEmpty()) {
                    data.hasNewUnlock = 0
                    unlockDataMaps[it] = data
                    data.save()
                }
            }
        }
    }
}