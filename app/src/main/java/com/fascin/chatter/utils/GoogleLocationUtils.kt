package com.fascin.chatter.utils

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.location.Location
import android.location.LocationManager
import android.net.Uri
import android.provider.Settings
import androidx.core.app.ActivityCompat
import androidx.fragment.app.FragmentActivity
import com.google.android.gms.location.LocationServices
import com.iandroid.allclass.lib_basecore.utils.PermissionCheckUtil
import com.iandroid.allclass.lib_common.AppContext


/**
 *  @author: LXL
 *  @description: Google Location
 *  @date: 2023/8/16 09:54
 */
object GoogleLocationUtils {

    private var locationManager: LocationManager? = null
    private var locationCallback: ((latitude: Double, longitude: Double) -> Unit)? = null

    fun checkLocationPermission(
        fragmentActivity: FragmentActivity,
        callback: (latitude: Double, longitude: Double) -> Unit
    ) {
        // 显示权限请求说明对话框，解释为什么需要该权限
        // 可以使用 AlertDialog 或自定义对话框来展示说明内容
        locationCallback = callback
        if (!PermissionCheckUtil.checkPermissions(
                fragmentActivity,
                arrayOf(Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION)
            )
        ) {//无权限，申请
            requestPermissions(fragmentActivity)
        } else {
            //有权限，直接加载数据
            userEnableLocation(true)
        }
    }

    fun userEnableLocation(enable: Boolean) {
        if (enable) {
            refreshLocation(AppContext.context)
        }
    }

    private fun refreshLocation(context: Context) {
        if (ActivityCompat.checkSelfPermission(
                context, Manifest.permission.ACCESS_FINE_LOCATION
            ) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(
                context, Manifest.permission.ACCESS_COARSE_LOCATION
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            return
        }

        locationManager = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager

        locationManager?.also {
            val lp: List<String> = it.allProviders
            if (lp.isNullOrEmpty()) {
                return
            }

            // GPS_PROVIDER
            if (lp.contains(LocationManager.GPS_PROVIDER)) {
                runCatching {
                    it.getLastKnownLocation(LocationManager.GPS_PROVIDER)?.also { location ->
                        getLocationData(location)
                        return
                    }
                }.onFailure { _ ->
                }
            }

            if (lp.contains(LocationManager.NETWORK_PROVIDER)) {
                runCatching {
                    it.getLastKnownLocation(LocationManager.NETWORK_PROVIDER)?.also { location ->
                        getLocationData(location)
                        return
                    }
                }.onFailure { _ ->
                    // keep listener for updating location every 5 mins
                }
            }

            //对某些手机前面两种方式获取不到经纬度，使用下面Google的方式获取
            runCatching {
                val fusedLocationClient = LocationServices.getFusedLocationProviderClient(context)
                fusedLocationClient.lastLocation
                    .addOnSuccessListener { location ->
                        location?.let {
                            getLocationData(location)
                        }
                    }
            }.onFailure { _ ->

            }
        }
    }

    private fun getLocationData(location: Location) {
        locationCallback?.invoke(location.latitude, location.longitude)
    }

    private fun requestPermissions(activity: Activity) {
        // 初次使用App时和永久拒绝时，shouldShowRequestPermissionRationale反的是false
        // 手动拒绝时，反的是true
        PermissionConst.locationPermissionLastRecord = ActivityCompat.shouldShowRequestPermissionRationale(
            activity,
            Manifest.permission.ACCESS_COARSE_LOCATION
        ) && ActivityCompat.shouldShowRequestPermissionRationale(
            activity,
            Manifest.permission.ACCESS_FINE_LOCATION
        )
        PermissionCheckUtil.requestPermissions(
            activity,
            arrayOf(Manifest.permission.ACCESS_COARSE_LOCATION, Manifest.permission.ACCESS_FINE_LOCATION),
            PermissionConst.REQUEST_CODE_LOCATION
        )
    }

    /**
     * 此方法需要在onRequestPermissionsResult中，监听到拒绝权限时调用
     * shouldShowRequestPermissionRationale，初次使用App时和永久拒绝时，反的是false
     * 手动拒绝时，反的是true
     */
    fun needJumpSetting(activity: Activity) {
        // 是本次才永久拒绝时，不跳
        if (PermissionConst.locationPermissionLastRecord) return
        // 上次已经永久拒绝，这次需要引导用户手动在应用设置中授予权限
        if (!ActivityCompat.shouldShowRequestPermissionRationale(
                activity,
                Manifest.permission.ACCESS_COARSE_LOCATION
            ) && !ActivityCompat.shouldShowRequestPermissionRationale(
                activity,
                Manifest.permission.ACCESS_FINE_LOCATION
            )
        ) {
            //开启定位权限
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
            val uri = Uri.fromParts("package", activity.packageName, null)
            intent.data = uri
            activity.startActivity(intent)
        }
    }
}