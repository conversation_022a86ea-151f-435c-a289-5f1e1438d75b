package com.fascin.chatter.utils;

/**
 * @Desc:
 * @Created: Quan
 * @Date: 2024/4/28
 */
public class PermissionConst {

    public static final int REQUEST_CODE_ASK_PERMISSIONS = 100;
    public static final int REQUEST_CODE_LOCATION_SHARE = 101;
    public static final int REQUEST_CODE_NOTIFY = 102;
    public static final int REQUEST_CODE_LOCATION = 103;

    // 上一次拒绝位置权限是否不是永久拒绝，false：永久拒绝
    public static boolean locationPermissionLastRecord = true;

    // 上一次拒绝通知权限是否不是永久拒绝，false：永久拒绝
    public static boolean notifyPermissionLastRecord = true;
}
