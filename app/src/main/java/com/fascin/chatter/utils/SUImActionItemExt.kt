package com.fascin.chatter.utils

import com.fascin.chatter.R
import com.fascin.chatter.bean.chat.UserImExtraEntity
import com.fascin.chatter.main.chats.ChatsConfig
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.AppController
import com.iandroid.allclass.lib_common.utils.GsonUtils.fromJson
import com.iandroid.allclass.lib_common.utils.exts.jsonToObj
import com.iandroid.allclass.lib_common.utils.exts.toJsonString
import io.rong.imkit.userinfo.RongUserInfoManager

/**
 *  @author: LXL
 *  @description: 长按或更多 开启关闭通知 文案、图标
 *  1.在线通知关闭、开启
 *  2.消息免打扰关闭、开启
 *  3.对用户隐身关闭、开启
 *  @date: 2023/12/8 14:20
 */

const val ConnectFlagInvisible: Long = 0x02 // 隐身
const val ConnectFlagNoOnline: Long = 0x04  // 上线通知
const val ConnectFlagMuteMsg: Long = 0x08 // 消息通知
const val ConnectFlagProhibition: Long = 0x100 //禁言

//开启
fun enableFlag(chatFlag: Long, flag: Long): Long {
    return chatFlag or flag
}

//关闭
fun disableFlag(chatFlag: Long, flag: Long): Long {
    return chatFlag and flag.inv()
}

//是否开启
fun isFlagEnabled(chatFlag: Long, flag: Long): Boolean {
    return chatFlag and flag != 0L
}

//接口请求更新开关状态
fun getChatFlag(chatFlag: Long, flag: Long): Long {
    return if (isFlagEnabled(chatFlag, flag)) {
        disableFlag(chatFlag, flag)
    } else {
        enableFlag(chatFlag, flag)
    }
}

data class ActionInfo(
    var chatFlag: Long,
    var offOnlineTitle: String,
    var offOnlineIcon: Int,
    var muteTitle: String,
    var muteIcon: Int,
    var invisibleTitle: String,
    var invisibleIcon: Int,
    var isPenalty: Boolean = false
)

/**
 * 刷新会话开关状态
 * 1.在线通知
 * 2.免打扰通知
 * 3.隐身状态
 */
fun refreshSwitchStatus(imUid: String?, chatFlag: Long) {
    RongUserInfoManager.getInstance().refreshUserInfoCache(
        RongUserInfoManager.getInstance().getUserInfo(imUid)
            .also { userInfo ->
                userInfo.extra = userInfo.extra.jsonToObj<UserImExtraEntity>()
                    .also { extra ->
                        extra?.chatFlag = chatFlag
                    }.toJsonString()
            }
    )
}

/**
 * 返回用户当前对应的开关状态的文案、图标
 */
fun getItemActionInfo(mTargetId: String?): ActionInfo {
    val userInfo = RongUserInfoManager.getInstance().getUserInfo(mTargetId)
    var offOnlineTitle = AppContext.getString(R.string.action_off_online)
    var offOnlineIcon = R.mipmap.ic_turn_online_off
    var muteTitle = AppContext.getString(R.string.action_mute_off)
    var muteIcon = R.mipmap.ic_turn_mute_off
    var invisibleTitle = AppContext.getString(R.string.action_visible_off)
    var invisibleIcon = R.mipmap.ic_turn_invisible_off
    var chatFlag = 0L
    var isPenalty = false
    userInfo?.takeIf { AppController.getNoticeAccount() != it.userId }?.extra?.apply {
        val userImExtra = fromJson(this, UserImExtraEntity::class.java)
        userImExtra?.apply {
            chatFlag = this.chatFlag
            offOnlineTitle = if (isFlagEnabled(chatFlag, ConnectFlagNoOnline))
                AppContext.getString(R.string.action_on_online) else AppContext.getString(R.string.action_off_online)
            offOnlineIcon = if (isFlagEnabled(chatFlag, ConnectFlagNoOnline))
                R.mipmap.ic_turn_online_on else R.mipmap.ic_turn_online_off

            muteTitle = if (isFlagEnabled(chatFlag, ConnectFlagMuteMsg))
                AppContext.getString(R.string.action_mute_on) else AppContext.getString(R.string.action_mute_off)
            muteIcon = if (isFlagEnabled(chatFlag, ConnectFlagMuteMsg))
                R.mipmap.ic_turn_mute_on else R.mipmap.ic_turn_mute_off

            invisibleTitle =
                if (isFlagEnabled(chatFlag, ConnectFlagInvisible) || isFlagEnabled(chatFlag, ConnectFlagProhibition))
                    AppContext.getString(R.string.action_visible_on) else AppContext.getString(R.string.action_visible_off)
            invisibleIcon =
                if (isFlagEnabled(chatFlag, ConnectFlagInvisible) || isFlagEnabled(chatFlag, ConnectFlagProhibition))
                    R.mipmap.ic_turn_invisible_on else R.mipmap.ic_turn_invisible_off
            isPenalty = isFlagEnabled(chatFlag, ConnectFlagProhibition)
        }
    }

    return ActionInfo(
        chatFlag, offOnlineTitle, offOnlineIcon,
        muteTitle, muteIcon,
        invisibleTitle, invisibleIcon,
        isPenalty
    )
}

/**
 * 对开启在线站内不通知
 */
fun chatFlagOnlineFilter(userId: String): Boolean {
    RongUserInfoManager.getInstance().getUserInfo(userId)?.extra?.apply {
        fromJson(this, UserImExtraEntity::class.java)?.apply {
            return isFlagEnabled(chatFlag, ConnectFlagNoOnline)
        }
    }
    return false
}

/**
 * 对开启免打扰站内不通知
 */
fun chatFlagMuteFilter(userId: String): Boolean {
    RongUserInfoManager.getInstance().getUserInfo(userId)?.extra?.apply {
        fromJson(this, UserImExtraEntity::class.java)?.apply {
            return isFlagEnabled(chatFlag, ConnectFlagMuteMsg)
        }
    }
    return false
}

/**
 * 对无法付费的用户新消息，站内不通知
 */
fun chatFlagCantPay(userId: String): Boolean {
    RongUserInfoManager.getInstance().getUserInfo(userId)?.extra?.apply {
        fromJson(this, UserImExtraEntity::class.java)?.apply {
            return this.isCantPayUser()
        }
    }
    return false
}

/**
 * 该model是否属于当前chatter
 */
fun isShieldModel(targetId: String?): Boolean {
    if (targetId.isNullOrEmpty()) return false
    val modelId = targetId.split("_")[0]
    return ChatsConfig.allModelIdAndHead.containsKey(modelId)
}