package com.fascin.chatter.utils

import android.os.Handler
import android.os.Looper
import com.fascin.chatter.repository.AppRepository
import com.iandroid.allclass.lib_common.UserController

/**
 *  @author: LXL
 *  @description: 状态心跳
 *  @date: 2024/9/4 15:01
 */
object StatusHeartbeatManager {
    private val handler = Handler(Looper.getMainLooper())
    private var runnable: Runnable? = null

    fun startHeartbeat() {
        if (UserController.hasLoggedIn()) {
            runnable = object : Runnable {
                override fun run() {
                    AppRepository.statusHeartbeat()
                    // 每隔1分钟执行一次
                    handler.postDelayed(this, 60000)
                }
            }
            handler.post(runnable!!)
        }
    }

    fun stopHeartbeat() {
        runnable?.let {
            handler.removeCallbacks(it)
        }
    }
}