package com.fascin.chatter.utils

import com.iandroid.allclass.lib_common.Values
import java.math.BigDecimal
import java.math.RoundingMode
import java.text.NumberFormat
import java.util.Locale

object SUStringUtils {

    fun getGenderStr(gender: Int): String {
        return when (gender) {
            Values.male -> "Male"
            Values.female -> "Female"
            else -> "Non-binary"
        }
    }

    fun getOrientStr(orient: Int): String {
        return when (orient) {
            Values.male -> "Man"
            Values.female -> "Woman"
            else -> "Other"
        }
    }

    fun formatBalance(balance: BigDecimal): String {
        return if (balance > BigDecimal(1_000_000)) {
            // 除以1000，保留2位小数，并缩写成K
            val scaledBalance = balance.divide(BigDecimal(1000)).setScale(2, RoundingMode.DOWN)
            formatWithComma(scaledBalance) + "K"
        } else {
            // 正常格式化，数字加逗号
            formatWithComma(balance)
        }
    }

    private fun formatWithComma(number: BigDecimal): String {
        // 使用 NumberFormat 格式化数字，逗号分隔
        val formatter = NumberFormat.getInstance(Locale.US)
        return formatter.format(number)
    }

    /**
     * 使用字符的 ASCII 值来计算字母
     */
    fun indexToABC(index: Int): String {
        return if (index in 0..25) {
            ('A' + index).toString()
        } else {
            "" // 超出范围返回 null
        }
    }

}