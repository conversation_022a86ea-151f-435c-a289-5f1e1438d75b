package com.fascin.chatter.component.views

import android.content.Context
import android.text.SpannableString
import android.util.AttributeSet
import android.util.TypedValue
import android.widget.FrameLayout
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import kotlinx.android.synthetic.main.view_right_arrow.view.tvContent
import kotlinx.android.synthetic.main.view_right_arrow.view.tvTitle

/**
 * @Desc:前、中、右侧箭头的item
 * @Created: Quan
 * @Date: 2024/11/19
 */
class RightArrowView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    init {
        inflate(context, com.fascin.chatter.R.layout.view_right_arrow, this)
    }

    fun setTitle(title: String) {
        tvTitle.text = title.orEmpty()
    }

    fun setTitle(title: SpannableString) {
        tvTitle.text = title
    }

    fun setContent(content: String) {
        tvContent.text = content.orEmpty()
        if (content.isNotEmpty())
            tvContent.setTextSize(TypedValue.COMPLEX_UNIT_SP, 17f)
    }

    fun setHintContent(hint: String) {
        tvContent.hint = hint.orEmpty()
        tvContent.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12f)
    }

    fun setContentClick(block: () -> Unit) {
        tvContent.clickWithTrigger {
            block.invoke()
        }
    }
}