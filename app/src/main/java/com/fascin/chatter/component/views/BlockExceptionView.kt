package com.fascin.chatter.component.views

import android.content.Context
import android.graphics.Rect
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.main.IRvItemAction
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RvItem
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.utils.DoubleUtils
import com.iandroid.allclass.lib_common.utils.exts.castObject
import kotlinx.android.synthetic.main.itemview_block_exception.view.*

/**
created by wangkm
on 2020/9/12.
 */
@RvItem(id = AppViewType.exceptionView, spanCount = 1)
class BlockExceptionView(context: Context, fm: FragmentManager, parent: ViewGroup) :
    BaseRvItemView(context, fm, parent) {

    override fun setView() {
        itemView?.run {
            retryActionBtn.setOnClickListener {
                if (!DoubleUtils.isFastDoubleClick()) {
                    getAction()?.onRefresh()
                }
            }
        }
    }

    private fun getItemData(): Any? = data?.castObject<Any>()

    override fun attachLayoutId(): Int {
        return R.layout.itemview_block_exception
    }

    override fun initView(context: Context?, view: View?) {
    }

    override fun getItemOffsets(
        recyclerView: RecyclerView,
        view: View,
        outRect: Rect,
        position: Int
    ): Boolean {
        return true
    }

    private fun getAction(): IRvItemAction? {
        return info?.callBack?.castObject<IRvItemAction>()
    }
}