package com.fascin.chatter.component.views.swipe

import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.iandroid.allclass.lib_common.utils.exts.show

class SwipeLayoutManager(
    private val onTouchCallback: (View) -> Unit
) : RecyclerView.LayoutManager() {

    override fun generateDefaultLayoutParams(): RecyclerView.LayoutParams {
        return RecyclerView.LayoutParams(
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
    }

    override fun onLayoutChildren(recycler: RecyclerView.Recycler, state: RecyclerView.State?) {
        super.onLayoutChildren(recycler, state)
        detachAndScrapAttachedViews(recycler)
        Log.d("<PERSON>", "onLayoutChildren：$itemCount")
        val itemCount = itemCount.coerceAtMost(ATTACH_ITEM_COUNT)
        repeat(itemCount) {
            val position = itemCount - it - 1
            Log.d("David", "onLayoutChildren--position：" + position)
            val view = recycler.getViewForPosition(position)
            addView(view)
            measureChildWithMargins(view, 0, 0)
            val widthSpace = width - getDecoratedMeasuredWidth(view)
            val heightSpace = height - getDecoratedMeasuredHeight(view)

            layoutDecoratedWithMargins(
                view, widthSpace / 2, heightSpace / 2,
                widthSpace / 2 + getDecoratedMeasuredWidth(view),
                heightSpace / 2 + getDecoratedMeasuredHeight(view)
            )
            if (position > 0) {
//                view.show(false, invisible = true)
                view.scaleX = DEFAULT_BACK_SCALE
                view.scaleY = DEFAULT_BACK_SCALE
                view.rotation = 0F
                view.alpha = 0F
            } else {
                view.show(true)
                view.scaleX = 1F
                view.scaleY = 1F
                view.alpha = 1F
                view.rotation = 0F
                view.setOnTouchListener { v, e ->
                    Log.d("David", "setOnTouchListener: ${e.actionMasked}")
                    when (e.actionMasked) {
                        MotionEvent.ACTION_DOWN -> {
                            onTouchCallback?.apply { (v) }
                        }
                    }
                    false
                }
            }

        }
    }

    companion object {
        const val ATTACH_ITEM_COUNT = 2
        const val DEFAULT_BACK_SCALE = 0.95F
        const val DEFAULT_ROTATE_DEGREE = 20F
        const val MAX_SWIPE_RATIO = 2.5F
        const val MIN_SWIPE_RATIO = 0.0F
        const val DEFAULT_PIVOT_OFFSET = 10
        const val DONEVIEW_START_ALPHA = 0.35F
        const val DONEVIEW_MIN_SCALE = 0.65F
        const val DONEVIEW_MAX_SCALE = 1.8F
    }
}
