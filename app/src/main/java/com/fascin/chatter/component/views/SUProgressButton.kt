package com.fascin.chatter.component.views

import android.content.Context
import android.content.res.ColorStateList
import android.graphics.Color
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import androidx.annotation.ColorInt
import com.fascin.chatter.R
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.toPx
import kotlinx.android.synthetic.main.item_progress_button.view.progress_button_background
import kotlinx.android.synthetic.main.item_progress_button.view.progress_button_loading
import kotlinx.android.synthetic.main.item_progress_button.view.progress_button_text
import androidx.core.content.withStyledAttributes


class SUProgressButton @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private var buttonStatus: SUButtonStatus = SUButtonStatus.Disabled

    init {
        View.inflate(context, R.layout.item_progress_button, this)
        setButtonStatus(SUButtonStatus.Disabled)
    }

    fun setText(text: CharSequence) {
        progress_button_text.text = text
    }

    fun setViewHeight(height: Int) {
        val layoutParams = progress_button_background.layoutParams?.also {
            it.height = height.toPx
        }
        progress_button_background.layoutParams = layoutParams
    }

    fun setButtonStatus(
        status: SUButtonStatus,
        disabledBg: Int = R.drawable.bg_progress_button_disable,
        disabledColor: String = "#BFBFBF"
    ) {
        buttonStatus = status
        when (status) {
            SUButtonStatus.Disabled -> {
                isEnabled = false
                progress_button_loading.show(false)
                progress_button_text.setTextColor(Color.parseColor(disabledColor))
                progress_button_background.setBackgroundResource(disabledBg)
            }

            SUButtonStatus.Loading -> {
                isEnabled = false
                progress_button_loading.show(true)
                progress_button_text.setTextColor(Color.WHITE)
                progress_button_background.setBackgroundResource(R.drawable.bg_progress_button_loading)
            }

            SUButtonStatus.Activated -> {
                isEnabled = true
                progress_button_loading.show(false)
                progress_button_text.setTextColor(Color.WHITE)
                progress_button_background.setBackgroundResource(R.drawable.bg_progress_button_active)
            }
        }
    }

    fun setButtonStatus(
        status: SUButtonStatus,
        disabledBg: Int = R.drawable.bg_progress_button_disable,
        loadingBg: Int = R.drawable.bg_progress_button_loading,
        activatedBg: Int = R.drawable.bg_progress_button_active,
        disabledColor: String = "#BFBFBF"
    ) {
        buttonStatus = status
        when (status) {
            SUButtonStatus.Disabled -> {
                isEnabled = false
                progress_button_loading.show(false)
                progress_button_text.setTextColor(Color.parseColor(disabledColor))
                progress_button_background.setBackgroundResource(disabledBg)
            }

            SUButtonStatus.Loading -> {
                isEnabled = false
                progress_button_loading.show(true)
                progress_button_text.setTextColor(Color.WHITE)
                progress_button_background.setBackgroundResource(loadingBg)
            }

            SUButtonStatus.Activated -> {
                isEnabled = true
                progress_button_loading.show(false)
                progress_button_text.setTextColor(Color.WHITE)
                progress_button_background.setBackgroundResource(activatedBg)
            }
        }
    }

    fun getButtonStatus(): SUButtonStatus = buttonStatus

    fun isLoadingStatus(): Boolean = SUButtonStatus.Loading == buttonStatus
}

enum class SUButtonStatus {
    Disabled,
    Loading,
    Activated
}