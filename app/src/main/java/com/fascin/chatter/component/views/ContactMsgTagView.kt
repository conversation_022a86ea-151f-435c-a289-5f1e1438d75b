package com.fascin.chatter.component.views

import android.content.Context
import android.graphics.Typeface
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import androidx.core.content.res.ResourcesCompat
import com.fascin.chatter.R
import com.fascin.chatter.main.chats.ChatsConfig
import com.iandroid.allclass.lib_common.utils.exts.textColorResource
import com.iandroid.allclass.lib_common.utils.exts.toPx
import kotlinx.android.synthetic.main.view_contact_msg_tag.view.ivTag
import kotlinx.android.synthetic.main.view_contact_msg_tag.view.llTag
import kotlinx.android.synthetic.main.view_contact_msg_tag.view.tvTag

/**
 * @Desc: 当前会话的状态
 * @Created: Quan
 * @Date: 2024/6/4
 */
class ContactMsgTagView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private var boldTypeface: Typeface? = null
    private var typeface: Typeface? = null

    init {
        View.inflate(context, R.layout.view_contact_msg_tag, this)
        typeface = ResourcesCompat.getFont(context, com.iandroid.allclass.lib_basecore.R.font.gilroy_medium)
        boldTypeface = ResourcesCompat.getFont(context, com.iandroid.allclass.lib_basecore.R.font.gilroy_semibold)
    }

    fun setChatFlag(flag: Int) {
        llTag.setPadding(0, 0, 0, 0)
        llTag.setBackgroundResource(0)
        when (flag) {
            ChatsConfig.FlagMatched -> {
                // 仅建联
                ivTag.setImageResource(R.drawable.ic_contact_matched)
                tvTag.text = "Matched"
                tvTag.textColorResource = com.iandroid.allclass.lib_common.R.color.cr_8c8c8c
                tvTag.typeface = typeface
            }

            ChatsConfig.FlagDelivered -> {
                // 发送过去的
                ivTag.setImageResource(R.drawable.ic_contact_delivered)
                tvTag.text = "Sent"
                tvTag.textColorResource = com.iandroid.allclass.lib_common.R.color.cr_8c8c8c
                tvTag.typeface = typeface
            }

            ChatsConfig.FlagReceived -> {
                // 接收到的
                llTag.setPadding(6.toPx, 0, 6.toPx, 0)
                llTag.setBackgroundResource(R.drawable.bg_fa4740_r99)
                ivTag.setImageResource(R.drawable.ic_contact_received)
                //TODO Your Turn change by mask
                tvTag.text = "you're up"
                tvTag.textColorResource = R.color.white
                tvTag.typeface = boldTypeface
            }

            ChatsConfig.FlagUnlocked -> {
                // 有新解锁的
                ivTag.setImageResource(R.drawable.ic_contact_unlocked)
                tvTag.text = "New unlock"
                tvTag.textColorResource = com.iandroid.allclass.lib_common.R.color.color_F74E57
                tvTag.typeface = boldTypeface
            }

            ChatsConfig.FlagNewMsg -> {
                // 有未读消息
                ivTag.setImageResource(R.drawable.ic_contact_unread)
                //TODO New Message change by mask
                tvTag.text = "[New msg]"
                tvTag.textColorResource = com.iandroid.allclass.lib_common.R.color.color_F74E57
                tvTag.typeface = boldTypeface
            }

            else -> {
                ivTag.setImageResource(R.drawable.ic_contact_matched)
                tvTag.text = "Matched"
                tvTag.textColorResource = com.iandroid.allclass.lib_common.R.color.cr_8c8c8c
                tvTag.typeface = typeface
            }
        }
    }
}