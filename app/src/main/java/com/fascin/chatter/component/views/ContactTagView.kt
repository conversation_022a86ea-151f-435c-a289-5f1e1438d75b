package com.fascin.chatter.component.views

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import com.fascin.chatter.R
import com.fascin.chatter.main.chats.ChatsConfig
import com.fascin.chatter.main.view.ContactTagSelDialog
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import kotlinx.android.synthetic.main.view_contact_tag.view.ivTag

/**
 * @Desc: 添加会话标记用的
 * @Created: Quan
 * @Date: 2024/6/4
 */
class ContactTagView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private var userName: String = ""
    private var targetId: String = ""
    private var curTagId: Int = ChatsConfig.TAG_NONE

    init {
        View.inflate(context, R.layout.view_contact_tag, this)
        clickWithTrigger {
            AppContext.getTopFragmentActivity()?.also { fragmentActivity ->
                ContactTagSelDialog(targetId, curTagId, userName).show(
                    fragmentActivity.supportFragmentManager,
                    ContactTagSelDialog::class.java.name
                )
            }
        }
    }

    fun setTagUI(targetId: String, tagId: Int, userName: String) {
        this.targetId = targetId
        this.curTagId = tagId
        this.userName = userName
        when (tagId) {
            ChatsConfig.TAG_NONE -> {
                ivTag.setImageResource(R.drawable.ic_contact_tag_sel)
                visibility = View.GONE
            }

            ChatsConfig.TAG_MONEY -> {
                ivTag.setImageResource(R.drawable.ic_contact_tag_money)
                visibility = View.VISIBLE
            }

            ChatsConfig.TAG_HOT -> {
                ivTag.setImageResource(R.drawable.ic_contact_tag_hot)
                visibility = View.VISIBLE
            }

            ChatsConfig.TAG_HEART -> {
                ivTag.setImageResource(R.drawable.ic_contact_tag_heart)
                visibility = View.VISIBLE
            }

            ChatsConfig.TAG_TURTLE -> {
                ivTag.setImageResource(R.drawable.ic_contact_tag_turtle)
                visibility = View.VISIBLE
            }

            else -> {
                ivTag.setImageResource(0)
                visibility = View.GONE
            }
        }
    }
}