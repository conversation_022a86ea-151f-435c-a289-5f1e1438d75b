package com.fascin.chatter.component.views.swipe

import android.view.View
import android.view.animation.Animation
import android.view.animation.Animation.AnimationListener
import android.view.animation.LinearInterpolator
import android.view.animation.Transformation
import android.widget.ImageView
import androidx.recyclerview.widget.ItemTouchHelper
import com.fascin.chatter.R
import com.fascin.chatter.component.views.swipe.SwipeLayoutManager.Companion.DONEVIEW_MAX_SCALE
import com.fascin.chatter.component.views.swipe.SwipeLayoutManager.Companion.DONEVIEW_MIN_SCALE
import com.fascin.chatter.component.views.swipe.SwipeLayoutManager.Companion.DONEVIEW_START_ALPHA
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.toPx
import kotlin.math.abs

class SwipeItemAnimation(
    private val frontView: View,
    private val backView: View?,
    private val exploredoneView: ImageView?,
    private val direction: Int,
    private val animationEnd: () -> Unit
) : Animation(), AnimationListener {
    init {
        this.duration = 600L
        this.interpolator = LinearInterpolator()
        frontView.pivotX = frontView.width / 2F
        frontView.pivotY = frontView.height.toFloat() + SwipeLayoutManager.DEFAULT_PIVOT_OFFSET.toPx
        backView?.scaleX = SwipeLayoutManager.DEFAULT_BACK_SCALE
        backView?.scaleY = SwipeLayoutManager.DEFAULT_BACK_SCALE
        backView?.show(true)

        exploredoneView?.setImageResource(
            if (direction == ItemTouchHelper.RIGHT) R.mipmap.ic_like_big else R.mipmap.ic_pass_big
        )

        setAnimationListener(this)
    }

    fun play() {
        frontView.startAnimation(this)
    }

    fun stop() {
        frontView.clearAnimation()
    }

    override fun applyTransformation(interpolatedTime: Float, t: Transformation?) {
        if (interpolatedTime == 0F) {
            frontView.rotation = 0F
        }
        val factor = if (direction == ItemTouchHelper.RIGHT) 1F else -1F

        frontView.rotation =
            factor * (2.5F * SwipeLayoutManager.DEFAULT_ROTATE_DEGREE) * interpolatedTime
        if (interpolatedTime > 0.4F) {
            (0.8 - interpolatedTime) / 0.4
            frontView.alpha = ((0.8F - interpolatedTime) / 0.4F).coerceAtLeast(0F)
            backView?.scaleX = 1F
            backView?.scaleY = 1F
            backView?.alpha = 1F
        } else {
            val defaultScale = SwipeLayoutManager.DEFAULT_BACK_SCALE
            val ratio = interpolatedTime / 0.4F
            backView?.alpha = ratio
            backView?.scaleX = defaultScale + (1F - defaultScale) * abs(ratio)
            backView?.scaleY = defaultScale + (1F - defaultScale) * abs(ratio)
        }

        if (interpolatedTime >= 1.0F) {
            exploredoneView?.show(false)
            exploredoneView?.alpha = 0.0F
        } else if (interpolatedTime == 0.0f) {
            exploredoneView?.alpha = DONEVIEW_START_ALPHA
            exploredoneView?.show(false)
        } else {
            exploredoneView?.show(true)
        }

        exploredoneView?.alpha = abs(interpolatedTime) + DONEVIEW_START_ALPHA
        exploredoneView?.scaleX =
            DONEVIEW_MIN_SCALE.coerceAtLeast(DONEVIEW_MAX_SCALE.coerceAtMost(abs(interpolatedTime) + DONEVIEW_MIN_SCALE))
        exploredoneView?.scaleY =
            DONEVIEW_MIN_SCALE.coerceAtLeast(DONEVIEW_MAX_SCALE.coerceAtMost(abs(interpolatedTime) + DONEVIEW_MIN_SCALE))
    }

    override fun onAnimationStart(animation: Animation) {

    }

    override fun onAnimationEnd(animation: Animation) {
        exploredoneView?.show(false)
        exploredoneView?.alpha = 0.0F
        animationEnd()
    }

    override fun onAnimationRepeat(animation: Animation) {

    }
}