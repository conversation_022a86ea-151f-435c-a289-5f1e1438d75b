package com.fascin.chatter.component.views

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.math.abs

class AutoRecycleView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    private var initialXPress = 0f
    private var initialYPress = 0f
    private var isUP: AtomicBoolean = AtomicBoolean(false)
    var clickCallback: ((Int, String) -> Unit?)? = null

    override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
        when (ev.actionMasked) {
            MotionEvent.ACTION_DOWN -> {
                isUP.set(true)
                initialXPress = ev.x
                initialYPress = ev.y
            }

            MotionEvent.ACTION_MOVE -> {
                val xMove: Float = ev.x
                val yMove: Float = ev.y
                val dx = xMove - initialXPress
                val dy = yMove - initialYPress
                if (abs(dx + dy) > 10) {
                    isUP.set(false)
                }
            }

            MotionEvent.ACTION_UP -> {
                if (isUP.compareAndSet(true, false)) {
                    val childView: View? =
                        this.findChildViewUnder(ev.x, ev.y)
                    if (null != childView) {
                        val position = this.getChildAdapterPosition(childView)
                        val autoAdapter = adapter as AutoGreetScrollView.AutoAdapter
                        autoAdapter.getData()[position]?.let {
                            clickCallback?.invoke(
                                it.id,
                                it.getContentStr()
                            )
                        }
                    }
                }
            }
        }
        return super.dispatchTouchEvent(ev)
    }
}