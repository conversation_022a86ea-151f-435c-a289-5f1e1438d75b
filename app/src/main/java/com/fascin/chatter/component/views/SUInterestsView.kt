package com.fascin.chatter.component.views

import android.content.Context
import android.graphics.Typeface
import android.util.AttributeSet
import android.util.TypedValue
import android.view.Gravity
import androidx.core.content.res.ResourcesCompat
import com.fascin.chatter.R
import com.iandroid.allclass.lib_basecore.view.FontTextView
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.utils.exts.toPx
import com.iandroid.allclass.lib_common.widgets.FlowLayout

class SUInterestsView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet,
) : FlowLayout(context, attrs) {


    init {
        setHorizontalChildGap(8.toPx)
        setMaxWidthReservedSpace(20.toPx)
        setIsSingleLineWrapWidth(true)
    }

    fun setDataSource(
        tags: List<String>?,
        backGroundRes: Int = R.drawable.bg_tag_nor,
        textColorRes: Int = com.iandroid.allclass.lib_common.R.color.cl_000000_75,
        textSize: Float = 14F,
        verticalPadding: Int = 6.toPx,
        horizontalPadding: Int = 10.toPx
    ) {
        removeAllViews()
        tags?.forEach {
            addView(
                FontTextView(context).apply {
                    val typeface: Typeface? = ResourcesCompat.getFont(context, com.iandroid.allclass.lib_basecore.R.font.gilroy_bold)
                    this.typeface =typeface
                    setTextColor(AppContext.getColor(textColorRes))
                    setBackgroundResource(backGroundRes)
                    gravity = Gravity.CENTER
                    isSingleLine = true
                    setPadding(
                        horizontalPadding,
                        verticalPadding,
                        horizontalPadding,
                        verticalPadding
                    )
                    text = it
                    setTextSize(TypedValue.COMPLEX_UNIT_DIP, textSize)
                }
            )
        }
    }
}