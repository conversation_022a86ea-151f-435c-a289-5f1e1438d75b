package com.fascin.chatter.component.views

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.net.Uri
import android.util.AttributeSet
import android.util.Log
import android.view.View
import android.widget.FrameLayout
import androidx.constraintlayout.widget.ConstraintLayout
import com.fascin.chatter.R
import com.fascin.chatter.bean.ProfilePhotoCell
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.GlideLoader.loadImage
import com.iandroid.allclass.lib_common.Values
import com.iandroid.allclass.lib_common.beans.MediaEntity
import com.iandroid.allclass.lib_common.beans.UpLoadImgBean
import com.iandroid.allclass.lib_common.event.EventImgUpLoad
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.upload.UploadImpl
import com.iandroid.allclass.lib_common.utils.AlbumUtils
import com.iandroid.allclass.lib_common.utils.GsonUtils
import com.iandroid.allclass.lib_common.utils.PermissionUtils
import com.iandroid.allclass.lib_common.utils.ToastUtils
import com.iandroid.allclass.lib_common.utils.exts.castObject
import com.iandroid.allclass.lib_common.utils.exts.jsonToObj
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.toJsonString
import io.reactivex.disposables.Disposable
import io.rong.imkit.config.RongConfigCenter
import io.rong.imkit.picture.PictureSelector
import io.rong.imkit.picture.config.PictureMimeType
import io.rong.imkit.utils.PermissionCheckUtil
import io.rong.imkit.utils.RongUtils
import io.rong.imlib.RongIMClient
import kotlinx.android.synthetic.main.item_photos_edit.view.*

class SUPhotoEditView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {


    private val photoImageViews by lazy {
        listOf(
            profile_photos_image1,
            profile_photos_image2,
            profile_photos_image3,
            profile_photos_image4,
            profile_photos_image5,
            profile_photos_image6
        )
    }

    private val photoOptionViews by lazy {
        listOf(
            profile_photos_image1_edit,
            profile_photos_image2_delete,
            profile_photos_image3_delete,
            profile_photos_image4_delete,
            profile_photos_image5_delete,
            profile_photos_image6_delete
        )
    }

    private var uploadEventDisposable: Disposable? = null
    private var targetImageIndex = -1
    private var autoSaveCallback: ((String) -> Unit)? = null

    init {
        View.inflate(context, R.layout.item_photos_edit, this)

        photoImageViews.forEachIndexed { index, view ->
            view.setOnClickListener {
                targetImageIndex = getNextEmptyIndex(index)
                checkPermissionsAndPickPhoto()
            }
            if (index > 0) {
                photoOptionViews[index].setOnClickListener {
                    putPhotoCellData(index, null)
                    view.setImageResource(R.mipmap.bg_photos_placeholder)
                    refreshPhotoImageViews()
                    autoSaveCallback?.invoke(getUploadedMediaListStr())
                }
            }
        }

        SimpleRxBus.observe(EventImgUpLoad::class) {
            uploadResultCallback(it.url, it.uploadBean?.data.castObject<ProfilePhotoCell>())

            val index = runCatching {
                Integer.parseInt(it.uploadBean?.data.toString())
            }.getOrNull() ?: return@observe
            if (index >= 0 && index < photoImageViews.size) {
                photoImageViews[index].tag = it.url
                photoImageViews[index].loadImage(context, it.url)
                refreshPhotoImageViews()
                autoSaveCallback?.invoke(getUploadedMediaListStr())
            }
        }.also { uploadEventDisposable = it }
    }

    private fun uploadResultCallback(url: String?, data: ProfilePhotoCell?) {
        profile_photos_progress.show(false)

        if (data != null) {
            photoImageViews[data.index].isEnabled = true
            photoOptionViews[data.index].isEnabled = true
        }

        if (url.isNullOrEmpty() || data == null) {
            Log.d("upload", "上传失败")
            ToastUtils.showToast(com.iandroid.allclass.lib_common.R.string.upload_fail)
        } else {
            data.url = url
            putPhotoCellData(data.index, data)
            Log.d("upload", "上传成功:${url}")
            autoSaveCallback?.invoke(getUploadedMediaListStr())
        }
    }

    fun initPhotos(mediaList: List<MediaEntity>) {
        mediaList.forEachIndexed { i, entity ->
            ProfilePhotoCell()
            if (i < photoImageViews.size) {
                photoImageViews[i].tag = entity.toJsonString()
                photoImageViews[i].loadImage(context, entity.url)
            }
        }
        refreshPhotoImageViews()
    }


    fun setAutoSaveCallback(autoSaveCallback: (String) -> Unit) {
        this.autoSaveCallback = autoSaveCallback
    }

    fun getUploadedMediaListStr(): String {
        val imageList = mutableListOf<MediaEntity>()
        photoImageViews.forEach { image ->
            image.tag?.toString()?.jsonToObj<ProfilePhotoCell>()?.takeIf { !it.url.isNullOrEmpty() }
                ?.also {
                    imageList.add(MediaEntity().apply {
                        this.url = it.url.orEmpty()
                        this.type = it.type
                    })
                }
        }
        return GsonUtils.toJson(imageList)
    }


    private fun refreshPhotoImageViews() {
        photoImageViews.forEachIndexed { index, image ->
            photoOptionViews[index].show(image.tag != null)
        }
    }

    private fun checkPermissionsAndPickPhoto() {
        // KNOTE: 2021/8/25 CAMERA权限进入图库后点击拍照时申请
        var permissions = if (RongUtils.checkSDKVersionAndTargetIsTIRAMISU(context)) {
            arrayOf(Manifest.permission.READ_MEDIA_IMAGES, Manifest.permission.READ_MEDIA_VIDEO)
        } else {
            arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE)
        }
        if (PermissionCheckUtil.checkPermissions(context, permissions)) {
            openPictureSelector()
        } else {
            PermissionCheckUtil.requestPermissions(
                AppContext.getTopActivity(),
                permissions,
                PermissionUtils.REQUEST_ALBUM_CODE
            )
        }
    }

    private fun openPictureSelector() {
        var showVideo = RongConfigCenter.conversationConfig().rc_media_selector_contain_video
        if (targetImageIndex == 0) {
            showVideo = false
        }

        PictureSelector.create(AppContext.getTopActivity())
            .openGallery(
                if (showVideo) PictureMimeType.ofAll() else PictureMimeType.ofImage()
            )
            .loadImageEngine(RongConfigCenter.featureConfig().kitImageEngine)
            .setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT)
            .videoDurationLimit(RongIMClient.getInstance().videoLimitTime)
            .maxSelectNum(1)
            .actionUse(1)
            .videoDurationLimit(15)
            .previewVideo(true)
            .crop(1)
            .imageSpanCount(3)
            .isGif(true)
            .forResult(Values.waitcode_choosepic_from_album)
    }


    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        uploadEventDisposable?.takeIf { !it.isDisposed }?.dispose()
    }

    private fun getNextEmptyIndex(clickIndex: Int): Int {
        var clickItemData = getPhotoCellData(clickIndex)
        if (clickItemData?.url != null) {
            return clickIndex
        }

        photoImageViews.forEachIndexed { index, _ ->
            var itemData = getPhotoCellData(index)
            if (itemData == null || itemData.url.isNullOrEmpty()) {
                return index
            }
        }
        return 0
    }

    private fun getPhotoCellData(index: Int): ProfilePhotoCell? {
        if (index < 0 || index >= photoImageViews.size || photoImageViews[index].tag == null) return null
        return photoImageViews[index].tag?.toString()?.jsonToObj<ProfilePhotoCell>()
    }

    private fun putPhotoCellData(index: Int, data: ProfilePhotoCell?) {
        if (index < 0 || index >= photoImageViews.size) return

        if (data == null) {
            photoImageViews[index].tag = null
        } else {
            photoImageViews[index].tag = data.toJsonString()
        }
    }

    fun onActivityResult(requestCode: Int, data: Intent?) {
        if (requestCode == Values.waitcode_choosepic_from_album) {
            // 图片、视频、音频选择结果回调
            val selectList = PictureSelector.obtainMultipleResult(data)
            if (selectList != null && selectList.size > 0) {
                for (item in selectList) {
                    val mimeType = item.mimeType
                    if (mimeType.startsWith("image")) {
                        putPhotoCellData(targetImageIndex, ProfilePhotoCell().also {
                            it.url = item.path
                            it.mime_type = item.mimeType
                            it.type = 0
                            it.index = targetImageIndex
                        })
                        photoImageViews[targetImageIndex].loadImage(context, item.path)
                        refreshPhotoImageViews()
                        startUpLoadFile(targetImageIndex)
                    } else if (mimeType.startsWith("video")) {
                        putPhotoCellData(targetImageIndex, ProfilePhotoCell().also {
                            it.url = item.path
                            it.mime_type = item.mimeType
                            it.type = 1
                            it.index = targetImageIndex
                        })
                        photoImageViews[targetImageIndex].loadImage(context, item.path)
                        refreshPhotoImageViews()
                        startUpLoadFile(targetImageIndex)
                    }
                }
            }
        }
    }

    private fun startUpLoadFile(position: Int) {
        photoImageViews[position]?.also { image ->
            image.tag?.toString()?.jsonToObj<ProfilePhotoCell>()?.also {
                if (!it.url.isNullOrEmpty() && !it.url.orEmpty().toLowerCase()
                        .startsWith("https://")
                ) {
                    //显示进度
                    profile_photos_progress?.layoutParams?.castObject<ConstraintLayout.LayoutParams>()
                        ?.also { layout ->
                            layout.endToEnd = image.id
                            layout.startToStart = image.id
                            layout.topToTop = image.id
                            layout.bottomToBottom = image.id
                            profile_photos_progress.layoutParams = layout
                        }
                    profile_photos_progress.show(true)

                    var path = AlbumUtils.getPath(context, Uri.parse(it.url))
                    UploadImpl.instance.uploadImage(
                        path,
                        UpLoadImgBean(it.mime_type.orEmpty()).apply {
                            data = it
                        })

                    image.isEnabled = false
                    photoOptionViews[position].isEnabled = false
                    Log.d("upload", "开始上传:${it.url}, $path")
                    return
                }
            }
        }
    }
}