package com.fascin.chatter.component.views

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import com.fascin.chatter.R
import com.iandroid.allclass.lib_common.utils.exts.show
import kotlinx.android.synthetic.main.item_icon_progress_button.view.*


class SUIconProgressButton @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private var buttonStatus: SUButtonStatus = SUButtonStatus.Activated


    init {
        View.inflate(context, R.layout.item_icon_progress_button, this)
        setButtonStatus(buttonStatus)
    }

    fun setText(text: CharSequence) {
        progress_button_text.text = text
    }

    fun setIcon(res: Int) {
        progress_icon.setImageResource(res)
        progress_icon.show(true)
    }

    fun setButtonStatus(status: SUButtonStatus) {
        buttonStatus = status
        when (status) {
            SUButtonStatus.Disabled -> {
                isEnabled = false
                progress_button_loading.show(false)
                progress_button_background.setBackgroundResource(R.drawable.bg_ic_button_normal)
            }
            SUButtonStatus.Loading -> {
                isEnabled = false
                progress_button_loading.show(true)
                progress_button_background.setBackgroundResource(R.drawable.bg_ic_button_normal)
            }
            SUButtonStatus.Activated -> {
                isEnabled = true
                progress_button_loading.show(false)
                progress_button_background.setBackgroundResource(R.drawable.bg_ic_button_normal)
            }
        }
    }

    fun getButtonStatus(): SUButtonStatus = buttonStatus

    fun isLoadingStatus(): Boolean = SUButtonStatus.Loading == buttonStatus
}