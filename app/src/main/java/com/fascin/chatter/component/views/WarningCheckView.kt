package com.fascin.chatter.component.views

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import com.fascin.chatter.R
import com.fascin.chatter.bean.WarningCheckEntity
import com.fascin.chatter.main.chats.ChatsConfig
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.show
import io.rong.imkit.utils.RouteUtils
import io.rong.imlib.model.Conversation
import kotlinx.android.synthetic.main.view_warning_check.view.tvWarningDesc
import kotlinx.android.synthetic.main.view_warning_check.view.tvWarningTitle

/**
 * 首页质检提示
 */
class WarningCheckView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private var data: WarningCheckEntity? = null

    init {
        View.inflate(context, R.layout.view_warning_check, this)
        clickWithTrigger {
            data?.records?.let {
                ChatsConfig.chatPenaltyTag = buildString {
                    append(it[0].imid)
                    append(",")
                    append(it[0].penaltyId)
                }
                RouteUtils.routeToConversationActivity(
                    context,
                    Conversation.ConversationType.PRIVATE,
                    it[0].imid
                )
            }
            data?.records?.removeAt(0)
            show(data?.records?.isNotEmpty() == true)
            data?.records?.let {
                tvWarningTitle.text = it[0].title
                tvWarningDesc.text = it[0].desc
            }
        }
    }

    fun setData(data: WarningCheckEntity?) {
        this.data = data
        show(data?.records?.isNotEmpty() == true)
        if (data?.records?.isNotEmpty() == true) {
            tvWarningTitle.text = data.records?.get(0)?.title.orEmpty()
            tvWarningDesc.text = data.records?.get(0)?.desc.orEmpty()
        }
    }

}