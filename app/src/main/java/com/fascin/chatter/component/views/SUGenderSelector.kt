package com.fascin.chatter.component.views

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import androidx.appcompat.widget.AppCompatTextView
import com.fascin.chatter.R
import kotlinx.android.synthetic.main.item_gender_select.view.*

class SUGenderSelector @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private val genderViews by lazy {
        listOf(
            gender_select_one,
            gender_select_two,
            gender_select_three
        )
    }

    init {
        View.inflate(context, R.layout.item_gender_select, this)
    }

    fun initDataSource(
        data: List<String>,
        selectIndex: Int = 0,
        clickCallback: () -> Unit,
    ) {
        if (data.size != 3) return
        genderViews.forEachIndexed { index, view ->
            view.text = data[index]
            view.setOnClickListener {
                selectGender(view)
                clickCallback.invoke()
            }
            if (selectIndex == index) selectGender(view)
        }
    }

    fun getSelectIndex(): Int {
        val selectView = genderViews.find { it.isSelected } ?: return -1
        return genderViews.indexOf(selectView)
    }

    private fun selectGender(view: AppCompatTextView) {
        genderViews.forEach {
            it.isSelected = view == it
        }
    }
}