package com.fascin.chatter.component.views.swipe

import android.graphics.Canvas
import android.view.View
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.toPx
import kotlin.math.abs


class SwipeItemTouchHelper(private val callback: OnSwipeCallback) : SuItemTouchHelper.Callback() {

    override fun getMovementFlags(
        recyclerView: RecyclerView,
        viewHolder: RecyclerView.ViewHolder
    ): Int {
        val dragFlags = 0
        var swipeFlags = 0
        val layoutManager = recyclerView.layoutManager
        if (layoutManager is SwipeLayoutManager) {
            swipeFlags = ItemTouchHelper.LEFT or ItemTouchHelper.RIGHT
        }
        return makeMovementFlags(dragFlags, swipeFlags)
    }

    override fun onMove(
        recyclerView: RecyclerView,
        viewHolder: RecyclerView.ViewHolder,
        target: RecyclerView.ViewHolder
    ): Boolean {
        return false
    }

    override fun isItemCanSwipe(direction: Int): Boolean {
        if (direction == ItemTouchHelper.RIGHT) {
            return callback.isItemCanSwipe(direction)
        }
        return super.isItemCanSwipe(direction)
    }

    override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {
        viewHolder.itemView.setOnTouchListener(null)
        val targetPosition = viewHolder.layoutPosition
        viewHolder.itemView.post {
            when (direction) {
                ItemTouchHelper.LEFT -> callback.onSwipeLeft(targetPosition)
                ItemTouchHelper.RIGHT -> callback.onSwipeRight(targetPosition)
            }
        }
    }

    override fun onChildDraw(
        c: Canvas,
        recyclerView: RecyclerView,
        viewHolder: RecyclerView.ViewHolder,
        dX: Float,
        dY: Float,
        actionState: Int,
        isCurrentlyActive: Boolean
    ) {
        val currentView = viewHolder.itemView
        if (actionState == ItemTouchHelper.ACTION_STATE_SWIPE) {
            val threshold = recyclerView.width * getSwipeThreshold(viewHolder)
            var swipeRatio: Float = dX / threshold
            callback.onSwiping(currentView, swipeRatio, isCurrentlyActive)
            if (abs(swipeRatio) <= 0.01F) return

            val offset = SwipeLayoutManager.DEFAULT_PIVOT_OFFSET.toPx
            val centerX = currentView.width / 2F
            currentView.pivotX = if (swipeRatio < 0) centerX else centerX
            currentView.pivotY = currentView.height.toFloat() + offset
            currentView.rotation = swipeRatio * SwipeLayoutManager.DEFAULT_ROTATE_DEGREE
//            Log.d("David", "swipeRatio:$swipeRatio ===== isCurrentlyActive:$isCurrentlyActive")
            if (abs(swipeRatio) > 1F) {
                currentView.alpha = (1F - abs(abs(swipeRatio) - 1)).coerceAtLeast(0F)
            } else {
                currentView.alpha = 1F
            }

            val childCount = recyclerView.childCount
            val nextPosition = childCount - 2
            if (nextPosition >= 0) {
                swipeRatio = abs(swipeRatio).coerceAtMost(1F)
                val nextView = recyclerView.getChildAt(nextPosition)
                nextView.show(true)
                val defaultScale = SwipeLayoutManager.DEFAULT_BACK_SCALE
                nextView.alpha = 1F * abs(swipeRatio)
                nextView.scaleX = defaultScale + (1F - defaultScale) * abs(swipeRatio)
                nextView.scaleY = defaultScale + (1F - defaultScale) * abs(swipeRatio)
            }
        }
    }

    override fun clearView(
        recyclerView: RecyclerView,
        viewHolder: RecyclerView.ViewHolder
    ) {
        super.clearView(recyclerView, viewHolder)
        viewHolder.itemView.apply {
            rotation = 0F
            alpha = 1F
            scaleX = 1F
            scaleY = 1F
        }
    }

    override fun isItemViewSwipeEnabled(): Boolean {
        return false
    }

    override fun getSwipeThreshold(viewHolder: RecyclerView.ViewHolder): Float {
        return 0.4F
    }

    override fun getSwipeEscapeVelocity(defaultValue: Float): Float {
        return 1.0F * defaultValue
    }

    override fun getSwipeVelocityThreshold(defaultValue: Float): Float {
        return 2.0F * defaultValue
    }

    override fun getMoveThreshold(viewHolder: RecyclerView.ViewHolder): Float {
        return 0.5F
    }

    override fun getAnimationDuration(
        recyclerView: RecyclerView,
        animationType: Int,
        animateDx: Float,
        animateDy: Float
    ): Long {
        return 200L
    }

    override fun canDropOver(
        recyclerView: RecyclerView,
        current: RecyclerView.ViewHolder,
        target: RecyclerView.ViewHolder
    ): Boolean {
        return false
    }

    override fun interpolateOutOfBoundsScroll(
        recyclerView: RecyclerView,
        viewSize: Int,
        viewSizeOutOfBounds: Int,
        totalSize: Int,
        msSinceStartScroll: Long
    ): Int {
        return super.interpolateOutOfBoundsScroll(
            recyclerView,
            viewSize,
            viewSizeOutOfBounds,
            totalSize,
            msSinceStartScroll
        )
    }
}

interface OnSwipeCallback {
    fun onSwipeLeft(position: Int)
    fun onSwipeRight(position: Int)
    fun onSwiping(itemView: View, swipeRatio: Float, isCurrentlyActive: Boolean)
    fun isItemCanSwipe(direction: Int): Boolean
    fun inSwipeRange(swipeRatio: Float): Boolean = swipeRatio > SwipeLayoutManager.MIN_SWIPE_RATIO
            && swipeRatio < SwipeLayoutManager.MAX_SWIPE_RATIO
}