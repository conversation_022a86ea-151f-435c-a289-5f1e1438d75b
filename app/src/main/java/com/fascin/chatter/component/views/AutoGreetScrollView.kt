package com.fascin.chatter.component.views

import android.annotation.SuppressLint
import android.content.Context
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.LinearInterpolator
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.component.views.looplayout.LoopingLayoutManager
import com.iandroid.allclass.lib_common.beans.GreetItem
import kotlinx.android.synthetic.main.layout_auto_greet.view.bottomRecyclerView
import kotlinx.android.synthetic.main.layout_auto_greet.view.topRecyclerView


/**
 * @date 2022/12/1.
 */
class AutoGreetScrollView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    private val topList = mutableListOf<GreetItem>()
    private val bottomList = mutableListOf<GreetItem>()
    private var mHandler: ScrollHandler
    private var isScroll = true

    inner class ScrollHandler :
        Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            postDelayed({
                topRecyclerView.smoothScrollBy(-60, 0, LinearInterpolator(), 1000)
                bottomRecyclerView.smoothScrollBy(60, 0, LinearInterpolator(), 1000)
                sendEmptyMessage(1)
            }, 1000)
        }
    }

    init {
        mHandler = ScrollHandler()
        View.inflate(context, R.layout.layout_auto_greet, this)
    }

    fun setClickCallback(clickCallback: (Int, String) -> Unit?) {
        topRecyclerView.clickCallback = clickCallback
        bottomRecyclerView.clickCallback = clickCallback
    }

    fun updateView(data: ArrayList<GreetItem>) {
        if (!data.isNullOrEmpty()) {
            // 需双行显示，只有一条数据时，复制一条
            if (data.size == 1)
                data.add(data[0])
            val count = data.size
            val index = (count / 2)
            topList.clear()
            bottomList.clear()
            topList.addAll(data.subList(0, index))
            bottomList.addAll(data.subList(index, count))
            val topAdapter = AutoAdapter(context, topList)
            val bottomAdapter =
                AutoAdapter(context, bottomList)
            topRecyclerView.apply {
                setHasFixedSize(true)
                layoutManager = LoopingLayoutManager(
                    context,
                    LoopingLayoutManager.HORIZONTAL,
                    false
                )
                adapter = topAdapter
            }
            bottomRecyclerView.apply {
                setHasFixedSize(true)
                layoutManager = LoopingLayoutManager(
                    context,
                    LoopingLayoutManager.HORIZONTAL,
                    false
                )
                adapter = bottomAdapter
            }
            mHandler.sendEmptyMessage(1)
        }
    }

    class AutoAdapter(
        val context: Context, val list: MutableList<GreetItem>
    ) :
        RecyclerView.Adapter<AutoAdapter.ViewHolder>() {


        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AutoAdapter.ViewHolder {
            val view =
                LayoutInflater.from(parent.context)
                    .inflate(R.layout.itemview_greet_item, parent, false)
            return ViewHolder(view)
        }

        override fun onBindViewHolder(
            holder: AutoAdapter.ViewHolder,
            @SuppressLint("RecyclerView") position: Int
        ) {
            holder.greetView.text = list[position]?.getContentStr()
        }

        override fun getItemCount(): Int {
            return list.size
        }

        fun updateView(data: List<GreetItem>) {
            list.clear()
            list.addAll(data)
            notifyDataSetChanged()
        }

        fun getData(): MutableList<GreetItem> {
            return list
        }

        inner class ViewHolder : RecyclerView.ViewHolder {
            var greetView: TextView

            constructor(itemView: View) : super(itemView) {
                greetView = itemView.findViewById(R.id.id_name)
            }
        }
    }

    fun stopLoop() {
        isScroll = false
        mHandler.sendEmptyMessage(1)
    }

    fun startLoop() {
        isScroll = false
        mHandler.removeMessages(1)
    }

    private fun stopScroll() {
        topRecyclerView.stopScroll()
        bottomRecyclerView.stopScroll()
    }
}
