package com.fascin.chatter.component.views

import android.content.Context
import android.util.AttributeSet
import android.widget.FrameLayout
import com.fascin.chatter.R
import com.fascin.chatter.bean.ShiftEntranceEntity
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.show
import kotlinx.android.synthetic.main.view_work_shift_entrance.view.ivIcon
import kotlinx.android.synthetic.main.view_work_shift_entrance.view.ivNew
import kotlinx.android.synthetic.main.view_work_shift_entrance.view.tvShiftContent

/**
 * @Desc: 排班入口
 * @Created: Quan
 * @Date: 2024/11/13
 */
class WorkShiftEntranceView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private var entity: ShiftEntranceEntity? = null

    init {
        inflate(context, R.layout.view_work_shift_entrance, this)
    }

    fun setData(entity: ShiftEntranceEntity) {
        this.entity = entity
        hasChange(entity.schedulingChange)
        ivIcon.setImageResource(
            if (entity.workScheduling) R.drawable.ic_shift_entrance
            else R.drawable.ic_break_entrance
        )
        tvShiftContent.text = context.getString(
            if (entity.workScheduling) R.string.text_work_shifts
            else R.string.text_work_break
        )
    }

    /**
     * 是否有未读的变更
     */
    fun hasChange(change: Boolean) {
        ivNew.show(change)
    }
}