package com.fascin.chatter.component.views

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.PopupWindow
import com.fascin.chatter.R
import com.fascin.chatter.main.chats.ChatsConfig
import com.iandroid.allclass.lib_common.UserController
import com.iandroid.allclass.lib_common.event.EventKey
import com.iandroid.allclass.lib_common.repository.CommonRepository
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.toPx
import kotlinx.android.synthetic.main.view_sort_contact_tag.view.ivContactTagSel

/**
 * @Desc: 会话列表排序用的view
 * @Created: Quan
 * @Date: 2024/6/4
 */
class ContactTagSortView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private var curTagId: Int = ChatsConfig.TAG_NONE
    private var block: (Int) -> Unit = {}
    private var from: Int = 0 //0:会话列表页   1：VIP页

    init {
        View.inflate(context, R.layout.view_sort_contact_tag, this)
        setTagUI(ChatsConfig.TAG_NONE)
        clickWithTrigger {
            showPopupMenu()
        }
    }

    /**
     * 监听tag变化
     * @param from 0:会话列表页   1：VIP页
     */
    fun tagChangeListener(from: Int, block: (Int) -> Unit) {
        this.block = block
        this.from = from
    }

    private fun setTagUI(tagId: Int) {
        this.curTagId = tagId
        when (tagId) {
            ChatsConfig.TAG_MONEY -> {
                ivContactTagSel.setImageResource(R.drawable.ic_contact_tag_money)
            }

            ChatsConfig.TAG_HOT -> {
                ivContactTagSel.setImageResource(R.drawable.ic_contact_tag_hot)
            }

            ChatsConfig.TAG_HEART -> {
                ivContactTagSel.setImageResource(R.drawable.ic_contact_tag_heart)
            }

            ChatsConfig.TAG_TURTLE -> {
                ivContactTagSel.setImageResource(R.drawable.ic_contact_tag_turtle)
            }

            else -> {
                ivContactTagSel.setImageResource(R.drawable.ic_contact_tag_top_sel)
            }
        }
    }

    private fun showPopupMenu() {
        val contentView = LayoutInflater.from(context).inflate(R.layout.popup_contact_tag_sel, null)

        val clSelMoney = contentView.findViewById<View>(R.id.clMoney)
        val clSelHot = contentView.findViewById<View>(R.id.clHot)
        val clSelHeart = contentView.findViewById<View>(R.id.clHeart)
        val clSelTurtle = contentView.findViewById<View>(R.id.clTurtle)

        val ivSelMoney = contentView.findViewById<ImageView>(R.id.ivSelMoney)
        val ivSelHot = contentView.findViewById<ImageView>(R.id.ivSelHot)
        val ivSelHeart = contentView.findViewById<ImageView>(R.id.ivSelHeart)
        val ivSelTurtle = contentView.findViewById<ImageView>(R.id.ivSelTurtle)

        when (curTagId) {
            ChatsConfig.TAG_MONEY -> {
                clSelMoney.setBackgroundResource(com.iandroid.allclass.lib_common.R.color.color_FDA946_a10)
                ivSelMoney.setImageResource(R.drawable.ic_contact_tag_manu_sel)
            }

            ChatsConfig.TAG_HOT -> {
                clSelHot.setBackgroundResource(com.iandroid.allclass.lib_common.R.color.color_FDA946_a10)
                ivSelHot.setImageResource(R.drawable.ic_contact_tag_manu_sel)
            }

            ChatsConfig.TAG_HEART -> {
                clSelHeart.setBackgroundResource(com.iandroid.allclass.lib_common.R.color.color_FDA946_a10)
                ivSelHeart.setImageResource(R.drawable.ic_contact_tag_manu_sel)
            }

            ChatsConfig.TAG_TURTLE -> {
                clSelTurtle.setBackgroundResource(com.iandroid.allclass.lib_common.R.color.color_FDA946_a10)
                ivSelTurtle.setImageResource(R.drawable.ic_contact_tag_manu_sel)
            }
        }

        // 创建 PopupWindow
        val popupWindow = PopupWindow(
            contentView,
            200.toPx,
            LinearLayout.LayoutParams.WRAP_CONTENT,
            true
        )
        contentView.findViewById<View>(R.id.clMoney).setOnClickListener {
            if (curTagId != ChatsConfig.TAG_MONEY) {
                setTagUI(ChatsConfig.TAG_MONEY)
                block.invoke(ChatsConfig.TAG_MONEY)
                traceTagChange(ChatsConfig.TAG_MONEY, "money")
            }
            popupWindow.dismiss()
        }

        contentView.findViewById<View>(R.id.clHot).setOnClickListener {
            if (curTagId != ChatsConfig.TAG_HOT) {
                setTagUI(ChatsConfig.TAG_HOT)
                block.invoke(ChatsConfig.TAG_HOT)
                traceTagChange(ChatsConfig.TAG_HOT, "fire")
            }
            popupWindow.dismiss()
        }

        contentView.findViewById<View>(R.id.clHeart).setOnClickListener {
            if (curTagId != ChatsConfig.TAG_HEART) {
                setTagUI(ChatsConfig.TAG_HEART)
                block.invoke(ChatsConfig.TAG_HEART)
                traceTagChange(ChatsConfig.TAG_HEART, "heart")
            }
            popupWindow.dismiss()
        }

        contentView.findViewById<View>(R.id.clTurtle).setOnClickListener {
            if (curTagId != ChatsConfig.TAG_TURTLE) {
                setTagUI(ChatsConfig.TAG_TURTLE)
                block.invoke(ChatsConfig.TAG_TURTLE)
                traceTagChange(ChatsConfig.TAG_TURTLE, "stingy")
            }
            popupWindow.dismiss()
        }

        contentView.findViewById<View>(R.id.tvReset).setOnClickListener {
            if (curTagId != ChatsConfig.TAG_NO_SEL) {
                setTagUI(ChatsConfig.TAG_NO_SEL)
                block.invoke(ChatsConfig.TAG_NO_SEL)
                traceTagChange(ChatsConfig.TAG_NO_SEL, "remove")
            }
            popupWindow.dismiss()
        }

        // 显示 PopupWindow 在 anchor 视图的下方
        popupWindow.showAsDropDown(this@ContactTagSortView)
    }

    /**
     * 埋点
     * 点击筛选器Type中的4个tag记录一次，不包含点击重置
     */
    private fun traceTagChange(tagId: Int, tagName: String) {
        CommonRepository.eventTrace(EventKey.im_chat_type_sort) {
            "chatterID" to UserController.getUserId()
            "tagId" to tagId
            "tagName" to tagName
            "from" to from
        }
    }
}