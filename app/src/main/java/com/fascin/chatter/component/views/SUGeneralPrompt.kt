package com.fascin.chatter.component.views

import android.content.Context
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.content.ContextCompat
import com.fascin.chatter.R
import com.iandroid.allclass.lib_common.utils.exts.toPx

class SUGeneralPrompt @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatTextView(context, attrs, defStyleAttr) {

    init {
        setPadding(16.toPx, 16.toPx, 16.toPx, 16.toPx)
        ContextCompat.getDrawable(context, R.mipmap.ic_prompt_light)?.let {
            it.setBounds(0, 0, it.minimumWidth, it.minimumHeight)
            setCompoundDrawables(it, null, null, null)
            compoundDrawablePadding = 16.toPx
        }
        setBackgroundResource(R.drawable.bg_general_prompt_yellow)
    }

}