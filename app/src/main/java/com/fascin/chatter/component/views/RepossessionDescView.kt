package com.fascin.chatter.component.views

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import com.fascin.chatter.R
import com.fascin.chatter.bean.MpcRepossessionRuleEntity
import kotlinx.android.synthetic.main.view_repossession_rule.view.tvTitle
import kotlinx.android.synthetic.main.view_repossession_rule.view.tvValue

/**
 * @Desc:MPC页面回收预警信息任务信息
 * @Created: Quan
 * @Date: 2024/11/4
 */
class RepossessionDescView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    init {
        View.inflate(context, R.layout.view_repossession_rule, this)
    }

    fun setDescData(rule: MpcRepossessionRuleEntity) {
        tvTitle.text = rule.title
        tvValue.text = rule.value
    }
}