package com.fascin.chatter.component.views

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View
import androidx.viewpager2.widget.ViewPager2
import com.iandroid.allclass.lib_common.R
import com.iandroid.allclass.lib_common.utils.exts.toPx

class SURectIndicator @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private var rectRadius = 6.toPx
    private var selectColor = 0xFF9370DB.toInt()
    private var unselectColor = 0x8F000000.toInt()
    private var circleGap = 8.toPx
    private var count = 0
    private var pageIndex = 0
    private var openWeight = false
    private var selectPaint = Paint()
    private var unselectPaint = Paint()

    init {
        attrs?.let {
            val typedArray = context.obtainStyledAttributes(it, R.styleable.SuRectIndicator)
            typedArray?.apply {
                selectColor =
                    getColor(R.styleable.SuRectIndicator_selectColor, 0xFF9370DB.toInt())
                unselectColor =
                    getColor(R.styleable.SuRectIndicator_unselectColor, 0x8F000000.toInt())
                openWeight = getBoolean(R.styleable.SuRectIndicator_openWeight, false)
                recycle()
            }
        }
        selectPaint.color = selectColor
        selectPaint.isAntiAlias = true
        selectPaint.style = Paint.Style.FILL
        selectPaint.strokeWidth = 4F.toPx
        selectPaint.strokeCap = Paint.Cap.ROUND
        unselectPaint.color = unselectColor
        unselectPaint.isAntiAlias = true
        unselectPaint.style = Paint.Style.FILL
        unselectPaint.strokeWidth = 4F.toPx
        unselectPaint.strokeCap = Paint.Cap.ROUND
    }

    fun setupWithViewPager2(viewPager: ViewPager2) {
        count = viewPager.adapter?.itemCount ?: 0
        viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {

            override fun onPageSelected(position: Int) {
                pageIndex = position
                invalidate()
            }
        })
        invalidate()
    }

    override fun onDraw(canvas: Canvas?) {
        canvas ?: return
        if (count < 1) return

        var diameter = 0 // 指示器item宽度
        var half = 0f // 指示器第一个item的startX
        if (openWeight) {
            half = paddingLeft.toFloat()
            // item自适应宽度
            diameter = (width - (paddingLeft + paddingRight) - circleGap * (count - 1)) / count
        } else {
            diameter = 2 * rectRadius
            half = (width - diameter * count - circleGap * (count - 1)) / 2F
        }
        repeat(count) {
            val startX = half + (diameter + circleGap) * it
            val startY = height / 2F
            val endX = startX + diameter
            val endY = height / 2F
            canvas.drawLine(
                startX, startY, endX, endY,
                if (it == pageIndex) selectPaint else unselectPaint
            )
        }
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        setMeasuredDimension(MeasureSpec.getSize(widthMeasureSpec), (2 * rectRadius).toInt())
    }

}