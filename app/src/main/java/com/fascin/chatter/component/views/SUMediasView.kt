package com.fascin.chatter.component.views

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewOutlineProvider
import android.widget.FrameLayout
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback
import com.fascin.chatter.R
import com.fascin.chatter.component.player.SUVolumeEvent
import com.fascin.chatter.component.player.suGlobalVideoVolume
import com.fascin.chatter.config.Config
import com.iandroid.allclass.lib_basecore.view.blurview.BlurConfig
import com.iandroid.allclass.lib_common.GlideLoader.loadImage
import com.iandroid.allclass.lib_common.GlideLoader.loadVideo
import com.iandroid.allclass.lib_common.beans.MediaEntity
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.toPx
import kotlinx.android.synthetic.main.item_medias_image.view.ScaleAbleView
import kotlinx.android.synthetic.main.item_medias_image.view.mediasImageRoot
import kotlinx.android.synthetic.main.item_medias_image.view.medias_image_view
import kotlinx.android.synthetic.main.item_medias_image.view.wildPhotoPrivate
import kotlinx.android.synthetic.main.item_medias_image.view.wildPhotoPrivateBlur
import kotlinx.android.synthetic.main.item_user_medias.view.maskViewLB
import kotlinx.android.synthetic.main.item_user_medias.view.maskViewLT
import kotlinx.android.synthetic.main.item_user_medias.view.maskViewRB
import kotlinx.android.synthetic.main.item_user_medias.view.maskViewRT
import kotlinx.android.synthetic.main.item_user_medias.view.media_rect_indicator
import kotlinx.android.synthetic.main.item_user_medias.view.media_top_indicator
import kotlinx.android.synthetic.main.item_user_medias.view.media_video_volume
import kotlinx.android.synthetic.main.item_user_medias.view.media_view_pager
import kotlinx.android.synthetic.main.item_user_medias.view.root_view

class SUMediasView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private val mediasAdapter by lazy {
        SUMediasAdapter()
    }

    private var imageRoundedCorner = 10.toPx
    private var mediaChangedCallback: OnMediaChangedCallback? = null

    init {
        View.inflate(context, R.layout.item_user_medias, this)
        media_view_pager.apply {
            offscreenPageLimit = 3
            adapter = mediasAdapter
        }
        enableUserInputEvent(false)
        media_video_volume.setOnClickListener { onTapMute() }

        media_view_pager.registerOnPageChangeCallback(object : OnPageChangeCallback() {

            private var lastPosition = -1
            override fun onPageSelected(position: Int) {
                resetItemView(lastPosition)
                mediasAdapter.getItem(position)?.also {
                    mediaChangedCallback?.onMediaChanged(it)
                }
                lastPosition = position
            }
        })
    }

    fun enableUserInputEvent(enable: Boolean) {
        media_view_pager.isUserInputEnabled = enable
    }

    fun setImageRoundedCorner(imageRoundedCorner: Int) {
        this.imageRoundedCorner = imageRoundedCorner
    }

    fun showCornerMaskView(show: Boolean) {
        maskViewLT.show(show)
        maskViewLB.show(show)
        maskViewRT.show(show)
        maskViewRB.show(show)
    }

    fun setOnMediaChangedCallback(mediaChangedCallback: OnMediaChangedCallback?) {
        this.mediaChangedCallback = mediaChangedCallback
    }

    fun setDataSource(mediaList: List<MediaEntity>, scaleAble: Boolean = false, showTopIndicator: Int = 0) {
        mediasAdapter.setDataSource(mediaList, imageRoundedCorner, scaleAble)
        if (mediaList.size > 1) {
            media_rect_indicator.setupWithViewPager2(media_view_pager)
            media_top_indicator.setupWithViewPager2(media_view_pager)
        }
        media_top_indicator.show(mediaList.size > 1 && showTopIndicator == 1)
        media_rect_indicator.show(mediaList.size > 1 && showTopIndicator == 0)
        // 调整声音按钮位置
        if (showTopIndicator == 1) changeMuteViewLocation(showTopIndicator)
        if (mediaList.isNotEmpty()) {
            media_view_pager.setCurrentItem(0, false)
        }
    }

    fun showMuteView() {
        media_video_volume.show(true)
        media_video_volume.setImageResource(
            if (suGlobalVideoVolume == 1F) R.mipmap.ic_video_unmute else R.mipmap.ic_video_mute
        )
    }

    fun hideMuteView() {
        media_video_volume.show(false)
    }

    fun getCurrentView(): View? {
        return getTargetView(media_view_pager.currentItem)
    }

    private fun getTargetView(position: Int): View? {
        val recyclerView = media_view_pager.getChildAt(0) as? RecyclerView ?: return null
        return recyclerView.layoutManager?.findViewByPosition(position)
    }

    private fun resetItemView(position: Int) {
        val itemView = getTargetView(position) ?: return
        itemView.medias_image_view.show(true)
    }

    fun onTapLeftRect() {
        val currentIndex = media_view_pager.currentItem
        if (currentIndex >= 0 && currentIndex < mediasAdapter.itemCount) {
            media_view_pager.setCurrentItem(currentIndex - 1, false)
        }
    }

    fun onTapRightRect() {
        val currentIndex = media_view_pager.currentItem
        if (currentIndex >= 0 && currentIndex < mediasAdapter.itemCount) {
            media_view_pager.setCurrentItem(currentIndex + 1, false)
        }
    }

    fun onTapMute() {
        SimpleRxBus.post(SUVolumeEvent.muteOrUnMute())
    }

    /**
     * 约束布局克隆，调整view位置
     */
    private fun changeMuteViewLocation(showTopIndicator: Int) {
        val constraintSet = ConstraintSet()
        //注意约束布局内的所有子控件都必须设置id值
        constraintSet.clone(root_view)
        //先清除之前的约束条件，方便我们重新设置约束条件
        constraintSet.clear(R.id.media_video_volume)
        //设置约束条件
        if (showTopIndicator == 1) {
            constraintSet.connect(
                R.id.media_video_volume,
                ConstraintSet.LEFT,
                media_top_indicator.id,
                ConstraintSet.LEFT
            )
        } else {
            constraintSet.connect(
                R.id.media_video_volume,
                ConstraintSet.LEFT,
                ConstraintSet.PARENT_ID,
                ConstraintSet.LEFT
            )
        }
        constraintSet.connect(
            R.id.media_video_volume, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP
        )
        //设置约束条件生效
        constraintSet.applyTo(root_view)
        media_video_volume.show(false)
        //设置具体底部的距离
        val layoutParams = media_video_volume.layoutParams as ConstraintLayout.LayoutParams
        // 手动设置宽高
        layoutParams.height = 28.toPx
        layoutParams.width = 28.toPx
        layoutParams.topMargin = if (showTopIndicator == 1) 20.toPx else 40.toPx
        layoutParams.leftMargin = 12.toPx
    }
}

class SUMediasAdapter : RecyclerView.Adapter<SUMediasAdapter.ViewHolder>() {
    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

    private val mediaList = mutableListOf<MediaEntity>()
    private var imageRoundedCorner: Int = 0
    private var scaleAble: Boolean = false

    fun setDataSource(
        mediaList: List<MediaEntity>,
        imageRoundedCorner: Int,
        scaleAble: Boolean = false
    ) {
        this.imageRoundedCorner = imageRoundedCorner
        this.mediaList.clear()
        this.mediaList.addAll(mediaList)
        this.scaleAble = scaleAble
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutInflater.from(parent.context).inflate(
                R.layout.item_medias_image, parent, false
            )
        )
    }

    fun getItem(position: Int): MediaEntity? {
        if (position >= 0 && position < mediaList.size) {
            return mediaList[position]
        }
        return null
    }

    override fun getItemCount(): Int {
        return mediaList.size
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val info = mediaList[position]
        holder.itemView.apply {
            if (!scaleAble) ScaleAbleView.setUnScaleAble()
            medias_image_view.show(true)
            if (info.type == Config.MediaVideo) {
                medias_image_view.loadVideo(
                    context = context,
                    url = info.getImageUrl(),
                    roundedCorners = imageRoundedCorner
                )
            } else {
                medias_image_view.loadImage(
                    context = context,
                    url = mediaList[position].url,
                    roundedCorners = imageRoundedCorner
                )
            }
            wildPhotoPrivateBlur.show(info.isPrivate == 1)
            if (context != null) {
                try {
                    BlurConfig.setBlur(context, wildPhotoPrivateBlur, mediasImageRoot, holder.itemView.background, 6)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
            wildPhotoPrivateBlur.outlineProvider = ViewOutlineProvider.BACKGROUND
            wildPhotoPrivateBlur.clipToOutline = true

            wildPhotoPrivate.text = context.getString(if (info.type == 0) R.string.wild_photo else R.string.wild_video)
        }
    }

}

interface OnMediaChangedCallback {
    fun onMediaChanged(mediaEntity: MediaEntity)

    fun onMuteTapped()
}