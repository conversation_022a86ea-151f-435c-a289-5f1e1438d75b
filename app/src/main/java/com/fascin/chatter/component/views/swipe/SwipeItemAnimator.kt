package com.fascin.chatter.component.views.swipe

import androidx.core.view.ViewPropertyAnimatorCompat
import androidx.recyclerview.widget.RecyclerView
import com.iandroid.allclass.lib_basecore.view.BaseItemAnimator

class SwipeItemAnimator: BaseItemAnimator() {


    override fun setRemoveAnimation(
        holder: RecyclerView.ViewHolder,
        animator: ViewPropertyAnimatorCompat
    ) {
        holder.itemView.pivotX = holder.itemView.width / 2F
        holder.itemView.pivotY = holder.itemView.height.toFloat()
        animator.rotation(180F)
    }

    override fun removeAnimationEnd(holder: RecyclerView.ViewHolder) {
        holder.itemView.rotation = 0F
    }

    override fun addAnimationInit(holder: RecyclerView.ViewHolder?) {

    }

    override fun setAddAnimation(
        holder: RecyclerView.ViewHolder?,
        animator: ViewPropertyAnimatorCompat?
    ) {
    }

    override fun addAnimationCancel(holder: RecyclerView.ViewHolder?) {
    }

    override fun setOldChangeAnimation(
        holder: RecyclerView.ViewHolder?,
        animator: ViewPropertyAnimatorCompat?
    ) {
    }

    override fun oldChangeAnimationEnd(holder: RecyclerView.ViewHolder?) {
    }

    override fun newChangeAnimationInit(holder: RecyclerView.ViewHolder?) {
    }

    override fun setNewChangeAnimation(
        holder: RecyclerView.ViewHolder?,
        animator: ViewPropertyAnimatorCompat?
    ) {
    }

    override fun newChangeAnimationEnd(holder: RecyclerView.ViewHolder?) {
    }
}

