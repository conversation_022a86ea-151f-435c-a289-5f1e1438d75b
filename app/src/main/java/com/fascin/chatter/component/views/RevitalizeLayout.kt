package com.fascin.chatter.component.views

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import com.fascin.chatter.R
import com.fascin.chatter.bean.RevitalizeInEntity
import com.fascin.chatter.main.view.RevitalizeDialog
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.GlideLoader.loadImage
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.timer.CurCountDownTimer
import com.iandroid.allclass.lib_common.utils.timer.ICountDownListener
import kotlinx.android.synthetic.main.layout_revitalize.view.ivRevitalize
import kotlinx.android.synthetic.main.layout_revitalize.view.ivRevitalizeIcon
import kotlinx.android.synthetic.main.layout_revitalize.view.tvRevitalizeTime
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledFuture
import java.util.concurrent.TimeUnit

/**
 * @Desc: 会员老用户召回的入口
 * @Created: Quan
 * @Date: 2024/10/18
 */
class RevitalizeLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private var scheduledTask: ScheduledFuture<*>? = null
    private val scheduler = Executors.newScheduledThreadPool(1)
    private val mainHandler = Handler(Looper.getMainLooper())


    private var canClick: Boolean = false
    private var countDownTimer: CurCountDownTimer? = null

    init {
        View.inflate(context, R.layout.layout_revitalize, this)
        clickWithTrigger {
            if (canClick) {
                AppContext.getTopFragmentActivity()?.also { fragmentActivity ->
                    RevitalizeDialog().show(
                        fragmentActivity.supportFragmentManager,
                        RevitalizeDialog::javaClass.name
                    )
                }
            }
        }
    }

    fun setRevitalizeData(entity: RevitalizeInEntity) {
        startDownTimer(entity.downtime.times(1000L))
        if (entity.icons.size > 1) {
            loopImage(entity)
        } else if (entity.icons.size == 1) {
            ivRevitalize.loadImage(AppContext.context, entity.icons[0])
        }
    }

    private fun loopImage(entity: RevitalizeInEntity?) {
        entity?.icons?.let {
            var currentIndex = 0
            scheduledTask?.cancel(true)
            scheduledTask = scheduler.scheduleAtFixedRate({
                mainHandler.post {
                    ivRevitalize.loadImage(AppContext.context, it[currentIndex])
                }
                currentIndex = (currentIndex + 1) % it.size
            }, 0, 5, TimeUnit.SECONDS)

        }
    }

    fun startDownTimer(time: Long) {
        canClick = time <= 0
        tvRevitalizeTime.show(time > 0)
        ivRevitalizeIcon.show(time <= 0)
        if (time <= 0) return
        if (countDownTimer == null){
            countDownTimer = CurCountDownTimer()
            countDownTimer?.startCountDown()
        }
        countDownTimer?.register(object : ICountDownListener {
            override fun onCountDownTick(day: Long, hour: Long, minute: Long, second: Long) {
                if (minute >= 0 && second >= 0) {
                    tvRevitalizeTime.text = buildString {
                        append(if (minute <= 9) "0$minute" else minute)
                        append(":")
                        append(if (second <= 9) "0$second" else second)
                    }
                }
            }

            override fun onCountDownFinish() {
                tvRevitalizeTime.show(false)
                ivRevitalizeIcon.show(true)
                canClick = true
            }
        }, time)
    }

    fun onDestroy() {
        scheduledTask?.cancel(true)
        countDownTimer?.cancelCountDownAndRemoveListener()
        scheduledTask = null
    }
}