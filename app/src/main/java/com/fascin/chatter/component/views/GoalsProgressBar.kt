package com.fascin.chatter.component.views

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.RectF
import android.util.AttributeSet
import android.util.TypedValue
import android.view.View
import com.iandroid.allclass.lib_common.utils.exts.toPx

/**
 * @Desc: 试岗优化进度条
 * @Created: Quan
 * @Date: 2024/10/21
 */
class GoalsProgressBar @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private var progress = 0
    private var maxProgress = 100
    private val fixedWidthDp = 20.toPx.toFloat()  // 固定宽度20dp
    private val fixedHeightDp = 12.toPx.toFloat()  // 高度12dp
    private val fixedRadiusDp = 6.toPx.toFloat()  // 高度12dp

    private val backgroundPaint = Paint().apply {
        color = Color.LTGRAY
        style = Paint.Style.FILL
        isAntiAlias = true
    }

    private val progressPaint = Paint().apply {
        color = Color.BLACK
        style = Paint.Style.FILL
        isAntiAlias = true
    }

    private val textBackgroundPaint = Paint().apply {
        color = Color.BLACK
        style = Paint.Style.FILL
        isAntiAlias = true
    }

    private val textPaint = Paint().apply {
        color = Color.WHITE
        textSize = spToPx(8f)
        isAntiAlias = true
        textAlign = Paint.Align.CENTER
    }

    private val cornerRadius = 25f  // 圆角半径

    // 设置最大值
    fun setMaxValue(max: Int) {
        maxProgress = max
        invalidate()
    }

    // 设置当前进度
    fun setCurrentValue(progress: Int) {
        this.progress = progress
        invalidate()
    }

    override fun onDraw(canvas: Canvas?) {
        super.onDraw(canvas)

        val width = width.toFloat()
        val curHeight = 4.toPx.toFloat()

        // 计算当前进度的宽度
        val progressWidth = (progress.toFloat() / maxProgress) * width

        // 绘制背景圆角矩形
        val backgroundRect = RectF(0f, (height - curHeight) / 2f, width, (height + curHeight) / 2f)
        canvas?.drawRoundRect(backgroundRect, cornerRadius, cornerRadius, backgroundPaint)

        // 绘制进度条圆角矩形
        val progressRect = RectF(0f, (height - curHeight) / 2f, progressWidth, (height + curHeight) / 2f)
        canvas?.drawRoundRect(progressRect, cornerRadius, cornerRadius, progressPaint)

        drawText(canvas, progressWidth)
        // 绘制末端的进度值
//        val textX = progressWidth - 50f  // 确保数字在末端但不超出视图
//        val textY = curHeight / 2f + textPaint.textSize / 3f  // 垂直居中
//        canvas?.drawText(progress.toString(), textX, textY, textPaint)
    }

    private fun drawText(canvas: Canvas?, progressWidth: Float) {
        val bgXS = if (progressWidth > fixedWidthDp) progressWidth - fixedWidthDp
        else if (progressWidth + fixedWidthDp > width) width - fixedWidthDp
        else 0f
        val bgYS = if (height > fixedHeightDp) (height - fixedHeightDp) / 2f else 0f
        // 绘制背景圆角矩形
        val rect = RectF(bgXS, bgYS, bgXS + fixedWidthDp, bgYS + fixedHeightDp)
        canvas?.drawRoundRect(rect, fixedRadiusDp, fixedRadiusDp, textBackgroundPaint)
        // 绘制文本
        val textY = bgYS + fixedHeightDp / 2f + textPaint.textSize / 3f  // 垂直居中
        canvas?.drawText(progress.toString(), bgXS + fixedWidthDp / 2f, textY, textPaint)
    }

    // 将sp转换为px
    private fun spToPx(sp: Float): Float {
        return TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_SP, sp, resources.displayMetrics)
    }
}