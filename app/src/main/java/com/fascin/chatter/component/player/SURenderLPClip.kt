package com.fascin.chatter.component.player

import android.widget.RelativeLayout

object SURenderLPClip {


    fun getLayoutParams(
        videoWidth: Int,
        videoHeight: Int,
        parentWidth: Int,
        parentHeight: Int
    ): RelativeLayout.LayoutParams {
        val ratioW = videoWidth.toFloat() / parentWidth.toFloat()
        val ratioH = videoHeight.toFloat() / parentHeight.toFloat()
        val ratio =
            if (videoWidth < videoHeight) Math.min(ratioW, ratioH) else Math.max(ratioW, ratioH)
        val viewWidth = Math.ceil((videoWidth.toFloat() / ratio).toDouble()).toInt()
        val viewHeight = Math.ceil((videoHeight.toFloat() / ratio).toDouble()).toInt()

        val layoutParams: RelativeLayout.LayoutParams =
            RelativeLayout.LayoutParams(viewWidth, viewHeight)
        if (videoWidth < videoHeight) {
            if (ratioW < ratioH) {
                layoutParams.leftMargin = 0
                layoutParams.rightMargin = 0
                layoutParams.topMargin = -(viewHeight - parentHeight) / 2
                layoutParams.bottomMargin = -(viewHeight - parentHeight) / 2
            } else {
                layoutParams.topMargin = 0
                layoutParams.bottomMargin = 0
                layoutParams.leftMargin = -(viewWidth - parentWidth) / 2
                layoutParams.rightMargin = -(viewWidth - parentWidth) / 2
            }
        }
        layoutParams.addRule(RelativeLayout.CENTER_IN_PARENT);
        return layoutParams
    }
}