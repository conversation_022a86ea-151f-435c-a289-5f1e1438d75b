package com.fascin.chatter.component.player

import android.content.Context
import com.danikula.videocache.HttpProxyCacheServer


object SUHttpCacheServer {

    private lateinit var proxy: HttpProxyCacheServer

    fun init(context: Context) {
        proxy = HttpProxyCacheServer(context.applicationContext)
    }


    fun getProxyUrl(url: String): String {
        return proxy.getProxyUrl(url)
    }
}