package com.fascin.chatter.component.views

import android.animation.ValueAnimator
import android.content.Context
import android.os.CountDownTimer
import android.util.AttributeSet
import android.util.TypedValue
import android.view.View
import android.view.animation.DecelerateInterpolator
import android.widget.FrameLayout
import androidx.core.view.setPadding
import com.fascin.chatter.R
import com.fascin.chatter.bean.BonusTipEntity
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.toPx
import kotlinx.android.synthetic.main.layout_bonus_expandable.view.ivBonusIcon
import kotlinx.android.synthetic.main.layout_bonus_expandable.view.llBonus
import kotlinx.android.synthetic.main.layout_bonus_expandable.view.progressLoading
import kotlinx.android.synthetic.main.layout_bonus_expandable.view.tvBonusTip

class BonusExpandableLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    // 点击请求间隔
    private val REQUEST_DURATION = 60 * 1000L
    private val ANIMATION_DURATION = 300L
    private var requestTime: Long = 0L
    private var expandedWidth: Int = 0
    private var currentWidth: Int = 0
    private var isExpanded: Boolean = false

    private var iconResId: Int = 0
    private var animator: ValueAnimator? = null
    private var timer: CountDownTimer? = null

    private var padding: Int = 0
    private var iconSize: Int = 0
    private var collapsedIconSize: Int = 0
    private var expandedIconSize: Int = 0

    private var click: (() -> Unit?)? = null
    private var bonusTip: BonusTipEntity? = null

    init {
        View.inflate(context, R.layout.layout_bonus_expandable, this)
        expandedWidth = calculateExpandedWidth()
        collapsedIconSize = 38.toPx
        expandedIconSize = 28.toPx
        currentWidth = collapsedIconSize
        iconResId = R.drawable.ic_bonus_expanded
        iconSize = collapsedIconSize
        padding = 0
        llBonus.setBackgroundResource(R.drawable.bg_e6e6fa_r99)
        invalidateView()
        clickWithTrigger {
            // 处于收起状态，并且数据尚未请求或者是1分钟前请求的时，重新请求数据
            if (!isExpanded && (bonusTip == null || System.currentTimeMillis() - requestTime > REQUEST_DURATION)) {
                ivBonusIcon.show(shown = false, invisible = true)
                progressLoading.show(true)
                click?.invoke()
            } else {
                toggle()
            }
        }
    }

    /**
     * 设置数据
     */
    fun setTipData(entity: BonusTipEntity?) {
        requestTime = System.currentTimeMillis()
        ivBonusIcon.show(true)
        progressLoading.show(false)
        bonusTip = entity
        bonusTip?.let {
            ivBonusIcon.setImageResource(
                if (it.status == 2) R.drawable.ic_bonus_not_expand
                else R.drawable.ic_bonus_expanded
            )
            tvBonusTip.text = it.desc
            llBonus.setBackgroundResource(
                if (it.status == 2) R.drawable.bg_dadada_r99
                else R.drawable.bg_e6e6fa_r99
            )
            // 未展开时，展开
            if (!isExpanded && it.isClick) toggle()
        }
    }

    /**
     * 设置点击回调
     */
    fun setClickCallback(callback: () -> Unit?) {
        click = callback
    }

    /**
     * 切换展开状态
     */
    private fun toggle() {
        animator?.takeIf { it.isRunning }?.cancel()

        val startWidth = currentWidth
        val endWidth = if (isExpanded) collapsedIconSize else expandedWidth

        isExpanded = !isExpanded
        timer?.cancel()
        // 展开时，开启倒计时
        if (isExpanded) showDownCount()
        animator = ValueAnimator.ofInt(startWidth, endWidth).apply {
            duration = ANIMATION_DURATION
            interpolator = DecelerateInterpolator()
            addUpdateListener { animation ->
                currentWidth = animation.animatedValue as Int
                val fraction = animation.animatedFraction
                iconSize = if (isExpanded) {
                    (collapsedIconSize + (expandedIconSize - collapsedIconSize) * fraction).toInt()
                } else {
                    (expandedIconSize + (collapsedIconSize - expandedIconSize) * fraction).toInt()
                }
                padding =
                    ((if (isExpanded) fraction else (1 - fraction)) * TypedValue.applyDimension(
                        TypedValue.COMPLEX_UNIT_DIP,
                        10f,
                        resources.displayMetrics
                    )).toInt()
                invalidateView()
            }
        }
        animator?.start()
    }

    /**
     * 展示10s后自动收起
     */
    private fun showDownCount() {
        // 使用 CountDownTimer 实现倒计时
        timer = object : CountDownTimer(10 * 1000L, 1000L) {
            override fun onTick(millisUntilFinished: Long) {
            }

            override fun onFinish() {
                // 恢复为收起状态
                if (isExpanded) toggle()
            }
        }.start()
    }

    /**
     * 计算展开时的宽度
     */
    private fun calculateExpandedWidth(): Int {
        val displayMetrics = context.resources.displayMetrics
        val screenWidth = displayMetrics.widthPixels
        val margin = 56.toPx
        return screenWidth - margin
    }

    /**
     * 重绘
     */
    private fun invalidateView() {
        ivBonusIcon.layoutParams.width = iconSize
        ivBonusIcon.layoutParams.height = iconSize
        if (padding > 0) {
            llBonus.layoutParams.width = currentWidth
            llBonus.setPadding(padding)
            tvBonusTip.layoutParams.width = currentWidth - iconSize - padding
        } else {
            llBonus.layoutParams = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
            llBonus.setPadding(0)
            tvBonusTip.layoutParams.width = 0
        }
        tvBonusTip.show(isExpanded)
    }
}