package com.fascin.chatter.component.views

import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Point
import android.util.AttributeSet
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.utils.exts.castObject
import kotlin.math.max
import kotlin.math.min
import kotlin.math.pow
import kotlin.math.sqrt

@SuppressLint("ClickableViewAccessibility")
class ScaleAbleView(context: Context, attrs: AttributeSet?) : FrameLayout(context, attrs) {

    // 缩放的View
    private var scaleableView: View? = null
    private var isInScale: Boolean = false

    // 缩放的动画状态
    private var isClick: Boolean = false

    // 触摸时 双指中间的点 / 双指距离
    private var originalTwoPointerCenter: Point? = null
    private var originalDistance: Int = 0

    private var originalSinglePoint: Point? = null

    // 缩放view的初始left/top
    private var originalXY: IntArray? = null
    private var initialXPress = 0f
    private var initialYPress = 0f

    // 移动时 双指距离 缩放比例
    private var moveDistance: Int = 0
    private var moveScale: Float = 0.0f

    // 双指移动距离的增量比（用于计算缩放比、背景颜色）
    private var moveDistanceIncrement: Float = 0.0f

    // 缩放的View原LayoutParams
    private var originalLayoutParams: ViewGroup.LayoutParams? = null

    // 缩放View的LayoutParams
    private var scaleFrameLayoutParams: LayoutParams? = null

    private var singleStartPoint: Point = Point()

    //block
    var clickBlock: (() -> Unit)? = null

    init {
        scaleableView = getChildAt(0)
        this.setOnTouchListener { v, event ->
            if (scaleableView == null && this.childCount <= 0) false

            when (event.actionMasked) {
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    Log.d("ScaleAbleView", "touch,up or cancel:${event.actionMasked}")
                    requestDisallowInterceptTouchEvent(false)
                    if (isInScale) {
                        dismissWithAnimator()
                        isInScale = false
                    }

                    if (isClick && event.actionMasked == MotionEvent.ACTION_UP) {
                        clickBlock?.invoke()
                    }
                    isClick = false
                }

                MotionEvent.ACTION_POINTER_DOWN -> {
                    Log.d("ScaleAbleView", "touch, pointer count:${event.pointerCount}")
                    if (event.pointerCount == 2) {
                        isClick = false
                        requestDisallowInterceptTouchEvent(true)
                    }
                }

                MotionEvent.ACTION_POINTER_UP -> {
                    originalSinglePoint = null
                    Log.d("ScaleAbleView", "touch, pointer up")
                }

                MotionEvent.ACTION_DOWN -> {
                    if (scaleableView == null) {
                        scaleableView = getChildAt(0)
                    }
                    Log.d("ScaleAbleView", "touch, down:$scaleableView")
                    isClick = true
                    originalSinglePoint = null
                    initialXPress = event.x
                    initialYPress = event.y
                }

                MotionEvent.ACTION_MOVE -> {
                    Log.d("ScaleAbleView", "touch, move：${event.pointerCount}")
                    if (event.pointerCount == 2) {
                        originalSinglePoint = null
                        if (!isInScale) {//开始处理缩放view
                            getPageRootView()?.also { rootView ->
                                isInScale = true
                                originalXY = IntArray(2)
                                v.getLocationOnScreen(originalXY)
                                scaleFrameLayoutParams =
                                    LayoutParams(v.width, v.height).apply {
                                        leftMargin = originalXY!![0]
                                        topMargin = originalXY!![1]
                                    }

                                originalLayoutParams = scaleableView!!.layoutParams
                                if (scaleableView?.parent != null) {
                                    val parentView: ViewGroup = scaleableView?.parent as ViewGroup
                                    parentView.removeView(scaleableView)
                                }
                                rootView.addView(scaleableView, scaleFrameLayoutParams)
                            }

                            originalDistance = getDistance(event)
                            if (originalTwoPointerCenter == null) {
                                originalTwoPointerCenter = Point()
                            }
                            originalTwoPointerCenter?.x = getTwoPointerCenterX(event)
                            originalTwoPointerCenter?.y = getTwoPointerCenterY(event)
                        } else {
                            // 双指距离和距离比例
                            moveDistance = getDistance(event)
                            moveDistanceIncrement =
                                (moveDistance.toFloat() - originalDistance.toFloat()) / originalDistance.toFloat()
                            // 关键点：
                            // 1.设置pivotX和pivotY为view左上角，相比View中心点更容易计算缩放后的位移
                            // 2.位移计算公式 （触摸屏幕时的坐标 * 缩放比 = 缩放后的坐标，当前两指中心点 - 缩放后的坐标 + 触摸屏幕时的leftMargin和topMargin = left和top最终需要的位移）
                            //   leftMargin = 当前两指中心点的x坐标 - 首次触摸屏幕时两指中心点的x坐标 乘以 缩放比 + 首次触摸时的原始leftMargin
                            //   topMargin同上，将x换成y
                            // 缩放
                            moveScale = 1 + moveDistanceIncrement
                            moveScale = max(0.5f, moveScale)
                            moveScale = min(5.0f, moveScale)
                            scaleableView?.run {
                                scaleX = moveScale
                                scaleY = moveScale
                            }

                            // 位移
                            if (originalTwoPointerCenter != null && originalXY != null) {
                                updateOffset(
                                    (getTwoPointerCenterX(event) - originalTwoPointerCenter!!.x) + (originalXY!![0]).toFloat(),
                                    (getTwoPointerCenterY(event) - originalTwoPointerCenter!!.y) + (originalXY!![1]).toFloat()
                                )
                            }
                        }
                    } else if (event.pointerCount == 1 && isInScale && scaleFrameLayoutParams != null) {
                        if (originalSinglePoint == null) {
                            singleStartPoint.x = scaleFrameLayoutParams!!.leftMargin
                            singleStartPoint.y = scaleFrameLayoutParams!!.topMargin
                            originalSinglePoint = Point()
                            originalSinglePoint?.x = event.x.toInt()
                            originalSinglePoint?.y = event.y.toInt()
                        } else {
                            scaleFrameLayoutParams?.run {
                                leftMargin =
                                    singleStartPoint.x + event.x.toInt() - originalSinglePoint!!.x
                                topMargin =
                                    singleStartPoint.y + event.y.toInt() - originalSinglePoint!!.y
                            }
                            scaleableView?.layoutParams = scaleFrameLayoutParams
                        }
                    }
                }

                else -> {}
            }
            true
        }
    }


    fun setUnScaleAble() {
        this.setOnTouchListener(null)
    }

    private
    fun dismissWithAnimator() {
        scaleableView?.also {
            if (!isInScale ||
                originalXY == null ||
                scaleFrameLayoutParams == null ||
                it.parent == this
            ) return

            Log.d("ScaleAbleView", "do dismissWithAnimator")
            val scaleStart = it.scaleY
            val leftMarginStart = scaleFrameLayoutParams!!.leftMargin
            val topMarginStart = scaleFrameLayoutParams!!.topMargin

            val scaleEnd = 1f
            val leftMarginEnd = originalXY!![0]
            val topMarginEnd = originalXY!![1]

            val valueAnimator = ValueAnimator.ofFloat(0f, 1f)
            valueAnimator.duration = 200
            valueAnimator.addUpdateListener { anim ->
                it.scaleX = ((scaleEnd - scaleStart) * anim.animatedFraction) + scaleStart
                it.scaleY = ((scaleEnd - scaleStart) * anim.animatedFraction) + scaleStart
                updateOffset(
                    ((leftMarginEnd - leftMarginStart) * anim.animatedFraction) + leftMarginStart,
                    ((topMarginEnd - topMarginStart) * anim.animatedFraction) + topMarginStart
                )

                if (anim.animatedFraction == 1.0f) {
                    returnViewToOriginalContainer()
                    valueAnimator.removeAllUpdateListeners()
                }
            }
            valueAnimator.start()
        }
    }

    // 关闭弹窗、view复原（在回弹动画结束之后）
    private fun returnViewToOriginalContainer() {
        scaleableView?.takeIf { it.parent != this && it.parent != null }?.also {
            val curParent = it.parent as ViewGroup
            Log.d("ScaleAbleView", "return view to original container")
            curParent?.removeView(it)
            this.addView(it, originalLayoutParams)
        }
    }

    // 更新位移
    private fun updateOffset(left: Float, top: Float) {
        scaleFrameLayoutParams?.run {
            leftMargin = left.toInt()
            topMargin = top.toInt()
        }
        scaleableView?.layoutParams = scaleFrameLayoutParams
    }

    // 获取两指的距离
    private fun getDistance(ev: MotionEvent): Int {
        return sqrt(
            (ev.getX(1).toDouble() - ev.x.toDouble()).pow(2.0) + (ev.getY(1)
                .toDouble() - ev.y.toDouble()).pow(2.0)
        ).toInt()
    }

    // 获取两指的中心点x坐标
    private fun getTwoPointerCenterX(ev: MotionEvent): Int {
        return ((ev.getX(0) + ev.getX(1)) / 2).toInt()
    }

    // 获取两指的中心点y坐标
    private fun getTwoPointerCenterY(ev: MotionEvent): Int {
        return ((ev.getY(0) + ev.getY(1)) / 2).toInt()
    }

    private fun getPageRootView(): FrameLayout? {
        return AppContext.getTopActivity()?.window?.decorView?.findViewById<View>(android.R.id.content)
            ?.castObject<FrameLayout>()
    }
}