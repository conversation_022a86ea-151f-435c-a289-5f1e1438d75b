package com.fascin.chatter.component.views

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import androidx.appcompat.widget.LinearLayoutCompat
import com.fascin.chatter.R
import com.fascin.chatter.bean.MpcRepossessionEntity
import com.iandroid.allclass.lib_common.utils.exts.show
import kotlinx.android.synthetic.main.view_repossession.view.llContent

/**
 * @Desc:MPC页面回收预警信息
 * @Created: Quan
 * @Date: 2024/11/4
 */
class RepossessionView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    init {
        View.inflate(context, R.layout.view_repossession, this)
    }

    fun setData(data: MpcRepossessionEntity?) {
        show(data?.showRisk == 1)
        if (data?.showRisk == 1) {
            val childCount = llContent.childCount
            for (i in 0 until childCount) {
                val childAt = llContent.getChildAt(i)
                if (childAt != null && childAt is RepossessionDescView) {
                    llContent.removeView(childAt)
                }
            }
            data.rules?.forEach {
                val itemView = RepossessionDescView(context).apply {
                    layoutParams = LinearLayoutCompat.LayoutParams(
                        LinearLayoutCompat.LayoutParams.MATCH_PARENT,
                        LinearLayoutCompat.LayoutParams.WRAP_CONTENT
                    )
                    setDescData(it)
                }
                llContent.addView(itemView)
            }
        }
    }
}