/*
 * Copyright 2019 Looping Layout
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


package com.fascin.chatter.component.views.looplayout

import android.view.View
import kotlin.math.abs

/**
 * The default view picker used when one is not provided.
 * @return A view with the given adapter index.
 */
fun defaultPicker(
    targetAdapterIndex: Int,
    layoutManager: LoopingLayoutManager
): View? {
    return childClosestToMiddle(targetAdapterIndex, layoutManager)
}

/**
 * Returns a view with the given [targetAdapterIndex]. If there are multiple views associated with the
 * given index, this returns the view whose middle is closest to the middle of the layout.
 */
fun childClosestToMiddle(
    targetAdapterIndex: Int,
    layoutManager: LoopingLayoutManager
): View? {
    var minDistance = Int.MAX_VALUE
    var closestView: View? = null
    val layoutMiddle = if (layoutManager.orientation == LoopingLayoutManager.HORIZONTAL) {
        layoutManager.paddingLeft + (layoutManager.layoutWidth / 2)
    } else {
        layoutManager.paddingTop + (layoutManager.layoutHeight / 2)
    }
    for (i in 0 until layoutManager.childCount) {
        val view = layoutManager.getChildAt(i) ?: return null
        if (layoutManager.getPosition(view) != targetAdapterIndex) {
            continue
        }
        val childMiddle = if (layoutManager.orientation == LoopingLayoutManager.HORIZONTAL) {
            layoutManager.getDecoratedLeft(view) +
                    (layoutManager.getDecoratedMeasuredWidth(view) / 2)
        } else {
            layoutManager.getDecoratedTop(view) +
                    (layoutManager.getDecoratedMeasuredHeight(view) / 2)
        }
        val distance = abs(childMiddle - layoutMiddle)
        if (distance < minDistance) {
            minDistance = distance
            closestView = view
        }
    }
    return closestView
}
