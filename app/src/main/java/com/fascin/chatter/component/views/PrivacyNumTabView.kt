package com.fascin.chatter.component.views

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import com.fascin.chatter.R
import kotlinx.android.synthetic.main.view_privacy_num_tab.view.tvHistory
import kotlinx.android.synthetic.main.view_privacy_num_tab.view.tvToday
import kotlinx.android.synthetic.main.view_privacy_num_tab.view.tvYesterday

/**
 * @Desc:
 * @Created: Quan
 * @Date: 2024/6/6
 */
class PrivacyNumTabView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    init {
        View.inflate(context, R.layout.view_privacy_num_tab, this)
    }

    fun setData(hNum: Int, yNum: Int, tNum: Int) {
        tvHistory.text = hNum.coerceAtLeast(0).toString()
        tvYesterday.text = yNum.coerceAtLeast(0).toString()
        tvToday.text = tNum.coerceAtLeast(0).toString()
    }
}