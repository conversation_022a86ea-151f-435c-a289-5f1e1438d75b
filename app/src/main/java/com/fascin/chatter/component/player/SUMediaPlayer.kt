package com.fascin.chatter.component.player

import android.content.Context
import android.graphics.SurfaceTexture
import android.net.Uri
import android.util.Log
import android.view.*
import android.widget.FrameLayout
import android.widget.RelativeLayout
import com.fascin.chatter.component.player.render.IRenderView
import com.fascin.chatter.component.player.render.TextureRenderView
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.MediaItem
import com.google.android.exoplayer2.Player
import com.google.android.exoplayer2.Player.REPEAT_MODE_ONE
import com.google.android.exoplayer2.video.VideoSize
import com.iandroid.allclass.lib_common.AppContext

class SUMediaPlayer(
    context: Context
) : TextureView.SurfaceTextureListener,
    Player.Listener {

    private var mediaPlayer: ExoPlayer? = null
    private val renderView = TextureRenderView(context)
    private val renderLayout = FrameLayout(context)
    private var renderCallback: SURenderCallback? = null

    init {
        renderView.surfaceTextureListener = this
        renderView.setAspectRatio(IRenderView.AR_MATCH_PARENT)
        renderLayout.addView(
            renderView, FrameLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT,
                Gravity.CENTER
            )
        )
        renderLayout.addOnAttachStateChangeListener(object : View.OnAttachStateChangeListener {
            override fun onViewAttachedToWindow(p0: View) {

            }

            override fun onViewDetachedFromWindow(p0: View) {
                releasePlayer()
            }
        })
        renderView.addOnAttachStateChangeListener(
            object : View.OnAttachStateChangeListener {
                override fun onViewAttachedToWindow(p0: View) {
                    Log.d("David", "renderView==onViewAttachedToWindow")
                }

                override fun onViewDetachedFromWindow(p0: View) {
                    Log.d("David", "renderView==onViewDetachedFromWindow")
                }
            }
        )
    }

    override fun onPlaybackStateChanged(playbackState: Int) {
        super.onPlaybackStateChanged(playbackState)
        when (playbackState) {
            Player.STATE_READY -> {
//                this.renderCallback?.onRenderStart()
            }
            Player.STATE_BUFFERING -> {

            }
            Player.STATE_ENDED -> {

            }
            Player.STATE_IDLE -> {

            }
        }
    }

    override fun onVideoSizeChanged(videoSize: VideoSize) {
        super.onVideoSizeChanged(videoSize)
        renderView.setVideoSize(videoSize.width, videoSize.height)

        var videoWidth = videoSize.width
        var videoHeight = videoSize.height
        if (videoWidth <= 0 || videoHeight <= 0) return

        val degree = renderView.rotation
        if (degree % 180 != 0F) {
            videoWidth = videoSize.height
            videoHeight = videoSize.width
        }

        val viewParent: ViewParent = renderLayout.parent
        if (viewParent == null || viewParent !is ViewGroup) return

        val parentWidth = viewParent.width
        var parentHeight = viewParent.height

        if (parentHeight == 0) {
            parentHeight = parentWidth * 520 / 390
        }

        if (parentWidth > 0 && parentHeight > 0) {
            renderLayout.layoutParams = SURenderLPClip.getLayoutParams(
                videoWidth,
                videoHeight,
                parentWidth,
                parentHeight
            )
            renderView.requestLayout()
        }
    }

    override fun onRenderedFirstFrame() {
        super.onRenderedFirstFrame()
        this.renderCallback?.onRenderStart()
    }

    fun attach(videoContainer: RelativeLayout, renderCallback: SURenderCallback) {
        this.renderCallback = renderCallback
        detach()
        videoContainer.addView(
            renderLayout,
            RelativeLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT,
            )
        )
    }

    fun detach() {
        (renderLayout.parent as? RelativeLayout)?.also {
            it.removeAllViews()
        }
    }

    override fun onSurfaceTextureAvailable(surface: SurfaceTexture, width: Int, height: Int) {
        mediaPlayer?.setVideoSurface(Surface(surface))
    }

    override fun onSurfaceTextureSizeChanged(surface: SurfaceTexture, width: Int, height: Int) {
    }

    override fun onSurfaceTextureDestroyed(surface: SurfaceTexture): Boolean {
        surface.release()
        mediaPlayer?.setVideoSurface(null)
        return false
    }

    override fun onSurfaceTextureUpdated(surface: SurfaceTexture) {
    }

    fun setDataSource(url: String) {
        try {
            releasePlayer()
            createPlayer()
            val mediaItem: MediaItem = MediaItem.fromUri(Uri.parse(url))
            mediaPlayer?.setMediaItem(mediaItem)
            mediaPlayer?.prepare()
            mediaPlayer?.playWhenReady = true
            suGlobalVideoVolume = 0.0f
            setVolume(suGlobalVideoVolume)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun start() {
        try {
            mediaPlayer?.play()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun pause() {
        try {
            mediaPlayer?.pause()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun setVolume(volume: Float) {
        try {
            mediaPlayer?.volume = volume
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun createPlayer() {
        mediaPlayer = ExoPlayer.Builder(AppContext.context).build()
        mediaPlayer?.addListener(this)
        mediaPlayer?.repeatMode = REPEAT_MODE_ONE
    }

    private fun releasePlayer() {
        mediaPlayer?.setVideoSurface(null)
        mediaPlayer?.release()
        mediaPlayer = null
    }
}

interface SURenderCallback {
    fun onRenderStart()
}