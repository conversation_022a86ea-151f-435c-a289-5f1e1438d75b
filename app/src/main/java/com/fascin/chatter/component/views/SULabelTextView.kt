package com.fascin.chatter.component.views

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import android.widget.TextView
import com.fascin.chatter.R
import com.iandroid.allclass.lib_common.utils.exts.show
import kotlinx.android.synthetic.main.item_label_text.view.*

class SULabelTextView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {


    init {
        View.inflate(context, R.layout.item_label_text, this)
    }

    fun initLabelText(
        title: String,
        content: String,
        onClick: (() -> Unit)? = null
    ) {
        label_text_title.text = title
        label_text_content.text = content
        label_text_arrow.show(onClick != null)
        onClick?.let {
            setOnClickListener {
                it()
            }
        }
    }

    fun updateContent(content: String) {
        label_text_content.text = content
    }

    fun getLabelContentView(): TextView {
        return label_text_content
    }
}