package com.fascin.chatter.im.view

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.AttributeSet
import android.util.Log
import android.view.LayoutInflater
import androidx.constraintlayout.motion.widget.MotionLayout
import com.bumptech.glide.Glide
import com.fascin.chatter.R
import com.fascin.chatter.bean.chat.GiftEntity
import com.fascin.chatter.databinding.ViewChatGiftBinding
import com.fascin.chatter.im.msg.GiftMessage
import com.iandroid.allclass.lib_common.utils.exts.jsonToObj
import io.rong.imkit.model.UiMessage

class ChatGiftView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : MotionLayout(context, attrs) {

    private val binding: ViewChatGiftBinding = ViewChatGiftBinding.inflate(LayoutInflater.from(context), this, true)
    private val handler = Handler(Looper.getMainLooper())
    private var animationCallback: (() -> Unit)? = null
    private var currentUiMessage: UiMessage? = null


    fun bindData(uiMessage: UiMessage?) {
        if (uiMessage == null) return

        // 保存当前消息引用
        currentUiMessage = uiMessage

        val isOpened = isGiftOpened(uiMessage)
        Log.d("ChatGiftView", "bindData: isOpened = $isOpened")

        bindData2View(uiMessage)

        // 确保在主线程中设置状态，并且在 View 完成布局后
        post {
            // 根据礼物状态设置初始状态
            if (isOpened) {
                // 已拆封，直接跳转到拆封后状态
                Log.d("ChatGiftView", "Setting opened state")
                setTransition(R.id.transition_opening_to_opened)
                jumpToState(R.id.opened)
                Log.d("ChatGiftView", "Opened state set, currentState: $currentState")
            } else {
                // 未拆封，跳转到初始状态
                Log.d("ChatGiftView", "Setting unopened state")
                setTransition(R.id.transition_unopened_to_opening)
                jumpToState(R.id.unopened)
                Log.d("ChatGiftView", "Unopened state set, currentState: $currentState")
            }
        }
    }

    /**
     * 开始拆封动画
     * @param callback 动画完成后的回调
     */
    fun play(callback: (() -> Unit)? = null) {
        Log.d("ChatGiftView", "play() called")
        animationCallback = callback

        // 获取当前消息状态
        val currentUiMessage = getCurrentUiMessage()
        val isOpened = isGiftOpened(currentUiMessage)

        Log.d("ChatGiftView", "isOpened: $isOpened, currentTransition: ${currentState}")

        if (isOpened) {
            // 如果礼物已经打开，直接播放展示动画
            Log.d("ChatGiftView", "Playing display animation for opened gift")
            playDisplayAnimationSimple()
        } else {
            // 如果礼物未打开，播放完整的拆封动画
            Log.d("ChatGiftView", "Starting unwrap animation for unopened gift")
            startUnwrapAnimation()
        }
    }

    private fun bindData2View(uiMessage: UiMessage) {
        val messageContent: GiftMessage = uiMessage.message.content as? GiftMessage ?: return
        val data = messageContent.content.jsonToObj<GiftEntity>() ?: return

        binding.tvGiftName.text = data.name
        Glide.with(context).asGif().load(data.gif).into(binding.ivGift)
    }

    /**
     * 开始拆封动画序列
     */
    private fun startUnwrapAnimation() {
        Log.d("ChatGiftView", "startUnwrapAnimation() called")

        // 设置动画监听器
        setTransitionListener(object : TransitionListener {
            override fun onTransitionStarted(motionLayout: MotionLayout?, startId: Int, endId: Int) {
                Log.d("ChatGiftView", "Unwrap animation started: $startId -> $endId")
            }

            override fun onTransitionChange(motionLayout: MotionLayout?, startId: Int, endId: Int, progress: Float) {}

            override fun onTransitionCompleted(motionLayout: MotionLayout?, currentId: Int) {
                Log.d("ChatGiftView", "Unwrap animation completed: $currentId")
                when (currentId) {
                    R.id.opening -> {
                        // 拆封中动画完成，立即切换到拆封后状态
                        Log.d("ChatGiftView", "Opening completed, transitioning to opened")
                        setTransition(R.id.transition_opening_to_opened)
                        transitionToEnd()
                    }
                    R.id.opened -> {
                        // 拆封后状态，延迟1秒后开始最终动画
                        Log.d("ChatGiftView", "Opened completed, scheduling final animation")
                        handler.postDelayed({
                            setTransition(R.id.transition_opened_to_finished)
                            transitionToEnd()
                        }, 1000)
                    }
                    R.id.finished -> {
                        // 所有动画完成
                        Log.d("ChatGiftView", "All animations finished, calling callback")
                        animationCallback?.invoke()
                        setTransitionListener(null) // 清除监听器
                    }
                }
            }

            override fun onTransitionTrigger(motionLayout: MotionLayout?, triggerId: Int, positive: Boolean, progress: Float) {}
        })

        // 开始第一个动画：未拆封 -> 拆封中 (2秒)
        Log.d("ChatGiftView", "Starting first transition: unopened -> opening")
        setTransition(R.id.transition_unopened_to_opening)
        transitionToEnd()
    }

    /**
     * 获取消息状态 0 未打开  1已打开
     */
    private fun isGiftOpened(uiMessage: UiMessage?): Boolean {
        var status = 0

        if (uiMessage?.expansion != null) {
            for ((k, v) in uiMessage.expansion) {
                if (k == "status") {
                    status = v.toIntOrNull() ?: 0
                }
            }
        }
        return status == 1
    }

    /**
     * 获取当前保存的 UiMessage
     */
    private fun getCurrentUiMessage(): UiMessage? = currentUiMessage

    /**
     * 播放已打开礼物的展示动画（从状态3开始）
     */
    private fun playDisplayAnimationSimple() {
        Log.d("ChatGiftView", "playDisplayAnimationSimple() called - starting from opened state")

        // 设置为状态3（拆封后状态）
        setToOpenedState()

        // 延迟1秒后开始过渡到完成状态
        postDelayed({
            transitionToFinishedState()
        }, 1000)
    }

    /**
     * 设置为状态3：拆封后状态
     */
    private fun setToOpenedState() {
        Log.d("ChatGiftView", "Setting to opened state (state 3)")

        // 状态3: 拆封后状态
        binding.bg.visibility = android.view.View.VISIBLE
        binding.bg.alpha = 1.0f

        binding.bgCover.visibility = android.view.View.VISIBLE
        binding.bgCover.alpha = 1.0f

        binding.ivGift.visibility = android.view.View.VISIBLE
        binding.ivGift.alpha = 1.0f
        // 设置礼物大小为74dp
        val layoutParams = binding.ivGift.layoutParams
        layoutParams.width = (74 * resources.displayMetrics.density).toInt()
        layoutParams.height = (74 * resources.displayMetrics.density).toInt()
        binding.ivGift.layoutParams = layoutParams

        binding.ivGiftBox.visibility = android.view.View.GONE
        binding.ivGiftBox.alpha = 0.0f

        binding.ivGiftBoxCover.visibility = android.view.View.GONE
        binding.ivGiftBoxCover.alpha = 0.0f

        binding.tvGiftName.visibility = android.view.View.VISIBLE
        binding.tvGiftName.alpha = 1.0f
        binding.tvGiftName.textSize = 16f

        binding.tvClickView.visibility = android.view.View.GONE
        binding.tvClickView.alpha = 0.0f
    }

    /**
     * 过渡到状态4：完成状态
     */
    private fun transitionToFinishedState() {
        Log.d("ChatGiftView", "Transitioning to finished state (state 4)")

        // 创建淡出动画
        val fadeOutBg = android.animation.ObjectAnimator.ofFloat(binding.bg, "alpha", 1.0f, 0.0f)
        val fadeOutCover = android.animation.ObjectAnimator.ofFloat(binding.bgCover, "alpha", 1.0f, 0.0f)
        val fadeOutName = android.animation.ObjectAnimator.ofFloat(binding.tvGiftName, "alpha", 1.0f, 0.0f)

        val animatorSet = android.animation.AnimatorSet()
        animatorSet.playTogether(fadeOutBg, fadeOutCover, fadeOutName)
        animatorSet.duration = 1000

        animatorSet.addListener(object : android.animation.AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: android.animation.Animator) {
                Log.d("ChatGiftView", "Animation finished, calling callback")
                animationCallback?.invoke()
            }
        })

        animatorSet.start()
    }

    /**
     * 播放已打开礼物的展示动画
     */
    private fun playDisplayAnimation() {
        Log.d("ChatGiftView", "playDisplayAnimation() called")

        // 确保 View 已经完成布局
        post {
            Log.d("ChatGiftView", "Post runnable executed, currentState: $currentState")
            Log.d("ChatGiftView", "MotionLayout width: $width, height: $height")
            Log.d("ChatGiftView", "MotionLayout isLaidOut: $isLaidOut")

            // 检查资源 ID
            val openedId = R.id.opened
            val finishedId = R.id.finished
            val transitionId = R.id.transition_opening_to_opened
            Log.d("ChatGiftView", "Resource IDs - opened: $openedId, finished: $finishedId, transition: $transitionId")

            // 强制设置到 opened 状态
            setTransition(R.id.transition_opening_to_opened)
            jumpToState(R.id.opened)

            Log.d("ChatGiftView", "Jumped to opened state, currentState: $currentState")

            // 设置动画监听器
            setTransitionListener(object : TransitionListener {
                override fun onTransitionStarted(motionLayout: MotionLayout?, startId: Int, endId: Int) {
                    Log.d("ChatGiftView", "Display animation started: $startId -> $endId")
                }

                override fun onTransitionChange(motionLayout: MotionLayout?, startId: Int, endId: Int, progress: Float) {
                    Log.d("ChatGiftView", "Display animation progress: $progress")
                }

                override fun onTransitionCompleted(motionLayout: MotionLayout?, currentId: Int) {
                    Log.d("ChatGiftView", "Display animation completed: $currentId")
                    when (currentId) {
                        R.id.finished -> {
                            // 展示动画完成
                            Log.d("ChatGiftView", "Animation finished, calling callback")
                            animationCallback?.invoke()
                            setTransitionListener(null) // 清除监听器
                        }
                    }
                }

                override fun onTransitionTrigger(motionLayout: MotionLayout?, triggerId: Int, positive: Boolean, progress: Float) {}
            })

            // 延迟一下再开始动画，确保状态设置完成
            postDelayed({
                Log.d("ChatGiftView", "Starting transition to finished, currentState: $currentState")
                setTransition(R.id.transition_opened_to_finished)
                transitionToEnd()
            }, 200)
        }
    }
}