package com.fascin.chatter.main.profile

import android.content.Intent
import android.os.Bundle
import android.view.Gravity
import android.view.View
import androidx.core.view.isVisible
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.fascin.chatter.BuildConfig
import com.fascin.chatter.R
import com.fascin.chatter.account.newbie.navigation.BaseTaskActivity
import com.fascin.chatter.bean.BindModelIntent
import com.fascin.chatter.bean.ChatterLevelEntity
import com.fascin.chatter.bean.ChatterShiftEntity
import com.fascin.chatter.bean.MineInfoEntity
import com.fascin.chatter.bean.PenaltyUnreadEntity
import com.fascin.chatter.bean.event.UICSUnderCountEvent
import com.fascin.chatter.bean.event.UISelectModelEvent
import com.fascin.chatter.config.Config
import com.fascin.chatter.dialog.NewAnchorFinishTaskDialog
import com.fascin.chatter.main.HomeConfig
import com.fascin.chatter.main.MainActivity
import com.fascin.chatter.main.profile.adapter.ModelListAdapter
import com.fascin.chatter.main.profile.invite.BindInviteCodeDialog
import com.fascin.chatter.main.profile.withdraw.BalanceDetailsActivity
import com.fascin.chatter.main.profile.withdraw.WithdrawViewModel
import com.fascin.chatter.main.view.TopHostDialog
import com.fascin.chatter.repository.AppRepository
import com.fascin.chatter.utils.SUStringUtils
import com.iandroid.allclass.lib_basecore.ActivityStack
import com.iandroid.allclass.lib_basecore.base.BaseUiFragment
import com.iandroid.allclass.lib_basecore.utils.DateUtils
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.AppController
import com.iandroid.allclass.lib_common.UserController
import com.iandroid.allclass.lib_common.Values
import com.iandroid.allclass.lib_common.beans.UIEventAppUpdate
import com.iandroid.allclass.lib_common.beans.UIEventTaskRefresh
import com.iandroid.allclass.lib_common.beans.WebIntent
import com.iandroid.allclass.lib_common.event.EventProfileUpdate
import com.iandroid.allclass.lib_common.repository.CommonRepository
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.GlideCacheUtils
import com.iandroid.allclass.lib_common.utils.NotificationUtil
import com.iandroid.allclass.lib_common.utils.SPConstants
import com.iandroid.allclass.lib_common.utils.SPUtils
import com.iandroid.allclass.lib_common.utils.ToastUtils
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.castObject
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.views.CommonVerticalAlertDialog
import io.rong.imkit.RongIM
import io.rong.imkit.manager.UnReadMessageManager
import io.rong.imkit.userinfo.RongUserInfoManager
import io.rong.imlib.IRongCoreCallback
import io.rong.imlib.IRongCoreEnum
import io.rong.imlib.RongCoreClient
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.UserInfo
import kotlinx.android.synthetic.main.fragment_profile.btnWithDraw
import kotlinx.android.synthetic.main.fragment_profile.parentCourse
import kotlinx.android.synthetic.main.fragment_profile.clearCacheBtn
import kotlinx.android.synthetic.main.fragment_profile.flRv
import kotlinx.android.synthetic.main.fragment_profile.isOnlineNoticeOff
import kotlinx.android.synthetic.main.fragment_profile.ivAddModel
import kotlinx.android.synthetic.main.fragment_profile.messageSetting
import kotlinx.android.synthetic.main.fragment_profile.modelList
import kotlinx.android.synthetic.main.fragment_profile.mpcHasNew
import kotlinx.android.synthetic.main.fragment_profile.nickName
import kotlinx.android.synthetic.main.fragment_profile.receivingAccount
import kotlinx.android.synthetic.main.fragment_profile.rlInvite
import kotlinx.android.synthetic.main.fragment_profile.settingCService
import kotlinx.android.synthetic.main.fragment_profile.settingCheckUpdate
import kotlinx.android.synthetic.main.fragment_profile.settingFeature
import kotlinx.android.synthetic.main.fragment_profile.settingInvite
import kotlinx.android.synthetic.main.fragment_profile.settingNotify
import kotlinx.android.synthetic.main.fragment_profile.settingPenalties
import kotlinx.android.synthetic.main.fragment_profile.trafficSetting
import kotlinx.android.synthetic.main.fragment_profile.tvCServiceNum
import kotlinx.android.synthetic.main.fragment_profile.tvCacheSize
import kotlinx.android.synthetic.main.fragment_profile.tvCurrentBalance
import kotlinx.android.synthetic.main.fragment_profile.tvInviteNew
import kotlinx.android.synthetic.main.fragment_profile.tvPaid
import kotlinx.android.synthetic.main.fragment_profile.tvPenaltiesNum
import kotlinx.android.synthetic.main.fragment_profile.tvPending
import kotlinx.android.synthetic.main.fragment_profile.tvTotalEarned
import kotlinx.android.synthetic.main.fragment_profile.tvVersionCode
import kotlinx.android.synthetic.main.fragment_profile.tvViewAllDetails
import kotlinx.android.synthetic.main.fragment_profile.updateNew
import kotlinx.android.synthetic.main.fragment_profile.userID
import kotlinx.android.synthetic.main.fragment_profile.topCustomService
import kotlinx.android.synthetic.main.fragment_profile.parentPenalties
import kotlinx.android.synthetic.main.fragment_profile.tvFraction
import kotlinx.android.synthetic.main.fragment_profile.shiftEntranceView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.launch

class ProfileFragment : BaseUiFragment(), UnReadMessageManager.IUnReadMessageObserver {

    private var viewModel: ProfileViewModel? = null
    private var salaryUrl = ""
    private var payrollUrl = ""
    private val modelListAdapter by lazy { ModelListAdapter(context) }
    private var csTotalUnderCount = 0
    private var penaltyUnreadCount = 0

    private val withdrawViewModel by lazy {
        ViewModelProvider(
            this,
            WithdrawViewModel.ViewModeFactory()
        )[WithdrawViewModel::class.java]
    }

    override fun attachLayoutId(): Int {
        return R.layout.fragment_profile
    }

    override fun startRefresh() {
        super.startRefresh()
        viewModel?.getMineInfo()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViewUIData()
        setListener()
        tvVersionCode.text = buildString {
            append("V")
            append(BuildConfig.VERSION_NAME)
        }
    }

    private fun initViewUIData() {
        viewModel = ViewModelProvider(this).get(ProfileViewModel::class.java)
        modelList.adapter = modelListAdapter
        modelList.layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        updateUserInfo()
        tvCacheSize.text = GlideCacheUtils.getCacheSizeByUnit()
        viewModel?.getMineInfo()
//        trafficRoot.show(AppController.isShowMatchTraffic())
        settingCheckUpdate.setOnClickListener {
            AppRepository.fetchAppUpgradeInfo(true)
        }
        updateNew.show(AppController.isNeedShowUpdateRedDot())

        clearCacheBtn.clickWithTrigger {
            lifecycleScope.launch {
                async(Dispatchers.IO) {
                    GlideCacheUtils.clearImageCache()
                }.await().also {
                    ToastUtils.showToast("Cleared successfully")
                    tvCacheSize.text = GlideCacheUtils.getCacheSizeByUnit()
                }
            }
        }
        viewModel?.pushFlagUpdate(if (NotificationUtil.isNotifyEnabled(context)) 1 else 0)
        postDelayed({
            getCSTotalUnderCount()
        }, 3000)
        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(UICSUnderCountEvent::class) {
            getCSTotalUnderCount()
        })
    }

    private fun setListener() {
        viewModel?.apply {
            // 监听用户信息变化
            mineInfoResult.observe(viewLifecycleOwner) {
                newAnchorFlow(it.newChatterStatus, it.totalReward)
                SPUtils.put(context, SPConstants.KEY_PUSH_CONFIG, it.pushConfig)
                isHaveNoticeOff()
                salaryUrl = it.salaryUrl
                payrollUrl = it.payrollUrl
                uploadAccount(it.payrollStatus)
                tvPenaltiesNum.show(it.penaltyUnreadNum > 0)
                tvPenaltiesNum.text = "${it.penaltyUnreadNum}"
                if (it.level.isNotEmpty()) {
                    SimpleRxBus.post(ChatterLevelEntity().apply { level = it.level })
                }
                updateUserInfo()
                HomeConfig.isNewChatter = it.isNewChatter
                // 显示新bie中心按钮
                getMainPage()?.showNewbieCenterBtn(it.isNewChatter == 1, it.newChatterStatus == 2)
                RongUserInfoManager.getInstance().refreshUserInfoCache(
                    UserInfo(it.userId, it.nickname, null)
                )
                //解绑Mode数据不显示
                val filteredList = it.modelsList?.filter { modelList ->
                    modelList.status != 1
                }
                if (it.modelsList.isNullOrEmpty() || filteredList.isNullOrEmpty()) {
                    flRv.visibility = View.GONE
                } else {
                    flRv.visibility = View.VISIBLE
                }
                setAddModelView(it.remainModelNum)
                modelListAdapter.updateData(filteredList)
                inviteFriends(it)

                if (it.isNewChatter != 1) withdraw()
                // mineInfo接口完成后，获取引导数据 //TODO 关闭引导 by mask
//                getMainPage()?.checkGuide()
            }

            mineInfoError.observe(viewLifecycleOwner) {
                // mineInfo接口完成后，获取引导数据  //TODO 关闭引导 by mask
//                getMainPage()?.checkGuide()
            }

            compositeDisposable?.add(SimpleRxBus.observe(UIEventAppUpdate::class) {
                updateNew.show(AppController.isNeedShowUpdateRedDot())
            })

            //主播分等级变更通知
            viewModel?.compositeDisposable?.add(SimpleRxBus.observe(ChatterLevelEntity::class) {
                tvFraction.show(it.level.isNotEmpty())
                tvFraction.text = it.level.orEmpty()
            })

            //主播排班更新
            viewModel?.compositeDisposable?.add(SimpleRxBus.observe(ChatterShiftEntity::class) {
                shiftEntranceView.setData(it.data)
            })

            // 退登回调
            delAccountResult.observe(viewLifecycleOwner) {
                UserController.loginOut()
                context.routeAction(ActionType.actionToLogin)
                activity?.finish()
            }

            // 监听是否去获取用户信息
            compositeDisposable?.add(
                SimpleRxBus.observe(EventProfileUpdate::class) {
                    viewModel?.getMineInfo()
                }
            )

            //未读质检通知
            compositeDisposable?.add(
                SimpleRxBus.observe(PenaltyUnreadEntity::class) {
                    penaltyUnreadCount = it.num
                    tvPenaltiesNum.show(penaltyUnreadCount > 0)
                    tvPenaltiesNum.text = "$penaltyUnreadCount"
                    getMainPage()?.updateMeRedDot(penaltyUnreadCount > 0 || csTotalUnderCount > 0, fromSysNotify = true)
                }
            )
            // 添加了model
            compositeDisposable?.add(SimpleRxBus.observe(UISelectModelEvent::class) {
                viewModel?.getMineInfo()
            })
        }

        RongIM.getInstance().addUnReadMessageCountChangedObserver(
            this,
            Conversation.ConversationType.SYSTEM
        )

        messageSetting.setOnClickListener {
            context.routeAction(ActionType.actionMessageSetting)
        }

        // 添加model入口
        ivAddModel?.clickWithTrigger {
            viewModel?.mineInfoResult?.value?.let { entity ->
                if (entity.remainModelNum > 0) {
                    context.routeAction(ActionType.actionSelectModel) {
                        it.param = BindModelIntent().apply {
                            maxNum = entity.remainModelNum
                            from = 1
                        }
                    }
                }
            }
        }

        //排版入口
        shiftEntranceView.clickWithTrigger {
            viewModel?.readNewSchedule()
            shiftEntranceView.hasChange(false)
            // 跳转排班界面
            context.routeAction(ActionType.actionSchedule)
        }

        //主播绩效中心
//        chatterMpc.clickWithTrigger {
//            context.routeAction(ActionType.actionMPC)
//            getMainPage()?.mpcViewReset(false)
//            AppRepository.eventTrace(EventKey.mpc_entrance_c)
//        }

        //course 新增页面
        parentCourse.clickWithTrigger {
            context.routeAction(ActionType.actionCourseSort)
        }

        //质检
        settingPenalties.clickWithTrigger {
            context.routeAction(ActionType.actionPenalties)
        }

        parentPenalties.clickWithTrigger {
            context.routeAction(ActionType.actionPenalties)
        }

        topCustomService.clickWithTrigger {
            context.routeAction(ActionType.actionCustomerService)
        }

        //客服
        settingCService.clickWithTrigger {
            context.routeAction(ActionType.actionCustomerService)
        }

        //上传付款账号信息
        receivingAccount.clickWithTrigger {
            context.routeAction(ActionType.actionTypeToWebActivity) {
                it.param = WebIntent().also { webIntent ->
                    webIntent.showTitle = true
                    webIntent.url = payrollUrl
                }
            }
        }

        settingFeature.clickWithTrigger {
            context.routeAction(ActionType.actionFeatureGuide)
        }

        settingNotify.setOnClickListener {
            // 上线通知开关页
            context.routeAction(ActionType.actionNotificationSetting)
        }
        //流量设置
        trafficSetting.clickWithTrigger {
            viewModel?.matchPolicyGet { entity ->
                context.routeAction(ActionType.actionTrafficSetting) {
                    it.param = entity
                }
            }
        }
    }

    /**
     * 设置用户信息
     */
    private fun updateUserInfo() {
        val userEntity = UserController.getLocalUser()

        with(userEntity) {
            nickName.text = "Hi $nickname"
            userID.text = buildString {
                append("ID:")
                append(userId)
            }
        }
    }

    /**
     * 邀请好友
     */
    private fun inviteFriends(entity: MineInfoEntity) {
        rlInvite.show(shown = AppController.isShowInviteEntry() == 1)
        settingInvite.text = getString(R.string.invite_friends)
        tvInviteNew.show(entity.invitationUnread == 1)
        //邀请好友
        settingInvite.clickWithTrigger {
            viewModel?.invitationRead()
            tvInviteNew.show(false)
            context.routeAction(ActionType.actionInviteFriends)
//            //已绑定不操作
//            if (entity.invitationUnread == -1) {
//                ToastUtils.showToast("You've bound your invite code")
//                return@clickWithTrigger
//            }
//            if (entity.isNewChatter == 1) { //新主播
//                BindInviteCodeDialog
//                    .newInstance(from = BindInviteCodeDialog.FROM_OTHER)
//                    .show(childFragmentManager, BindInviteCodeDialog::class.java.name)
//            } else { //老主播
//
//            }
        }
    }

    /**
     * 上传账号提示 每天只弹一次
     */
    private fun uploadAccount(payrollStatus: Int) {
        if (ActivityStack.getInstance().hasActivity(BaseTaskActivity::class.java)) {
            return //正在激活流程中，不弹出
        }

        if (payrollStatus == 0 && SPUtils.getString(
                context,
                UserController.attachAccount("IsTipsUpload"),
                ""
            ) != DateUtils.getCurrentDate()
        ) {
            SPUtils.put(context, UserController.attachAccount("IsTipsUpload"), DateUtils.getCurrentDate())
            CommonVerticalAlertDialog.Builder()
                .setTitle(getString(R.string.receiving_title))
                .setContext(getString(R.string.receiving_content))
                .setContextGravity(Gravity.CENTER_HORIZONTAL)
                .setContentCanSelect(false)
                .setCancelable(false)
                .setCancel(getString(com.iandroid.allclass.lib_basecore.R.string.cancel)) {}
                .setConfirm(getString(R.string.receiving_update)) {
                    context.routeAction(ActionType.actionTypeToWebActivity) {
                        it.param = WebIntent().also { webIntent ->
                            webIntent.showTitle = true
                            webIntent.url = payrollUrl
                        }
                    }
                }
                .create()
                .show(childFragmentManager, CommonVerticalAlertDialog::class.java.name)
        }
    }

    fun resetMpcNew(show: Boolean) {
        mpcHasNew.show(show)
    }

    override fun onResume() {
        super.onResume()
        if (getMainPage()?.hasMpcNotice == true) {
            resetMpcNew(true)
        }
        isHaveNoticeOff()
        withdraw()
    }

    override fun onCountChanged(count: Int) {
        getMainPage()?.updateMeRedDot(count > 0 || csTotalUnderCount > 0, fromSysNotify = true)
    }

    private fun getMainPage(): MainActivity? = context.castObject<MainActivity>()

    /**
     * 获取客服总未读数
     */
    private fun getCSTotalUnderCount() {
        val accountList = listOf(
            AppController.getQualityAccount(),
            AppController.getQAAccount(),
            AppController.getSalaryAccount()
        )
        var totalCount = 0
        for (account in accountList) {
            RongCoreClient.getInstance().getUnreadCount(
                Conversation.ConversationType.PRIVATE,
                account,
                object : IRongCoreCallback.ResultCallback<Int>() {
                    override fun onSuccess(integer: Int) {
                        totalCount += integer
                        csTotalUnderCount = totalCount
                        kotlin.runCatching {
                            tvCServiceNum.text = if (totalCount > 99) "99+" else "$totalCount"
                            tvCServiceNum.show(totalCount > 0)
                            getMainPage()?.updateMeRedDot(
                                totalCount > 0 || penaltyUnreadCount > 0,
                                fromSysNotify = true
                            )
                        }
                    }

                    override fun onError(e: IRongCoreEnum.CoreErrorCode) {}
                })
        }
    }

    /**
     * 在线通知开关是否有关闭
     */
    private fun isHaveNoticeOff() {
        val pushConfig = SPUtils.getInt(requireActivity(), SPConstants.KEY_PUSH_CONFIG, 0)
        isOnlineNoticeOff.show((pushConfig and 0x02) == Config.closeOnlinePush || (pushConfig and 0x01) == Config.closeInAppPush)
    }

    /**
     * 新主播流程
     */
    private fun newAnchorFlow(status: Int, totalReward: String) {
        HomeConfig.newChatterStatus = status
        SPUtils.put(context, UserController.attachAccount(SPConstants.KEY_IS_NEW_CHATTER), status == 0)
        if (status == 0) {
            if (!ActivityStack.getInstance().hasActivity(BaseTaskActivity::class.java)) {
                context.routeAction(ActionType.actionNewAnchor)
            }
        }
        if (status == 1) {
            NewAnchorFinishTaskDialog.showNewAnchorFinishTaskDialog(totalReward, isCancel = false) {
                viewModel?.newReadCongrats {
                    SimpleRxBus.post(UIEventTaskRefresh())
                }
            }
        }
    }

    /**
     * 薪资&提现
     */
    private fun withdraw() {
        withdrawViewModel.getPropertyInfo()
        withdrawViewModel.propertyInfoResult?.observe(this) { data ->
            tvCurrentBalance.text = "${data.currency}${SUStringUtils.formatBalance(data.balance)}"
            tvTotalEarned.text = "${data.currency}${SUStringUtils.formatBalance(data.totalSalary)}"
            tvPending.text = "${data.currency}${SUStringUtils.formatBalance(data.withdrawAmount)}"
            tvPaid.text = "${data.currency}${SUStringUtils.formatBalance(data.grantAmount)}"
            btnWithDraw.clickWithTrigger {
                context.routeAction(ActionType.actionWithdraw) { action ->
                    data.payrollUrl = payrollUrl
                    action.param = data
                }
            }
        }

        tvViewAllDetails.clickWithTrigger {
            startActivity(Intent(context, BalanceDetailsActivity::class.java))
        }
    }

    /**
     * 显示添加model的相关UI
     */
    private fun setAddModelView(num: Int) {
        ivAddModel?.show(num > 0)
        val noticeTag = SPUtils.getInt(
            AppContext.context,
            UserController.attachAccount(Values.keyCanAddModelNotice)
        )
        if (num > 0 && noticeTag == 0) {
            SPUtils.put(context, UserController.attachAccount(Values.keyCanAddModelNotice), 1)
            // 显示弹窗
            TopHostDialog(num).show(childFragmentManager, TopHostDialog::javaClass.name)
        }
    }

    override fun fragmentVisible(visible: Boolean) {
        if (visible && ivAddModel?.isVisible == true && HomeConfig.guideConfig?.meAddGuide == false) {
            HomeConfig.guideConfig?.meAddGuide = true
            // 触发add引导页
            getMainPage()?.setGuide(arrayListOf(R.drawable.ic_guide_main_me_add))
            CommonRepository.guideRead(HomeConfig.guideMeAdd)
        }
    }
}