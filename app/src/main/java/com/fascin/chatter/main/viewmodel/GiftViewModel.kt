package com.fascin.chatter.main.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.fascin.chatter.bean.chat.GiftEntity
import com.fascin.chatter.repository.AppRepository
import com.iandroid.allclass.lib_common.base.BaseViewModel
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.ConversationIdentifier

class GiftViewModel : BaseViewModel() {

    private val _giftsLiveData = MutableLiveData<List<GiftEntity>?>()
    val giftsLiveData: LiveData<List<GiftEntity>?> get() = _giftsLiveData

    fun isGiftInitialized(): <PERSON><PERSON>an {
        return !_giftsLiveData.value.isNullOrEmpty()
    }

    fun getGiftList(type: Conversation.ConversationType, id: String, channelID: String) {
        val imId = ConversationIdentifier.obtain(type, id, channelID).targetId

        compositeDisposable?.add(
            AppRepository.getGiftList(imId)
                .subscribe({
                    _giftsLiveData.postValue(it)
                }, {
                    _giftsLiveData.postValue(null)
                })
        )
    }
}