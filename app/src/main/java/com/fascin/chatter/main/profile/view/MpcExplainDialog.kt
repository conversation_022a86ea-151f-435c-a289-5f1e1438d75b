package com.fascin.chatter.main.profile.view

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.appcompat.widget.AppCompatTextView
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.bean.ExplainEntity
import com.iandroid.allclass.lib_basecore.utils.DeviceUtils
import com.iandroid.allclass.lib_common.base.BaseDialogFragment
import com.iandroid.allclass.lib_common.utils.exts.show
import kotlinx.android.synthetic.main.dialog_mpc_explain.explainScrollView
import kotlinx.android.synthetic.main.dialog_mpc_explain.ivExplainClose
import kotlinx.android.synthetic.main.dialog_mpc_explain.mpcExplainLow
import kotlinx.android.synthetic.main.dialog_mpc_explain.mpcExplainMed
import kotlinx.android.synthetic.main.dialog_mpc_explain.mpcExplainTop
import kotlinx.android.synthetic.main.dialog_mpc_explain.rvExplainTips
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


/**
 *  @author: LXL
 *  @description: 主播绩效中心 名词解释
 *  @date: 2024/3/20 10:21
 */
class MpcExplainDialog : BaseDialogFragment {

    constructor() : super()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return inflater.inflate(R.layout.dialog_mpc_explain, container, false)
    }

    override fun onStart() {
        super.onStart()
        val screenHeight = DeviceUtils.getScreenHeight(context).toFloat()
        val height = screenHeight * 0.8
        setBottomPopupAttr(WindowManager.LayoutParams.MATCH_PARENT, height.toInt())
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        AppModule.userActive()
        ivExplainClose.setOnClickListener {
            dismissAllowingStateLoss()
        }
        mpcExplainLow.setUnlockLevel("Low")
        mpcExplainMed.setUnlockLevel("Med")
        mpcExplainTop.setUnlockLevel("Top")

        val dataList = listOf(
            ExplainEntity("New Chats' Unlocked PPV", "Number of ppvs unlocked for users who created a chat today."),
            ExplainEntity(
                "Old Chats' Unlocked PPV",
                "Old chats created before, the number of ppvs the user has unlocked today."
            ),
            ExplainEntity("New Chats", "New start chats today."),
            ExplainEntity(
                "Old Chats",
                "Whether you or a user sends a message to your old conversation today, it will be counted as an active old chat.",
                "Example:\n" +
                        "Model Lisa and user Zack started chatting yesterday or more ago. The user or model has sent at least one message today, which is an active old chat."
            ),
            ExplainEntity(
                "Unlocked New Chats",
                "The number of chats which have unlocked PPV and these chats start today.",
                "Example:\n" +
                        "Model Yimi and user Jimmy started chatting for the first time today, and Zack unlocked at least 1 ppv today, which is called unlocking new chat numbers."
            ),
            ExplainEntity(
                "Unlocked Old Chats", "Unlocked Old Chats", "Example:\n" +
                        "The model and the user started chatting before today, and the user unlocked at least 1 ppv today. This is called the number of new chats unlocked"
            ),
            ExplainEntity(
                "Unlocked PPV per Chat", "1. The average number of ppvs unlocked by new chats today.\n" +
                        "2. The average number of ppvs unlocked today in old chats.", "Example:\n" +
                        "User Zack unlocked model Lisa10 ppv today,\n" +
                        "User Jimmy unlocked model Yimi 4 ppv today,\n" +
                        "So the average number of unlocked ppvs in 2 chats today is 7, Calculation formula = (10+4)/2"
            )
        )
        rvExplainTips.layoutManager = LinearLayoutManager(context)
        rvExplainTips.adapter = ExplainTipsAdapter(dataList)
        lifecycleScope.launch {
            delay(50)
            explainScrollView.scrollTo(0, 0)
        }
    }

}

class ExplainTipsAdapter(private val dataList: List<ExplainEntity>) :
    RecyclerView.Adapter<ExplainTipsAdapter.ViewHolder>() {

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val titleTextView: AppCompatTextView = itemView.findViewById(R.id.tvExplainTipsTitle)
        val contentTextView: AppCompatTextView = itemView.findViewById(R.id.tvExplainTipsContent)
        val tvExplainContentDesc: AppCompatTextView = itemView.findViewById(R.id.tvExplainContentDesc)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.itemview_explain_tips, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = dataList[position]
        holder.titleTextView.text = item.title
        holder.contentTextView.text = item.content
        holder.tvExplainContentDesc.text = item.contentDes
        holder.tvExplainContentDesc.show(item.contentDes.isNotEmpty())
    }

    override fun getItemCount(): Int {
        return dataList.size
    }
}