package com.fascin.chatter.main.profile.invite

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.bean.InviteHistoryEntity
import kotlinx.android.synthetic.main.itemview_invite_success.view.tv_success_id
import kotlinx.android.synthetic.main.itemview_invite_success.view.tv_success_money
import kotlinx.android.synthetic.main.itemview_invite_success.view.tv_success_num
import kotlinx.android.synthetic.main.itemview_invite_success.view.tv_success_time

/**
 *  @author: LXL
 *  @description: 邀请成功历史记录
 *  @date: 2024/9/3 19:56
 */
class InviteHistoryAdapter : RecyclerView.Adapter<InviteHistoryAdapter.ViewHolder>() {

    private val dataList = mutableListOf<InviteHistoryEntity>()

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

    fun updateData(data: List<InviteHistoryEntity>?) {
        dataList.clear()
        if (!data.isNullOrEmpty()) {
            dataList.addAll(data)
        }
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.itemview_invite_history_success, parent, false)
        )

    }

    override fun getItemCount(): Int = dataList.size

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val data = dataList[position]
        holder.itemView.apply {
            this.tv_success_num.text = "${position + 1}. "
            this.tv_success_id.text = "${data.id}, "
            this.tv_success_money.text = "+${data.money}"
            this.tv_success_time.text = data.successDate
        }
    }

}