package com.fascin.chatter.main.profile.guide

import android.os.Bundle
import com.fascin.chatter.R
import com.fascin.chatter.bean.FeatureGuideEntity
import com.fascin.chatter.main.GuideHelp
import com.iandroid.allclass.lib_basecore.base.BaseActivity
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemInfo
import com.iandroid.allclass.lib_basecore.view.recyclerview.RecyclerViewSupport
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import kotlinx.android.synthetic.main.activity_feature_guide.btnDirectory
import kotlinx.android.synthetic.main.activity_feature_guide.drawerLayout
import kotlinx.android.synthetic.main.activity_feature_guide.drawerRv
import kotlinx.android.synthetic.main.activity_feature_guide.guideBack
import kotlinx.android.synthetic.main.activity_feature_guide.guideRv
import kotlinx.android.synthetic.main.activity_feature_guide.rightDrawer

/**
 * 引导页汇总界面
 */
class FeatureGuideActivity : BaseActivity(), DirectoryItemAction {

    private var recyclerViewSupport: RecyclerViewSupport? = null
    private var drawerSupport: RecyclerViewSupport? = null


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_feature_guide)
    }

    override fun initBaseContent() {
        super.initBaseContent()
        setRv()
        setData()
        setListener()
    }

    private fun setRv() {
        recyclerViewSupport = RecyclerViewSupport(supportFragmentManager, guideRv, null)
        recyclerViewSupport?.setCanPullDown(false)
        recyclerViewSupport?.setCanPullUp(false)
        drawerSupport = RecyclerViewSupport(supportFragmentManager, drawerRv, null)
        drawerSupport?.setCanPullDown(false)
        drawerSupport?.setCanPullUp(false)
    }

    private fun setData() {
        val guideHelp = GuideHelp()
        val featureGuideData = guideHelp.getFeatureGuideData()
        recyclerViewSupport?.updateData(
            ArrayList<BaseRvItemInfo?>().also { list ->
                featureGuideData.forEach { feature ->
                    list.add(BaseRvItemInfo(feature, AppViewType.featureGuideItemView, this))
                }
            }, true
        )
        drawerSupport?.updateData(
            ArrayList<BaseRvItemInfo?>().also { list ->
                featureGuideData.forEach { feature ->
                    list.add(BaseRvItemInfo(feature, AppViewType.featureDirectoryItemView, this))
                }
            }, true
        )
    }

    private fun setListener() {
        guideBack.clickWithTrigger {
            finish()
        }
        // 打开和关闭抽屉功能
        btnDirectory.setOnClickListener {
            if (drawerLayout.isDrawerOpen(rightDrawer)) {
                drawerLayout.closeDrawer(rightDrawer)
            } else {
                drawerLayout.openDrawer(rightDrawer)
            }
        }
    }

    override fun onDirectoryItemClick(position: Int) {
        drawerLayout.closeDrawer(rightDrawer)
        recyclerViewSupport?.scrollToPosition(position)
        recyclerViewSupport?.getItemData<FeatureGuideEntity>(position)?.also {
            it.picShow = true
        }
        recyclerViewSupport?.updateViewByPosition(position)
    }

    override fun immersiveIntoStatusBar(): Boolean {
        return true
    }

    override fun isShowTitleBar(): Boolean {
        return false
    }
}