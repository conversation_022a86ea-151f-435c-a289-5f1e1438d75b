package com.fascin.chatter.main.profile.invite

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.main.profile.ProfileViewModel
import com.iandroid.allclass.lib_basecore.utils.DeviceUtils
import com.iandroid.allclass.lib_common.base.BaseDialogFragment
import com.iandroid.allclass.lib_common.event.EventProfileUpdate
import com.iandroid.allclass.lib_common.event.UINewAnchorTaskStatusEvent
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.show
import kotlinx.android.synthetic.main.dialog_bind_invite_code.etBindCode
import kotlinx.android.synthetic.main.dialog_bind_invite_code.rtvCodeCancel
import kotlinx.android.synthetic.main.dialog_bind_invite_code.tvBindCode
import kotlinx.android.synthetic.main.dialog_bind_invite_code.tvBindTitle

/**
 * @Desc: 新主播绑定邀请码弹窗
 * @Created: LXL
 * @Date: 2024/9/3 19:50
 */
class BindInviteCodeDialog : BaseDialogFragment() {

    companion object {
        private const val FROM_KEY = "from_key"

        const val FROM_OTHER = 0
        const val FROM_TASK = 1

        fun newInstance(from: Int = FROM_OTHER): BindInviteCodeDialog = BindInviteCodeDialog().apply {
            arguments = Bundle().apply {
                putInt(FROM_KEY, from)
            }
        }
    }

    private val from by lazy { arguments?.getInt(FROM_KEY) ?: FROM_OTHER }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(
            R.layout.dialog_bind_invite_code,
            container,
            false
        )
    }

    override fun onStart() {
        super.onStart()
        setCenterPopupAttr(
            (DeviceUtils.getScreenWidth(context) * 0.885).toInt(),
            WindowManager.LayoutParams.WRAP_CONTENT
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        isCancelable = false
        AppModule.userActive()
        val viewModel = ViewModelProvider(this).get(ProfileViewModel::class.java)

        tvBindTitle.text = when (from) {
            FROM_TASK -> getString(R.string.invite_you_code_from_task)
            else -> getString(R.string.invite_you_code)
        }

        rtvCodeCancel.text = when(from) {
            FROM_TASK -> getString(R.string.invite_skip)
            else -> getString(com.iandroid.allclass.lib_common.R.string.btn_cancel)
        }

        tvBindCode.isEnabled = false
        //etBindCode.requestFocus()
        etBindCode.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {

            }

            override fun afterTextChanged(s: Editable?) {
                val characterCount = s.toString().length
                tvBindCode.isEnabled = characterCount >= 8
            }
        })

        tvBindCode.clickWithTrigger {
            viewModel.newBindCode(etBindCode.text.toString()) {
                SimpleRxBus.post(EventProfileUpdate())
                SimpleRxBus.post(UINewAnchorTaskStatusEvent())
                dismissAllowingStateLoss()
            }
        }

        rtvCodeCancel.clickWithTrigger {
            if (from == FROM_TASK) {
                viewModel.skipBindCode {
                    SimpleRxBus.post(UINewAnchorTaskStatusEvent())
                    dismissAllowingStateLoss()
                }
            } else {
                dismissAllowingStateLoss()
            }
        }
    }
}