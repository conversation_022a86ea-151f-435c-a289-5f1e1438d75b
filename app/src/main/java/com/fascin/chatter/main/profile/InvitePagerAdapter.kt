package com.fascin.chatter.main.profile

import android.content.Context
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.fascin.chatter.R

enum class InvitePager {
    Activated {
        override fun title(context: Context): String {
            return context.getString(R.string.activated_invitees)
        }
    },
    Qualified {
        override fun title(context: Context): String {
            return context.getString(R.string.qualified_invitees)
        }
    };

    abstract fun title(context: Context): String
}

class InvitePagerAdapter : FragmentStateAdapter {

    private val titles: List<InvitePager>

    constructor(fragmentActivity: FragmentActivity, titles: List<InvitePager>) : super(fragmentActivity) {
        this.titles = titles
    }

    constructor(fragmentManager: FragmentManager, lifecycle: Lifecycle, titles: List<InvitePager>) : super(fragmentManager, lifecycle) {
        this.titles = titles
    }

    constructor(fragment: Fragment, titles: List<InvitePager>) : super(fragment) {
        this.titles = titles
    }

    override fun createFragment(position: Int): Fragment = InviteListDetailFragment.newInstance(titles[position])

    override fun getItemCount(): Int = titles.size
}
