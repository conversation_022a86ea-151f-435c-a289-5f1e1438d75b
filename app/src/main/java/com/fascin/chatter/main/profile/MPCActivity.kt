package com.fascin.chatter.main.profile

import android.graphics.Color
import android.os.Bundle
import android.view.View
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.bean.MpcEntity
import com.fascin.chatter.main.profile.adapter.UnlockPPVAdapter
import com.fascin.chatter.main.profile.view.MpcExplainDialog
import com.fascin.chatter.repository.AppRepository
import com.iandroid.allclass.lib_basecore.utils.SpanUtil
import com.iandroid.allclass.lib_common.base.ChatterBaseActivity
import com.iandroid.allclass.lib_common.event.EventKey
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.immersiveStatusBarWithMargin
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.views.UnlockLevel
import com.iandroid.allclass.lib_common.views.leverColor
import com.iandroid.allclass.lib_common.views.parseUnlockLevel
import io.rong.imkit.widget.FixedLinearLayoutManager
import kotlinx.android.synthetic.main.activity_mpc.mpcBack
import kotlinx.android.synthetic.main.activity_mpc.mpcEmpty
import kotlinx.android.synthetic.main.activity_mpc.mpcPlace
import kotlinx.android.synthetic.main.activity_mpc.mpcScrollView
import kotlinx.android.synthetic.main.activity_mpc.mpcToolbar
import kotlinx.android.synthetic.main.activity_mpc.mpcUpdateTips
import kotlinx.android.synthetic.main.activity_mpc.newChatUnlockLevel
import kotlinx.android.synthetic.main.activity_mpc.oldChatUnlockLevel
import kotlinx.android.synthetic.main.activity_mpc.repossessionView
import kotlinx.android.synthetic.main.activity_mpc.rvNewUnlockPPV
import kotlinx.android.synthetic.main.activity_mpc.rvOldUnlockPPV
import kotlinx.android.synthetic.main.activity_mpc.totalUnlockLevel
import kotlinx.android.synthetic.main.activity_mpc.tvBeatChatter
import kotlinx.android.synthetic.main.activity_mpc.tvMpc3day
import kotlinx.android.synthetic.main.activity_mpc.tvMpc7day
import kotlinx.android.synthetic.main.activity_mpc.tvMpcYesterday
import kotlinx.android.synthetic.main.activity_mpc.tvNewChatPPVCount
import kotlinx.android.synthetic.main.activity_mpc.tvNewChatPPVTips
import kotlinx.android.synthetic.main.activity_mpc.tvOldChatPPVCount
import kotlinx.android.synthetic.main.activity_mpc.tvOldChatPPVTips
import kotlinx.android.synthetic.main.activity_mpc.tvTotalUnlockPPV
import kotlinx.android.synthetic.main.activity_mpc.tvTotalUnlockPPVTips

/**
 *  @author: LXL
 *  @description: 主播绩效中心
 *  @date: 2024/3/18 18:27
 */
class MPCActivity : ChatterBaseActivity() {
    private var unlockNewPPVAdapter: UnlockPPVAdapter? = null
    private var unlockOldPPVAdapter: UnlockPPVAdapter? = null
    private var mpcEntity: MpcEntity? = null
    private var selectDaysTab = YESTERDAY

    companion object {
        const val YESTERDAY = 0       //昨天
        const val THREE_DAYS_AGO = 1  //3天前
        const val SEVEN_DAYS_AGO = 2  //7天前
    }

    private val viewModel by lazy {
        ViewModelProvider(
            this,
            ProfileViewModel.ViewModeFactory()
        )[ProfileViewModel::class.java]
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_mpc)
    }

    override fun initBaseContent() {
        super.initBaseContent()
        AppModule.userActive()
        immersiveStatusBarWithMargin(mpcToolbar)
        mpcBack.setOnClickListener {
            finish()
        }

        selectTabSelect(tvMpcYesterday)

        viewModel.chatMpcData()
        viewModel.chatMpcDataResult?.observe(this) {
            mpcPlace.show(false)
            if (it?.yesterday == null) {
                showEmptyView()
                return@observe
            }
            mpcUpdateTips.show(it.updateTime == 0L)
            mpcEntity = it
            repossessionView.setData(it.mpcRepossession)
            mpcEntity?.yesterday?.totalLevel?.apply {
                tvTotalUnlockPPV.text = "${this.num} "
                tvTotalUnlockPPV.setTextColor(leverColor(this.level))
                setTotalUnlockLevel(this.level, "${this.rank}%")
                totalUnlockLevel.setUnlockLevel(this.level)
            }
            unlockPPVBlock()
        }

        //昨天
        tvMpcYesterday.setOnClickListener {
            updateDayTab(YESTERDAY, it)
            AppRepository.eventTrace(EventKey.mpc_tab_y)
        }
        //3天前
        tvMpc3day.setOnClickListener {
            updateDayTab(THREE_DAYS_AGO, it)
            AppRepository.eventTrace(EventKey.mpc_tab_3)
        }
        //7天前
        tvMpc7day.setOnClickListener {
            updateDayTab(SEVEN_DAYS_AGO, it)
            AppRepository.eventTrace(EventKey.mpc_tab_7)
        }
        tvTotalUnlockPPVTips.clickWithTrigger {
            showExplainDialog()
        }
    }

    /**
     * 解锁PPV点击模块
     */
    private fun unlockPPVBlock() {
        unlockNewPPVAdapter = UnlockPPVAdapter(onImproveMethodClick = { tipId, title ->
            MpcImproveDialog(tipId, getDaysTab(), "new ppv", title).show(
                supportFragmentManager,
                MpcImproveDialog::class.java.name
            )
            AppRepository.eventTrace(EventKey.mpc_im_c) {
                "tab" to getDaysTab()
                "block" to "new ppv"
                "itemTitle" to title
            }
        }, unlockItemTipsClick = {
            showExplainDialog()
        })
        rvNewUnlockPPV.isNestedScrollingEnabled = false
        rvNewUnlockPPV.layoutManager = FixedLinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
        rvNewUnlockPPV.adapter = unlockNewPPVAdapter

        unlockOldPPVAdapter = UnlockPPVAdapter(onImproveMethodClick = { tipId, title ->
            MpcImproveDialog(tipId, getDaysTab(), "old ppv", title).show(
                supportFragmentManager,
                MpcImproveDialog::class.java.name
            )
            AppRepository.eventTrace(EventKey.mpc_im_c) {
                "tab" to getDaysTab()
                "block" to "old ppv"
                "itemTitle" to title
            }
        }, unlockItemTipsClick = {
            showExplainDialog()
        })
        rvOldUnlockPPV.isNestedScrollingEnabled = false
        rvOldUnlockPPV.layoutManager = FixedLinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
        rvOldUnlockPPV.adapter = unlockOldPPVAdapter

        updateUnlockSelectedTab()

        tvNewChatPPVTips.clickWithTrigger {
            showExplainDialog()

        }
        tvOldChatPPVTips.clickWithTrigger {
            showExplainDialog()
        }
    }

    /**
     * 更新Days选中的Tab
     */
    private fun updateDayTab(day: Int, view: View) {
        selectDaysTab = day
        selectTabSelect(view)
        val totalLevel = when (day) {
            YESTERDAY -> mpcEntity?.yesterday?.totalLevel
            THREE_DAYS_AGO -> mpcEntity?.threeDays?.totalLevel
            SEVEN_DAYS_AGO -> mpcEntity?.sevenDays?.totalLevel
            else -> null
        }
        totalLevel?.apply {
            tvTotalUnlockPPV.text = "${this.num} "
            tvTotalUnlockPPV.setTextColor(leverColor(this.level))
            setTotalUnlockLevel(this.level, "${this.rank}%")
            totalUnlockLevel.setUnlockLevel(this.level)
        }
        unlockPPVBlock()
    }

    /**
     * 选中Unlock Tab更新对应数据
     */
    private fun updateUnlockSelectedTab() {
        val chatData = when (selectDaysTab) {
            YESTERDAY -> mpcEntity?.yesterday
            THREE_DAYS_AGO -> mpcEntity?.threeDays
            SEVEN_DAYS_AGO -> mpcEntity?.sevenDays
            else -> null
        }
        chatData?.let {
            tvNewChatPPVCount.text = it.newChat.num
            tvNewChatPPVCount.setTextColor(leverColor(it.newChat.level))
            newChatUnlockLevel.setUnlockLevel(it.newChat.level)

            tvOldChatPPVCount.text = it.oldChat.num
            tvOldChatPPVCount.setTextColor(leverColor(it.oldChat.level))
            oldChatUnlockLevel.setUnlockLevel(it.oldChat.level)

            unlockNewPPVAdapter?.updateData(it.newChat.list)
            unlockOldPPVAdapter?.updateData(it.oldChat.list)

        }
    }


    /**
     * 总解释PPV 低 中 高 超过百分比
     */
    private fun setTotalUnlockLevel(unlockLevelString: String, percentage: String) {
        when (parseUnlockLevel(unlockLevelString)) {
            UnlockLevel.LOW -> {
                SpanUtil.create()
                    .addForeColorSection("You are at ", Color.parseColor("#000000"))
                    .addForeColorSection(percentage, Color.parseColor("#F74E57"))
                    .addForeColorSection(", improve yourself and", Color.parseColor("#000000"))
                    .addForeColorSection("\nbecome a better chatter.", Color.parseColor("#000000"))
                    .showIn(tvBeatChatter)
            }

            UnlockLevel.MED -> {
                SpanUtil.create()
                    .addForeColorSection("You surpass ", Color.parseColor("#000000"))
                    .addForeColorSection(percentage, Color.parseColor("#FDA946"))
                    .addForeColorSection(" of chatters,", Color.parseColor("#000000"))
                    .addForeColorSection("\nstrive to become the top chatter.", Color.parseColor("#000000"))
                    .showIn(tvBeatChatter)
            }

            UnlockLevel.TOP -> {
                SpanUtil.create()
                    .addForeColorSection("Great, you have defeated ", Color.parseColor("#000000"))
                    .addForeColorSection(percentage, Color.parseColor("#24C004"))
                    .addForeColorSection(" of chatters,", Color.parseColor("#000000"))
                    .addForeColorSection("\nkeep up the good work.", Color.parseColor("#000000"))
                    .showIn(tvBeatChatter)
            }
        }

    }


    /**
     * 顶部3个tab切换UI修改
     */
    private fun selectTabSelect(clickedView: View) {
        AppModule.userActive()
        tvMpcYesterday.isSelected = clickedView == tvMpcYesterday
        tvMpc3day.isSelected = clickedView == tvMpc3day
        tvMpc7day.isSelected = clickedView == tvMpc7day
        when (selectDaysTab) {
            YESTERDAY -> {
                tvTotalUnlockPPVTips.text = getString(R.string.mpc_yesterday_ppv)
            }

            THREE_DAYS_AGO -> {
                tvTotalUnlockPPVTips.text = getString(R.string.mpc_3_day_ppv)
            }

            SEVEN_DAYS_AGO -> {
                tvTotalUnlockPPVTips.text = getString(R.string.mpc_7_day_ppv)
            }
        }
    }

    /**
     * 空视图
     */
    private fun showEmptyView() {
        mpcEmpty.show(true)
        mpcUpdateTips.show(false)
        mpcScrollView.show(false)
    }

    /**
     * 名词解释说明弹窗
     */
    private fun showExplainDialog() {
        MpcExplainDialog().show(supportFragmentManager, MpcExplainDialog::class.java.name)
    }

    fun getDaysTab(): String {
        return when (selectDaysTab) {
            0 -> return "yesterday"
            1 -> return "3 day"
            2 -> return "7 day"
            else -> return "yesterday"
        }
    }

    override fun immersiveIntoStatusBar(): Boolean {
        return true
    }

    override fun isShowTitleBar(): Boolean {
        return false
    }
}