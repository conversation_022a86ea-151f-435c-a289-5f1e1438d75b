package com.fascin.chatter.main.profile.view

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.fascin.chatter.R
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RvItem
import com.iandroid.allclass.lib_common.AppViewType
import kotlinx.android.synthetic.main.itemview_block_matched_place.view.ShimmerLayout

/**
 * @Created: QuanZH
 */
@RvItem(id = AppViewType.balancePlaceItemView, spanCount = 1)
class BalancePlaceItemView(context: Context, fm: FragmentManager, parent: ViewGroup) :
    BaseRvItemView(context, fm, parent) {

    override fun setView() {
        itemView?.run {
            ShimmerLayout.startShimmerAnimation()
        }
    }

    override fun attachLayoutId(): Int {
        return R.layout.itemview_balance_place
    }

    override fun initView(context: Context?, view: View?) {

    }
}