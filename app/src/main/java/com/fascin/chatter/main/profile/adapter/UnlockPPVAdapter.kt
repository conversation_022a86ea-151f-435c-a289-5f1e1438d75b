package com.fascin.chatter.main.profile.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.bean.ChatUnlockItemEntity
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.views.leverBgColor
import com.iandroid.allclass.lib_common.views.leverColor
import kotlinx.android.synthetic.main.itemview_unlock_ppv.view.rcUnlockPpv
import kotlinx.android.synthetic.main.itemview_unlock_ppv.view.tvImproveMethod
import kotlinx.android.synthetic.main.itemview_unlock_ppv.view.tvUnlockItemTips
import kotlinx.android.synthetic.main.itemview_unlock_ppv.view.unlockItemCount
import kotlinx.android.synthetic.main.itemview_unlock_ppv.view.unlockItemLevel
import kotlinx.android.synthetic.main.itemview_unlock_ppv.view.unlockItemTips

/**
 *  @author: LXL
 *  @description: MPC 解锁PPV Adapter
 *  @date: 2024/3/19 16:34
 */
class UnlockPPVAdapter(
    private val onImproveMethodClick: (tipId: String, title: String) -> Unit,
    private val unlockItemTipsClick: () -> Unit
) :
    RecyclerView.Adapter<UnlockPPVAdapter.ViewHolder>() {

    private val dataList = mutableListOf<ChatUnlockItemEntity>()

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

    fun updateData(data: List<ChatUnlockItemEntity>) {
        dataList.clear()
        if (!data.isNullOrEmpty()) {
            dataList.addAll(data)
        }
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.itemview_unlock_ppv, parent, false)
        )

    }

    override fun getItemCount(): Int = dataList.size

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = dataList[position]
        holder.itemView.unlockItemCount.text = item.num
        holder.itemView.unlockItemCount.setTextColor(leverColor(item.level))
        holder.itemView.tvUnlockItemTips.text = item.title
        holder.itemView.unlockItemLevel.setUnlockLevel(item.level)
        holder.itemView.tvImproveMethod.clickWithTrigger {
            onImproveMethodClick(item.tipId, item.title)
        }
        holder.itemView.rcUnlockPpv.setBackgroundColor(leverBgColor(item.level))
        holder.itemView.unlockItemTips.clickWithTrigger {
            unlockItemTipsClick()
        }
    }
}