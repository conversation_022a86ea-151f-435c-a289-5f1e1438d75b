package com.fascin.chatter.main.profile.withdraw

import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.R
import com.fascin.chatter.bean.EmptyEntity
import com.fascin.chatter.config.TabConfig
import com.fascin.chatter.main.IRvItemAction
import com.iandroid.allclass.lib_basecore.base.BaseUiFragment
import com.iandroid.allclass.lib_basecore.utils.DateUtils
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemInfo
import com.iandroid.allclass.lib_basecore.view.recyclerview.PullBaseView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RecyclerViewSupport
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.beans.ProfitListBean
import com.iandroid.allclass.lib_common.beans.WithdrawListBean
import kotlinx.android.synthetic.main.fragment_balance.balanceRv

/**
 * @Desc: 全部余额记录列表
 * @Created: Quan
 * @Date: 2024/10/12
 */
class BalanceFragment : BaseUiFragment(), IRvItemAction {

    private var recyclerViewSupport: RecyclerViewSupport? = null

    private var viewModel: WithdrawViewModel? = null

    private var tabId = TabConfig.balanceIncome

    private var page = 1

    private var startTime = 0L

    override fun attachLayoutId(): Int {
        return R.layout.fragment_balance
    }

    override fun initView(view: View?) {
        super.initView(view)
        initRv()
        setPerView()
        initViewModel()
    }

    private fun initRv() {
        recyclerViewSupport = RecyclerViewSupport(childFragmentManager, balanceRv, null).also {
            it.setCanPullDown(true)
            it.setCanPullUp(true)
            it.recyclerView.setOnRefreshListener(object : PullBaseView.OnRefreshListener {
                override fun onHeaderRefresh() {
                    page = 1
                    it.setCanPullUp(true)
                    fetchPageData(true)
                }

                override fun onFooterRefresh() {
                    fetchPageData(false)
                }
            })
        }
    }

    private fun initViewModel() {
        viewModel = ViewModelProvider(this).get(WithdrawViewModel::class.java)

        viewModel?.withdrawListResult?.observe(this) {
            if (it == null || it.list.isNullOrEmpty()) {
                recyclerViewSupport?.setCanPullUp(false)
                if (page > 1) recyclerViewSupport?.setNoMoreData(true)
                else setWithdrawData(listOf(), true)
            } else {
                setWithdrawData(it.list!!, page == 1)
                page++
            }
            fetchComplete(true)
        }

        viewModel?.withdrawListError?.observe(this) {
            fetchComplete(false)
        }

        viewModel?.profitListResult?.observe(this) {
            if (it == null || it.list.isNullOrEmpty()) {
                recyclerViewSupport?.setCanPullUp(false)
                if (page > 1) recyclerViewSupport?.setNoMoreData(true)
                else setBalanceData(listOf(), true)
            } else {
                setBalanceData(it.list!!, page == 1)
                page++
            }
            fetchComplete(true)
        }

        viewModel?.profitListError?.observe(this) {
            fetchComplete(false)
        }
    }

    override fun fetchPageData(refresh: Boolean) {
        if (refresh) page = 1
        val endTime = DateUtils.getLastDayOfMonthFromTimestamp(startTime.times(1000)) / 1000
        if (tabId == TabConfig.balanceWithdraw)
            viewModel?.withdrawList(startTime, endTime, page)
        else {
            val type = when(tabId) {
                TabConfig.balanceIncomeAll -> "in"
                TabConfig.balanceIncomeNoGift -> "in-gift"
                else -> "out"
            }
            viewModel?.profitList(type, startTime, endTime, page)
        }
    }

    private fun setPerView() {
        updateData(
            ArrayList<BaseRvItemInfo>().apply {
                repeat(6) {
                    add(BaseRvItemInfo(Any(), AppViewType.balancePlaceItemView))
                }
            }, true
        )
    }

    private fun updateData(dataList: ArrayList<BaseRvItemInfo>, clearData: Boolean = true) {
        recyclerViewSupport?.updateData(dataList, clearData)
    }

    private fun setBalanceData(list: List<ProfitListBean>, refresh: Boolean) {
        updateData(
            ArrayList<BaseRvItemInfo>().also {
                for (bean in list) {
                    it.add(BaseRvItemInfo(bean, AppViewType.balanceItemView, this))
                }
            }, refresh
        )
    }

    private fun setWithdrawData(list: List<WithdrawListBean>, refresh: Boolean) {
        updateData(
            ArrayList<BaseRvItemInfo>().also {
                for (bean in list) {
                    it.add(BaseRvItemInfo(bean, AppViewType.withdrawItemView, this))
                }
            }, refresh
        )
    }

    private fun fetchComplete(success: Boolean) {
        recyclerViewSupport?.onHeaderRefreshComplete()
        recyclerViewSupport?.onFooterRefreshComplete()
        if (recyclerViewSupport?.hasData() == false) {
            //没有数据
            if (success) addEmptyView()
            else addErrorView()
        }
    }

    private fun addErrorView() {
        updateData(
            ArrayList<BaseRvItemInfo>().also {
                it.add(BaseRvItemInfo(Any(), AppViewType.exceptionView, this))
            }, true
        )
    }

    private fun addEmptyView() {
        updateData(
            ArrayList<BaseRvItemInfo>().also { list ->
                list.add(BaseRvItemInfo(EmptyEntity().also {
                    it.icRes = R.drawable.ic_balance_empty
                    it.title = "No Data"
                }, AppViewType.comEmptyView, this))
            }, true
        )
    }

    fun setQueryTime(time: Long, isUpdate: Boolean = false) {
        // 转成秒
        startTime = time / 1000
        if (isUpdate) fetchPageData(true)
    }

    fun setTabId(tabId: Int) {
        this.tabId = tabId
    }

    override fun onRefresh() {
        fetchPageData(true)
    }

}