package com.fascin.chatter.main.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.GlideLoader.loadImageCircleCrop
import com.iandroid.allclass.lib_common.beans.UserEntity
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import kotlinx.android.synthetic.main.dialog_flash_chat_vie.view.nickName
import kotlinx.android.synthetic.main.itemview_flash_chat_user.view.clModeRoot
import kotlinx.android.synthetic.main.itemview_flash_chat_user.view.ivSelect
import kotlinx.android.synthetic.main.itemview_flash_chat_user.view.tvAge
import kotlinx.android.synthetic.main.itemview_flash_chat_user.view.userHead

/**
 * @Desc: flash chat 弹窗model列表
 * @Created: Quan
 * @Date: 2023/9/18
 */
class FlashChatModelAdapter :
    RecyclerView.Adapter<FlashChatModelAdapter.ViewHolder>() {

    private val dataList = mutableListOf<UserEntity>()
    var selectId: String = ""// 选中的model id
    private var selectHolder: ViewHolder? = null// 选中的model item

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

    fun updateData(data: List<UserEntity>?) {
        dataList.clear()
        if (!data.isNullOrEmpty()) {
            dataList.addAll(data)
        }
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutInflater.from(parent.context).inflate(
                R.layout.itemview_flash_chat_user,
                parent,
                false
            )
        )
    }

    override fun getItemCount(): Int {
        return dataList.size
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = dataList[position]
        holder.itemView.run {
            userHead.loadImageCircleCrop(AppContext.context, item.avatarUrl)
            nickName.text = item.nickname
            if (item.age > 0) tvAge.text = ",${item.age}"
            // 默认选中第一个
            if (selectId.isEmpty()) {
                selectId = item.userId
                selectHolder = holder
            }
            ivSelect.setImageResource(
                if (selectId == item.userId) R.mipmap.ic_model_selected
                else R.mipmap.ic_model_unselect
            )
            clModeRoot.isSelected = selectId == item.userId
            clickWithTrigger {
                if (selectId != item.userId) {
                    selectId = item.userId
                    holder.itemView.ivSelect.setImageResource(R.mipmap.ic_model_selected)
                    selectHolder?.itemView?.ivSelect?.setImageResource(R.mipmap.ic_model_unselect)

                    holder.itemView.clModeRoot.isSelected = true
                    selectHolder?.itemView?.clModeRoot?.isSelected = false
                    selectHolder = holder
                }
            }
        }
    }
}