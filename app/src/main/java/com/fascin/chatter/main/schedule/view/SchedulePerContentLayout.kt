package com.fascin.chatter.main.schedule.view

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import androidx.recyclerview.widget.LinearLayoutManager
import com.fascin.chatter.R
import com.fascin.chatter.bean.ScheduleDateItemEntity
import com.fascin.chatter.bean.SchedulePerContentEntity
import com.fascin.chatter.bean.SchedulePerItemEntity
import com.fascin.chatter.bean.SchedulePerTabItemEntity
import com.fascin.chatter.main.schedule.ScheduleConst
import com.fascin.chatter.main.schedule.ScheduleDate
import com.fascin.chatter.main.schedule.adapter.LeftTimeAdapter
import kotlinx.android.synthetic.main.layout_schedule_per_content.view.friView
import kotlinx.android.synthetic.main.layout_schedule_per_content.view.monView
import kotlinx.android.synthetic.main.layout_schedule_per_content.view.rvLeftDate
import kotlinx.android.synthetic.main.layout_schedule_per_content.view.rvSelfDate
import kotlinx.android.synthetic.main.layout_schedule_per_content.view.satView
import kotlinx.android.synthetic.main.layout_schedule_per_content.view.sunView
import kotlinx.android.synthetic.main.layout_schedule_per_content.view.thuView
import kotlinx.android.synthetic.main.layout_schedule_per_content.view.tueView
import kotlinx.android.synthetic.main.layout_schedule_per_content.view.wedView

/**
 * @Desc: 排班预览页内容容器
 * @Created: Quan
 * @Date: 2024/11/15
 */
class SchedulePerContentLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private var tabEntity: SchedulePerTabItemEntity? = null
    private var weekDays: List<String> = ArrayList()
    private var contentViews = mutableListOf<ScheduleDayContentView>()

    init {
        View.inflate(context, R.layout.layout_schedule_per_content, this)
        contentViews = mutableListOf(sunView, monView, tueView, wedView, thuView, friView, satView)
    }

    fun setViewData(data: SchedulePerContentEntity?, tabEntity: SchedulePerTabItemEntity?) {
        this.tabEntity = tabEntity
        weekDays = ScheduleDate.getWeekDates(tabEntity?.startDay.orEmpty())
        data?.also { entity ->
            setLeftTimeView(entity.shiftDates)
            setDayContent(entity)
        }
    }

    /**
     * 设置表格排班数据
     */
    private fun setDayContent(entity: SchedulePerContentEntity) {
        entity.shiftList?.let { list ->
            ScheduleConst.weekDayMap.forEach { mapEntity ->
                val dayContents = getDayContents(list, mapEntity.key)
                val isToday =
                    entity.currentDay.isNotEmpty() && entity.currentDay == weekDays[mapEntity.key - 1]
                val dayDate = ScheduleDate.formatToMonthDay(weekDays[mapEntity.key - 1])
                // 设置每日数据
                contentViews[mapEntity.key - 1].setViewData(
                    dayContents,
                    mapEntity.key,
                    dayDate,
                    entity.shiftDates.size,
                    isToday
                )
            }
        }
    }

    /**
     * 设置左侧时间列表
     */
    private fun setLeftTimeView(shiftDates: List<ScheduleDateItemEntity>) {
        val selfDates = ArrayList<ScheduleDateItemEntity>().apply {
            shiftDates.forEach { date ->
                val newDate =
                    ScheduleDate.convertTimeRangeToDeviceTimeZone12Hour(date.shiftDate, "UTC-5")
                add(ScheduleDateItemEntity().apply {
                    shiftId = date.shiftId
                    shiftDate = newDate
                    isHot = date.isHot
                })
            }
        }
        val selfTimeAdapter = LeftTimeAdapter()
        rvSelfDate.layoutManager = LinearLayoutManager(context)
        rvSelfDate.adapter = selfTimeAdapter
        selfTimeAdapter.updateData(selfDates)

        val leftTimeAdapter = LeftTimeAdapter()
        rvLeftDate.layoutManager = LinearLayoutManager(context)
        rvLeftDate.adapter = leftTimeAdapter
        leftTimeAdapter.updateData(shiftDates)
    }

    /**
     * 获取某一天的班次列表
     */
    private fun getDayContents(
        shiftList: List<SchedulePerItemEntity>,
        weekDay: Int
    ): List<SchedulePerItemEntity> {
        return shiftList.filter { it.weekDay == weekDay }.orEmpty().sortedBy { it.shiftId }
    }

}