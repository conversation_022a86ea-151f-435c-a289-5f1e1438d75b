package com.fascin.chatter.main.profile.view

import android.annotation.SuppressLint
import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.bean.ModelUserEntity
import com.fascin.chatter.component.views.SUButtonStatus
import com.fascin.chatter.main.profile.ProfileViewModel
import com.fascin.chatter.main.profile.adapter.ProfileFlashChatModelAdapter
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.base.BaseDialogFragment
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.disableCopy
import com.iandroid.allclass.lib_common.utils.exts.toPx
import com.iandroid.allclass.lib_common.utils.keyboard.KeyboardUtils
import io.rong.imkit.utils.RouteUtils
import io.rong.imlib.model.Conversation
import kotlinx.android.synthetic.main.dialog_profile_flash_chat.editCount
import kotlinx.android.synthetic.main.dialog_profile_flash_chat.editInput
import kotlinx.android.synthetic.main.dialog_profile_flash_chat.llInput
import kotlinx.android.synthetic.main.dialog_profile_flash_chat.llRoot
import kotlinx.android.synthetic.main.dialog_profile_flash_chat.modelRv
import kotlinx.android.synthetic.main.dialog_profile_flash_chat.msgSend
import kotlinx.android.synthetic.main.dialog_profile_flash_chat.scrollView

/**
 * @Desc: user个人主页弹出的flash chat
 * @Created: Quan
 * @Date: 2023/9/19
 */
class ProfileFlashChatDialog : BaseDialogFragment {

    private var userID: String? = ""
    private var models: List<ModelUserEntity>? = null
    private var rootViewInitHeight = 0
    private var viewModel: ProfileViewModel? = null
    private var selectModelId: String = ""

    constructor() : super()
    constructor(userID: String?, models: List<ModelUserEntity>?) : super() {
        this.userID = userID
        this.models = models
    }

    private var adapter: ProfileFlashChatModelAdapter? = null
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(
            R.layout.dialog_profile_flash_chat,
            container,
            false
        )
    }

    override fun onStart() {
        super.onStart()
        setBottomPopupAttr(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.WRAP_CONTENT
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
        viewModel = ViewModelProvider(this).get(ProfileViewModel::class.java)
        adapter = ProfileFlashChatModelAdapter(context)
        modelRv.layoutManager = GridLayoutManager(context, 3)
        modelRv.adapter = adapter
        adapter?.updateData(models)
        setEditView()
        setListener()
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun setEditView() {
        msgSend.setText(getString(com.iandroid.allclass.lib_basecore.R.string.send))
        // 禁用edittext复制粘贴
        editInput.disableCopy()
        setEditUI()
        setContinueStatus()
        // 监听edit内容变化
        editInput.addTextChangedListener {
            setEditUI()
            setContinueStatus()
        }

        editInput.setOnTouchListener { view, event -> // 当触摸的是EditText & 当前EditText可滚动时，则将事件交给EditText处理
            if (view?.id == editInput.id && canVerticalScroll()) {
                view.parent.requestDisallowInterceptTouchEvent(true)
                if (event?.action == MotionEvent.ACTION_UP)
                    view.parent.requestDisallowInterceptTouchEvent(false)
            }
            false
        }
    }

    private fun canVerticalScroll(): Boolean {
        if (editInput.lineCount > editInput.maxLines)
            return true
        return false
    }

    private fun setEditUI() {
        editInput.text?.trim().toString().length.let {
            // 初始化字数标识
            editCount.text = String.format(
                getString(R.string.message_setting_edit_count), it
            )
            llInput.setBackgroundResource(
                if (it == 0) com.iandroid.allclass.lib_common.R.drawable.bg_stroke_gray_r12
                else R.drawable.bg_stroke_yellow_r12
            )
        }
    }

    /**
     * 设置按钮状态
     */
    private fun setContinueStatus() {
        if (msgSend.getButtonStatus() != SUButtonStatus.Loading) {
            if (editInput.text?.trim().toString().isNotEmpty()) msgSend.setButtonStatus(
                SUButtonStatus.Activated
            )
            else msgSend.setButtonStatus(
                SUButtonStatus.Disabled,
                disabledBg = R.drawable.bg_d9d9d9_r16,
                disabledColor = "#ffffff"
            )
        }
    }

    private fun setListener() {
        // 发送flash chat msg，收起输入法
        msgSend.clickWithTrigger {
            KeyboardUtils.hideKeyboard(editInput)
            // 发送flash chat
            selectModelId = adapter?.selectId!!
            val msg = editInput?.text?.trim().toString()
            if (selectModelId.isNotEmpty() && userID?.isNotEmpty() == true && msg.isNotEmpty()) {
                msgSend.setButtonStatus(SUButtonStatus.Loading)
                viewModel?.sendFlashChatMsg(
                    userID,
                    selectModelId,
                    msg,
                    from = 15
                )
            }
        }

        llRoot.setDispatchTouchEventListener {
            if (it.action == MotionEvent.ACTION_DOWN)
                AppModule.userActive()
        }

        llRoot.postDelayed(
            {
                try {
                    // 记录dialog的原有高度
                    rootViewInitHeight = llRoot.rootView.height
                    llRoot.viewTreeObserver.addOnGlobalLayoutListener {
                        // 判断是否发生了遮挡 & 是否是输入框的焦点，是，滑到底部
                        if (rootViewInitHeight - llRoot.rootView.height > 24.toPx && editInput.hasFocus()) {
                            scrollView.smoothScrollTo(0, rootViewInitHeight)
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }, 250
        )

        viewModel?.flashChatMsgResult?.observe(this) {
            if (it && selectModelId.isNotEmpty() && userID?.isNotEmpty() == true) {
                // 关闭个人主页
                AppContext.getTopActivity()?.finish()
                // flash chat成功，跳转聊天页
                RouteUtils.routeToConversationActivity(
                    context, Conversation.ConversationType.PRIVATE, selectModelId + "_" + userID
                )
            }
            dismissAllowingStateLoss()
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        KeyboardUtils.hideKeyboard(editInput)
        super.onDismiss(dialog)
    }
}