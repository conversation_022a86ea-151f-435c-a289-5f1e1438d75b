package com.fascin.chatter.main.course

import android.app.AlertDialog
import android.app.Dialog
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.bean.QuestionEntity
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RvItem
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.GlideLoader.loadImage
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.castObject
import com.iandroid.allclass.lib_common.utils.exts.show
import kotlinx.android.synthetic.main.itemview_question.view.answerRv
import kotlinx.android.synthetic.main.itemview_question.view.ivPic
import kotlinx.android.synthetic.main.itemview_question.view.tvTitle
import kotlinx.android.synthetic.main.itemview_question.view.tvTitleNum

/**
 * Question Item
 * @Created: QuanZH
 * @Date: 2023/9/20
 */
@RvItem(id = AppViewType.questionItemView, spanCount = 1)
class QuestionItemView(context: Context, fm: FragmentManager, parent: ViewGroup) :
    BaseRvItemView(context, fm, parent) {

    private var adapter: AnswerAdapter? = null
    override fun attachLayoutId(): Int {
        return R.layout.itemview_question
    }

    override fun initView(context: Context?, view: View?) {
        itemView.answerRv.layoutManager = LinearLayoutManager(context)
        adapter = AnswerAdapter()
        itemView.answerRv.adapter = adapter
        adapter?.setOnSelectChangeCallback(object : AnswerAdapter.OnSelectChangeCallback {
            override fun onSelected(selects: List<Int>) {
                AppModule.userActive()
                val itemData = getItemData()
                itemData?.let {
                    getAction()?.onChange(it.id, selects)
                }
            }
        })
    }

    override fun setView() {
        val entity = getItemData() ?: return
        itemView?.run {
            // 题号
            tvTitleNum.text = buildString {
                append(position + 1)
                append(". ")
            }
            tvTitle.text = entity.title
            ivPic.show(entity.picUrl.isNotEmpty())
            if (entity.picUrl.isNotEmpty()) {
                ivPic.loadImage(
                    AppContext.context,
                    entity.picUrl,
                    isCenterCrop = false
                )
                ivPic.clickWithTrigger {
                    // 查看大图
                    showImageInDialog(context, ivPic)
                }
            }
            // 设置题目类型
            adapter?.setType(entity.isMultiple)
            adapter?.updateData(entity.answers)
        }
    }

    override fun needClear() {
        adapter?.initSelected()
    }

    private fun showImageInDialog(context: Context, imageView: ImageView) {
        // 创建 Dialog
        val dialog = Dialog(context,android.R.style.Theme_Black_NoTitleBar_Fullscreen)
        val dialogView =
            LayoutInflater.from(context).inflate(R.layout.dialog_image_fullscreen, null)

        // 找到 ImageView 并加载图片
        val dialogImageView = dialogView.findViewById<ImageView>(R.id.dialogImageView)
        dialogImageView.setImageDrawable(imageView.drawable) // 复制当前图片

        // 设置点击关闭
        dialogImageView.setOnClickListener {
            dialog.dismiss()
        }

        dialog.setContentView(dialogView)
        dialog.show()
    }

    private fun getItemData(): QuestionEntity? = data?.castObject<QuestionEntity>()

    private fun getAction(): AnswerChangeAction? {
        return info?.callBack?.castObject<AnswerChangeAction>()
    }
}