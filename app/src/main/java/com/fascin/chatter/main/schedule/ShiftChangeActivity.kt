package com.fascin.chatter.main.schedule

import android.os.Bundle
import android.util.Log
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import com.fascin.chatter.R
import com.fascin.chatter.bean.SchedulePerTabEntity
import com.fascin.chatter.bean.event.UIRefreshScheduleEvent
import com.fascin.chatter.component.views.SUButtonStatus
import com.fascin.chatter.main.schedule.adapter.ShiftChangeAdapter
import com.iandroid.allclass.lib_common.base.ChatterBaseActivity
import com.iandroid.allclass.lib_common.beans.ComContentEntity
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.ToastUtils
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.textSpanValue
import com.iandroid.allclass.lib_common.views.ComWheelDialog
import kotlinx.android.synthetic.main.activity_shift_change.btnSubmit
import kotlinx.android.synthetic.main.activity_shift_change.rvNewShift
import kotlinx.android.synthetic.main.activity_shift_change.rvOldShift
import kotlinx.android.synthetic.main.activity_shift_change.selectNewView
import kotlinx.android.synthetic.main.activity_shift_change.selectOldView
import kotlinx.android.synthetic.main.activity_shift_change.startView
import kotlinx.android.synthetic.main.activity_shift_change.tvChangeNum
import kotlinx.android.synthetic.main.activity_shift_change.tvNewEmpty
import kotlinx.android.synthetic.main.activity_shift_change.tvOldEmpty
import kotlinx.android.synthetic.main.activity_shift_change.tvRules
import kotlinx.android.synthetic.main.activity_shift_change.tvRulesContent
import kotlinx.android.synthetic.main.activity_shift_change.typeView

/**
 * @Desc: 换班页
 * @Created: Quan
 * @Date: 2024/11/20
 */
class ShiftChangeActivity : ChatterBaseActivity() {

    private var viewModel: ScheduleModel? = null
    private var oldAdapter: ShiftChangeAdapter? = null
    private var newAdapter: ShiftChangeAdapter? = null
    private var oldSelectShiftId = -1
    private var newSelectShiftId = -1
    private var weekInfo: SchedulePerTabEntity? = null
    private var typeId: Int = -1 // 换班类型 1 临时 2 永久
    private var startWeekId = -1 // 开始周
    private var selectWeekDay = "" // 选中的日期 yyyy-MM-dd

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_shift_change)
    }

    override fun initBaseContent() {
        super.initBaseContent()
        setTitle(R.string.text_shift_change)
        setView()
        setRv()
        setModel()
        setListener()
    }

    private fun setView() {
        val typeText = textSpanValue(
            this,
            getString(R.string.text_shift_change_type),
            "*",
            R.color.cr_ed2b2b
        )
        val startText = textSpanValue(
            this,
            getString(R.string.text_shift_start_week),
            "*",
            R.color.cr_ed2b2b
        )
        val oldText = textSpanValue(
            this,
            getString(R.string.text_select_old_Shift),
            "*",
            R.color.cr_ed2b2b
        )
        val newText = textSpanValue(
            this,
            getString(R.string.text_select_new_Shift),
            "*",
            R.color.cr_ed2b2b
        )
        startView.isEnabled = false
        typeView.setTitle(typeText)
        startView.setTitle(startText)
        selectOldView.setTitle(oldText)
        selectNewView.setTitle(newText)
        selectOldView.setHintContent(getString(R.string.hint_select_old_Shift))
        selectNewView.setHintContent(getString(R.string.hint_select_new_Shift))
    }

    private fun setRv() {
        oldAdapter = ShiftChangeAdapter()
        rvOldShift.layoutManager = GridLayoutManager(this, 4)
        rvOldShift.adapter = oldAdapter
        newAdapter = ShiftChangeAdapter()
        rvNewShift.layoutManager = GridLayoutManager(this, 4)
        rvNewShift.adapter = newAdapter
    }

    private fun setModel() {
        viewModel = ViewModelProvider(this).get(ScheduleModel::class.java)
        viewModel?.getSchedulePreTab()
        viewModel?.scheduleTabResult?.observe(this) {
            weekInfo = it
            startView.isEnabled = true
            selectOldView.isEnabled = true
        }
        viewModel?.scheduleTabError?.observe(this) {
            ToastUtils.showToast(it)
            startView.isEnabled = true
            selectOldView.isEnabled = true
        }
        viewModel?.shiftResult?.observe(this) {
            selectOldView.isEnabled = true
            oldAdapter?.updateData(it.nowShifts)
            newAdapter?.updateData(it.newShifts)
            rvOldShift.show(it.nowShifts.isNotEmpty())
            rvNewShift.show(it.newShifts.isNotEmpty())
            tvOldEmpty.show(it.nowShifts.isNullOrEmpty())
            tvNewEmpty.show(it.newShifts.isNullOrEmpty())
            btnSubmit.setButtonStatus(SUButtonStatus.Disabled)
        }
        viewModel?.shiftError?.observe(this) {
            selectOldView.isEnabled = true
        }

        viewModel?.shiftSubmitResult?.observe(this) {
            btnSubmit.setButtonStatus(SUButtonStatus.Activated)
            if (it == "1") {
                SimpleRxBus.post(UIRefreshScheduleEvent())
                ToastUtils.showToast(getString(com.iandroid.allclass.lib_common.R.string.share_success))
                finish()
            } else {
                ToastUtils.showToast(it)
            }
        }
        viewModel?.shiftChangeRuleResult?.observe(this) { rule ->
            // 换班规则
            rule?.let {
                tvChangeNum.show(it.shiftChangeLimit.isNotEmpty())
                tvRules.show(it.shiftChangeRule.isNotEmpty())
                tvRulesContent.show(it.shiftChangeRule.isNotEmpty())
                tvChangeNum.text = it.shiftChangeLimit
                tvRulesContent.text = it.shiftChangeRule
            }
        }
        viewModel?.shiftChangeRuleError?.observe(this) {
            ToastUtils.showToast(it)
        }
        viewModel?.getShiftChangeRule()
    }

    private fun setListener() {
        typeView.clickWithTrigger {
            showTypeDialog()
        }

        startView.clickWithTrigger {
            showWeekDialog()
        }

        selectOldView.clickWithTrigger {
            showOldShiftDialog()
        }

        oldAdapter?.setOnItemClickListener {
            oldSelectShiftId = it.shiftId
            setBtnSubmitStatus()
        }

        newAdapter?.setOnItemClickListener {
            newSelectShiftId = it.shiftId
            setBtnSubmitStatus()
        }
        btnSubmit.clickWithTrigger {
            btnSubmit.setButtonStatus(SUButtonStatus.Loading)
            Log.e("btnSubmit", "type:$typeId  start:$startWeekId  day:$selectWeekDay")
            val week = if (startWeekId == ScheduleConst.START_THIS_WEEK) {
                weekInfo?.thisWeek
            } else {
                weekInfo?.nextWeek
            }
            val start = week?.startDay.orEmpty()
            val end = week?.endDay.orEmpty()
            viewModel?.shiftChangeSubmit(
                typeId,
                start,
                end,
                selectWeekDay,
                oldSelectShiftId,
                newSelectShiftId
            )
        }
    }

    private fun setBtnSubmitStatus() {
        if (typeId > 0 && oldSelectShiftId > 0 && newSelectShiftId > 0
            && selectWeekDay.isNotEmpty() && startWeekId > 0
            && !btnSubmit.isLoadingStatus()
        ) {
            btnSubmit.setButtonStatus(SUButtonStatus.Activated)
        } else {
            btnSubmit.setButtonStatus(SUButtonStatus.Disabled)
        }
    }

    /**
     * 切换周的时候，重置数据
     */
    private fun resetWeekShift() {
        oldAdapter?.updateData(arrayListOf())
        newAdapter?.updateData(arrayListOf())
        rvOldShift.show(false)
        rvNewShift.show(false)
        tvOldEmpty.show(false)
        tvNewEmpty.show(false)
        selectWeekDay = ""
        selectOldView.setContent("")
        selectNewView.setContent("")
        selectOldView.setHintContent(getString(R.string.hint_select_old_Shift))
        selectNewView.setHintContent(getString(R.string.hint_select_new_Shift))
        if (!btnSubmit.isLoadingStatus()) {
            btnSubmit.setButtonStatus(SUButtonStatus.Disabled)
        }
    }

    /**
     * 显示类型选择器
     */
    private fun showTypeDialog() {
        ComWheelDialog.Builder()
            .setTitle("Type")
            .setContext(getTypeContent())
            .setConfirm(com.iandroid.allclass.lib_basecore.R.string.confirm) {
                it.let {
                    if (typeId != it.id && selectWeekDay.isNotEmpty()) {
                        selectOldView.isEnabled = false
                        selectOldView.isEnabled = false
                        viewModel?.getShiftList(selectWeekDay, it.id)
                    }
                    typeId = it.id
                    typeView.setContent(it.content.orEmpty())
                    setBtnSubmitStatus()
                }
            }.create()
            .show(supportFragmentManager, ComWheelDialog::javaClass.name)
    }

    /**
     * 显示周选择器
     */
    private fun showWeekDialog() {
        ComWheelDialog.Builder()
            .setTitle(getString(R.string.text_start_week))
            .setContext(getStartWeekContent())
            .setConfirm(com.iandroid.allclass.lib_basecore.R.string.confirm) {
                it.let {
                    if (it.id > 0 && startWeekId != it.id) {
                        startWeekId = it.id
                        startView.setContent(it.content.orEmpty())
                        resetWeekShift()
                    }
                }
            }.create()
            .show(supportFragmentManager, ComWheelDialog::javaClass.name)
    }

    /**
     * 显示周日期选择器
     */
    private fun showOldShiftDialog() {
        if (typeId <= 0) {
            ToastUtils.showToast(getString(R.string.text_shift_change_select_error2))
            return
        }
        if (startWeekId <= 0) {
            ToastUtils.showToast(getString(R.string.text_shift_change_select_error))
            return
        }
        ComWheelDialog.Builder()
            .setTitle(getString(R.string.text_change_day))
            .setContext(getOldShiftContent())
            .setConfirm(com.iandroid.allclass.lib_basecore.R.string.confirm) {
                it.let {
                    selectOldView.setContent(it.content.orEmpty())
                    selectNewView.setContent(it.content.orEmpty())
                    val selectDay = ScheduleDate.getYMDStr(it.ymdwMap)
                    getShiftList(selectDay)
                }
            }.create()
            .show(supportFragmentManager, ComWheelDialog::javaClass.name)
    }

    /**
     * 获取用于换班的数据
     */
    private fun getShiftList(selectDay: String) {
        if (selectDay.isNotEmpty() && selectDay != selectWeekDay) {
            selectWeekDay = selectDay
            selectOldView.isEnabled = false
            selectOldView.isEnabled = false
            viewModel?.getShiftList(selectWeekDay, typeId)
        }
    }

    /**
     * 获取换班类型列表
     */
    private fun getTypeContent(): List<ComContentEntity> {
        val types = arrayListOf<ComContentEntity>().apply {
            add(ComContentEntity().apply {
                id = ScheduleConst.TYPE_FOREVER
                content = getString(R.string.text_forever)
            })
            add(ComContentEntity().apply {
                id = ScheduleConst.TYPE_ONE_WEEK
                content = getString(R.string.text_one_week)
            })
        }
        return types
    }

    /**
     * 获取换班类型列表
     */
    private fun getStartWeekContent(): List<ComContentEntity> {
        val types = arrayListOf<ComContentEntity>().apply {
            weekInfo?.let { info ->
                add(ComContentEntity().apply {
                    val start = ScheduleDate.formatToMonthDay(info.thisWeek?.startDay.orEmpty())
                    val end = ScheduleDate.formatToMonthDay(info.thisWeek?.endDay.orEmpty())
                    id = ScheduleConst.START_THIS_WEEK
                    content = buildString {
                        append(start)
                        append("~")
                        append(end)
                        append(" ")
                        append(getString(R.string.text_schedule_this_week))
                    }
                })
                add(ComContentEntity().apply {
                    val start = ScheduleDate.formatToMonthDay(info.nextWeek?.startDay.orEmpty())
                    val end = ScheduleDate.formatToMonthDay(info.nextWeek?.endDay.orEmpty())
                    id = ScheduleConst.START_NEXT_WEEK
                    content = buildString {
                        append(start)
                        append("~")
                        append(end)
                        append(" ")
                        append(getString(R.string.text_schedule_next_week))
                    }
                })
            }
        }
        return types
    }

    /**
     * 获取对应周的天列表
     */
    private fun getOldShiftContent(): List<ComContentEntity> {
        val shifts = arrayListOf<ComContentEntity>().apply {
            weekInfo?.let { info ->
                if (startWeekId == ScheduleConst.START_THIS_WEEK) {
                    info.thisWeek?.let {
                        val num =
                            7 - (ScheduleDate.getDaysDifference(it.startDay, info.currentDay)
                                .minus(0).toInt())
                        val weekDays = ScheduleDate.getNextXDays(info.currentDay, num)
                        weekDays.forEach { day ->
                            add(
                                ComContentEntity().apply {
                                    content = ScheduleDate.getMDWStr(day)
                                    ymdwMap = ScheduleDate.splitDateString(day, false)
                                }
                            )
                        }
                    }
                } else if (startWeekId == ScheduleConst.START_NEXT_WEEK) {
                    info.nextWeek?.let {
                        val weekDays = ScheduleDate.getNextXDays(it.startDay, 7)
                        weekDays.forEach { day ->
                            add(
                                ComContentEntity().apply {
                                    content = ScheduleDate.getMDWStr(day)
                                    ymdwMap = ScheduleDate.splitDateString(day, false)
                                }
                            )
                        }
                    }
                }
            }
        }
        return shifts
    }
}