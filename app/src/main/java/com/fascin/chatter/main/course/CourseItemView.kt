package com.fascin.chatter.main.course

import android.content.Context
import android.graphics.Rect
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.bean.CourseEntity
import com.fascin.chatter.main.HomeConfig
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RvItem
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.beans.WebIntent
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.utils.ToastUtils
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.castObject
import com.iandroid.allclass.lib_common.utils.exts.getColorEx
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.textColorResource
import com.iandroid.allclass.lib_common.utils.exts.toPx
import kotlinx.android.synthetic.main.itemview_course.view.llIcon
import kotlinx.android.synthetic.main.itemview_course.view.tvMandatory
import kotlinx.android.synthetic.main.itemview_course.view.tvNewTag
import kotlinx.android.synthetic.main.itemview_course.view.tvQuiz
import kotlinx.android.synthetic.main.itemview_course.view.tvStatus
import kotlinx.android.synthetic.main.itemview_course.view.tvTitle
import net.csdn.roundview.RoundTextView

/**
 * Course Item
 * @Created: QuanZH
 * @Date: 2023/9/20
 */
@RvItem(id = AppViewType.courseItemView, spanCount = 1)
class CourseItemView(context: Context, fm: FragmentManager, parent: ViewGroup) :
    BaseRvItemView(context, fm, parent) {

    override fun attachLayoutId(): Int {
        return R.layout.itemview_course
    }

    override fun initView(context: Context?, view: View?) {

    }

    override fun setView() {
        val entity = getItemData() ?: return
        itemView?.run {
            tvTitle.text = entity.title
            tvNewTag.show(entity.isNew == 1)
            // 没有试卷时，不显示答题按钮
            tvQuiz.show(entity.hasPaper == 1)
            tvMandatory.show(entity.required == 1)
            tvStatus.show(true)
            when (entity.status) {
                CourseEntity.STATUS_PASS -> {
                    tvStatus.text = context.getString(R.string.course_status_pass)
                    tvStatus.textColorResource = com.iandroid.allclass.lib_common.R.color.color_24C004
                    tvStatus.setBackgroundResource(com.iandroid.allclass.lib_common.R.color.color_f8fff6)
                    (tvStatus as RoundTextView).setStrokeColor(context.getColorEx(com.iandroid.allclass.lib_common.R.color.color_24C004))
                }

                CourseEntity.STATUS_NOT_PASS -> {
                    tvStatus.text = context.getString(R.string.course_status_not_pass)
                    tvStatus.textColorResource = com.iandroid.allclass.lib_common.R.color.color_F74E57
                    tvStatus.setBackgroundResource(com.iandroid.allclass.lib_common.R.color.color_fff7f8)
                    (tvStatus as RoundTextView).setStrokeColor(context.getColorEx(com.iandroid.allclass.lib_common.R.color.color_F74E57))
                }

                CourseEntity.STATUS_TO_DO -> {
                    tvStatus.text = context.getString(R.string.course_status_to_do)
                    tvStatus.textColorResource = com.iandroid.allclass.lib_common.R.color.color_d49611
                    tvStatus.setBackgroundResource(com.iandroid.allclass.lib_common.R.color.color_fffcf6)
                    (tvStatus as RoundTextView).setStrokeColor(context.getColorEx(com.iandroid.allclass.lib_common.R.color.color_d49611))
                }

                else -> {
                    tvStatus.show(false)
                }
            }
            llIcon.show(tvNewTag.isVisible || tvQuiz.isVisible || tvMandatory.isVisible || tvStatus.isVisible)
            itemView.clickWithTrigger {
                AppModule.userActive()
                if (entity.h5Url.isNotEmpty()) {
                    if (entity.isNew == 1) {
                        entity.isNew = 0
                        tvNewTag.show(false)
                        HomeConfig.courseNeedRefresh = true
                    }
                    context.routeAction(ActionType.actionTypeToWebActivity) {
                        it.param = WebIntent().also { webIntent ->
                            webIntent.showTitle = true
                            webIntent.url = entity.h5Url
                            webIntent.actionParam = entity
                            if (entity.hasPaper == 1) {
                                // 有试卷时，web页显示答题按钮
                                webIntent.pageFrom = ActionType.actionCourseList
                            }
                        }
                    }
                } else {
                    ToastUtils.showToast("Course content url error")
                }
            }
        }
    }

    override fun getItemOffsets(parent: RecyclerView, view: View?, outRect: Rect, position: Int): Boolean {
        outRect.top = if (position == 0) 16.toPx else 0
        return true
    }

    private fun getItemData(): CourseEntity? = data?.castObject<CourseEntity>()
}