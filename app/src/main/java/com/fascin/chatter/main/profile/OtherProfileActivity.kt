package com.fascin.chatter.main.profile

import android.os.Bundle
import android.util.Log
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.bean.ModelUserEntity
import com.fascin.chatter.bean.NewFcCountEntity
import com.fascin.chatter.bean.PrivacyListIntent
import com.fascin.chatter.bean.event.UISelectModelEvent
import com.fascin.chatter.bean.event.UISelectModelFailedEvent
import com.fascin.chatter.component.player.SUHttpCacheServer
import com.fascin.chatter.component.player.SUMediaPlayer
import com.fascin.chatter.component.player.SURenderCallback
import com.fascin.chatter.component.player.SUVolumeEvent
import com.fascin.chatter.component.views.OnMediaChangedCallback
import com.fascin.chatter.config.Config
import com.fascin.chatter.getUserId
import com.fascin.chatter.im.IMConfig
import com.fascin.chatter.main.profile.view.ProfileFlashChatDialog
import com.fascin.chatter.utils.SUStringUtils
import com.iandroid.allclass.lib_common.base.ChatterBaseActivity
import com.iandroid.allclass.lib_common.beans.ActionMenuItem
import com.iandroid.allclass.lib_common.beans.MediaEntity
import com.iandroid.allclass.lib_common.beans.ReportIntent
import com.iandroid.allclass.lib_common.beans.UserEntity
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.route.routeActionByParam
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.ToastUtils
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.getCompatColor
import com.iandroid.allclass.lib_common.utils.exts.immersiveStatusBarWithMargin
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.views.ActionSheetDialog
import com.iandroid.allclass.lib_common.views.CommonAlertDialog
import io.rong.imkit.userinfo.RongUserInfoManager
import kotlinx.android.synthetic.main.activity_profile_other.defaultPlaceView
import kotlinx.android.synthetic.main.activity_profile_other.ivPrivacyPic
import kotlinx.android.synthetic.main.activity_profile_other.ivPublicPic
import kotlinx.android.synthetic.main.activity_profile_other.llMessageSetting
import kotlinx.android.synthetic.main.activity_profile_other.other_profile_age
import kotlinx.android.synthetic.main.activity_profile_other.other_profile_desires
import kotlinx.android.synthetic.main.activity_profile_other.other_profile_gender_value
import kotlinx.android.synthetic.main.activity_profile_other.other_profile_interests
import kotlinx.android.synthetic.main.activity_profile_other.other_profile_medias
import kotlinx.android.synthetic.main.activity_profile_other.other_profile_nickname
import kotlinx.android.synthetic.main.activity_profile_other.other_profile_orient_value
import kotlinx.android.synthetic.main.activity_profile_other.other_profile_sign_value
import kotlinx.android.synthetic.main.activity_profile_other.picEntrance
import kotlinx.android.synthetic.main.activity_profile_other.scroll_layout
import kotlinx.android.synthetic.main.activity_profile_other.userDataPlaceView
import kotlinx.android.synthetic.main.activity_profile_other.userPageBack
import kotlinx.android.synthetic.main.activity_profile_other.userPageMore
import kotlinx.android.synthetic.main.activity_profile_other.userPageTitleView
import kotlinx.android.synthetic.main.include_message_setting_add_btn.btnMessageSetting
import kotlinx.android.synthetic.main.include_message_setting_add_btn.maskView
import kotlinx.android.synthetic.main.include_message_setting_add_btn.otherFlashChatNum
import kotlinx.android.synthetic.main.include_message_setting_add_btn.rllFlashChat
import kotlinx.android.synthetic.main.item_medias_image.view.medias_image_view
import kotlinx.android.synthetic.main.item_medias_image.view.medias_video_container

class OtherProfileActivity : ChatterBaseActivity(), OnMediaChangedCallback {
    private var curUser: UserEntity? = null
    private var notMatchModels = ArrayList<ModelUserEntity>()
    private var fcFailMsg: String = ""
    private var isFromSelectModel = false
    private var modelSelected = false
    private var modelSelectRequesting = false
    private val viewModel by lazy {
        ViewModelProvider(
            this,
            ProfileViewModel.ViewModeFactory()
        )[ProfileViewModel::class.java]
    }

    private val suMediaPlayer by lazy { SUMediaPlayer(this) }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_profile_other)
    }

    override fun initBaseContent() {
        super.initBaseContent()
        AppModule.userActive()
        curUser = parseJsonParams<UserEntity>()
        isFromSelectModel = curUser?.from == -100
        immersiveStatusBarWithMargin(userPageTitleView)
        userPageBack.setOnClickListener {
            finish()
        }

        showTitleBar(false)
        userPageMore.setOnClickListener {
            ActionSheetDialog(
                mutableListOf(
                    ActionMenuItem(getString(R.string.action_report)) {
                        routeActionByParam<ReportIntent>(ActionType.actionReportDialog) {
                            it.toUserId = curUser?.userId.orEmpty()
                            it.from = Config.userFromUserPage
                        }
                    },
                    ActionMenuItem(
                        if (curUser?.block == 0) getString(R.string.action_block) else getString(
                            R.string.action_unblock
                        )
                    ) {
                        viewModel.userBan(
                            curUser?.userId.orEmpty(),
                            if (curUser?.block == 0) 1 else 2
                        )
                    }
                ))
                .show(supportFragmentManager, ActionSheetDialog::class.java.name)
        }
        updateUserInfo(curUser)
        if (isFromSelectModel && !modelSelected) {
            btnMessageSetting.text = getString(R.string.choose_her_continue)
            btnMessageSetting.setTextColor(getCompatColor(com.iandroid.allclass.lib_common.R.color.ffffff_90))
            llMessageSetting.show(true)
            rllFlashChat.setBackgroundResource(R.mipmap.bg_new_anchor_highlight)
        }

        viewModel.userInfoResult.observe(this) {
            updateUserInfo(it)
            // 获取可以flash chat的model列表
            curUser?.let {
                if (curUser?.isModel == false)
                    viewModel.getNotMatchModel(curUser?.userId?.getUserId())
            }
        }

        viewModel.blockStatus.observe(this) {
            curUser?.block = it
            if (it > 0) finish()
        }

        viewModel.notMatchModelsResult.observe(this) {
            if (it.list.isNotEmpty()) {
                notMatchModels.clear()
                notMatchModels.addAll(toModelUserList(it.list))
            }
            fcFailMsg = it.fcFailMsg
            llMessageSetting.show(notMatchModels.isNotEmpty())
            btnMessageSetting.text = getString(R.string.flash_chat_continue)

            otherFlashChatNum.text = "${it.fcCount}"
            otherFlashChatNum.show(it.fcCount > 0)
            if (it.fcFailMsg.isNotEmpty()) {
                rllFlashChat.setBackgroundColor(getCompatColor(com.iandroid.allclass.lib_common.R.color.color_d9d9d9))
                btnMessageSetting.setTextColor(getCompatColor(R.color.cr_bfbfbf))
                otherFlashChatNum.show(false)
            }
        }

        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(NewFcCountEntity::class) {
            if (it.im_uid == curUser?.userId?.getUserId()) {
                viewModel?.getNotMatchModel(curUser?.userId?.getUserId())
            }
        })

        viewModel.notMatchModelsError.observe(this) {
            ToastUtils.showToast("")
        }

        viewModel.selectModelResult.observe(this) {
            modelSelectRequesting = false
            if (isFromSelectModel) {
                if (it == 1) {
                    SimpleRxBus.post(UISelectModelEvent(curUser?.userId.orEmpty()))
                    finish()
                } else if (it == 1020) {
//                     1020: 此model不可绑定，绑定失败
                    showChooseHerFailedDialog()
                }
            }
        }

        viewModel.getUserInfo(curUser?.userId.orEmpty())
        viewModel.compositeDisposable?.add(
            SimpleRxBus.observe(SUVolumeEvent::class) {
                suMediaPlayer.setVolume(it.volume)
                other_profile_medias.showMuteView()
            }
        )

        //打开Flash Chat弹窗
        rllFlashChat.clickWithTrigger {
            AppModule.userActive()
            curUser?.run {
                if (isFromSelectModel) {
                    showChooseHerDialog()
                } else {
                    if (fcFailMsg.isNotEmpty()) {
                        ToastUtils.showToast(fcFailMsg)
                        return@clickWithTrigger
                    }
                    // 跳转flash chat弹窗
                    ProfileFlashChatDialog(curUser?.userId, notMatchModels).show(
                        supportFragmentManager,
                        ProfileFlashChatDialog::class.java.name
                    )
                }
            }

        }

        // 去预览私密相册
        ivPrivacyPic.setOnClickListener {
            toPhotosPreview(IMConfig.AlbumPrivate)
        }
        // 去预览相册
        ivPublicPic.setOnClickListener {
            toPhotosPreview(IMConfig.AlbumPublic)
        }

        scroll_layout.viewTreeObserver.addOnScrollChangedListener {
            curUser.let {
                if (it != null && it.isModel) {
                    val contentViewHeight = scroll_layout.getChildAt(0).height
                    val scrollViewHeight = scroll_layout.height
                    if (contentViewHeight <= scrollViewHeight) {
                        maskView.show(true)
                    } else {
                        val isAtBottom =
                            scroll_layout.getChildAt(0).bottom <= scroll_layout.height + scroll_layout.scrollY
                        maskView.show(!isAtBottom, true)
                    }
                }
            }
        }
    }

    /**
     * 进入相册预览
     */
    private fun toPhotosPreview(type: Int) {
        if (curUser != null && curUser!!.isModel) {
            routeAction(ActionType.actionPrivacyActivity) {
                it.param = PrivacyListIntent().also { intent ->
                    intent.request_code = 0
                    intent.type = type
                    // _100:模拟modelId_userid，否则接口请求不通
                    intent.imId = curUser?.userId!! + "_100"
                    intent.from = 1
                }
            }
        }
    }

    private fun updateUserInfo(userEntity: UserEntity?) {
        userEntity ?: return
        curUser = userEntity
        // chatter model不需要拉黑举报user
        userPageMore.show(false)
        other_profile_medias.apply {
            enableUserInputEvent(true)
            setImageRoundedCorner(0)
            setDataSource(userEntity.mediaList.orEmpty(), true)
            showCornerMaskView(false)
            setOnMediaChangedCallback(this@OtherProfileActivity)
        }

        if (userEntity.nickname.isNullOrEmpty() || userEntity.gender == 0) {
            defaultPlaceView.show(true)
            userDataPlaceView.show(false)
        } else {
            defaultPlaceView.show(false)
            userDataPlaceView.show(true)
        }

        other_profile_nickname.text = userEntity.nickname
        other_profile_age.text = ",${userEntity.age}"
        other_profile_gender_value.text = SUStringUtils.getOrientStr(userEntity.gender)
        other_profile_orient_value.text = SUStringUtils.getOrientStr(userEntity.sexuality)
        other_profile_sign_value.text = userEntity.sign
        other_profile_interests.apply {
            setIsSingleLineWrapWidth(false)
            setDataSource(userEntity.tags.orEmpty())
        }
        other_profile_desires.apply {
            setIsSingleLineWrapWidth(false)
            setDataSource(userEntity.desires.orEmpty())
        }
        picEntrance.show(userEntity.isModel)
        if (isFromSelectModel || userEntity.status == 1) {
            ivPublicPic.show(false)
            ivPrivacyPic.show(false)
        }
    }

    private fun toModelUserList(modelIds: List<String>?): ArrayList<ModelUserEntity> {
        return ArrayList<ModelUserEntity>().apply {
            modelIds?.forEach {
                RongUserInfoManager.getInstance().getUserInfo(it)?.let { userInfo ->
                    add(ModelUserEntity().also { model ->
                        model.userId = userInfo.userId
                        model.avatarUrl = userInfo.portraitUri.toString()
                        model.nickname = userInfo.name
                    })
                }
            }
        }
    }

    /**
     * 确定model
     */
    private fun showChooseHerDialog() {
        CommonAlertDialog.Builder()
            .setTitle(getString(R.string.select_model_choose_title))
            .setContext(getString(R.string.select_model_choose_desc))
            .setCancel(getString(com.iandroid.allclass.lib_common.R.string.btn_cancel)) {}
            .setConfirm(getString(com.iandroid.allclass.lib_basecore.R.string.confirm)) {
                selectModel()
            }.create().show(
                supportFragmentManager, CommonAlertDialog::class.java.name
            )
    }

    /**
     * 确定model失败的弹窗
     */
    private fun showChooseHerFailedDialog() {
        CommonAlertDialog.Builder()
            .setTitle(getString(R.string.select_model_choose_failed_title))
            .setContext(getString(R.string.select_model_choose_failed_desc))
            .setCancelable(false)
            .showCloseBtn(false)
            .setConfirm(getString(R.string.dialog_ok)) {
                SimpleRxBus.post(UISelectModelFailedEvent())
                finish()
            }.create().show(
                supportFragmentManager, CommonAlertDialog::class.java.name
            )
    }

    /**
     * 新账号新手任务选择model
     */
    private fun selectModel() {
        curUser?.run {
            if (isFromSelectModel && !modelSelectRequesting) {
                modelSelectRequesting = true
                viewModel.selectModel(userId)
            }
        }
    }

    override fun immersiveIntoStatusBar(): Boolean {
        return true
    }

    override fun isShowTitleBar(): Boolean {
        return false
    }


    override fun onResume() {
        super.onResume()
        suMediaPlayer.start()
    }

    override fun onPause() {
        super.onPause()
        suMediaPlayer.pause()
    }

    override fun onMediaChanged(mediaEntity: MediaEntity) {
        Log.d("David", "onMediaChanged==${mediaEntity.url}")
        val mediasView = other_profile_medias.getCurrentView() ?: return
        if (mediaEntity.type == 1) {
            mediasView.medias_image_view.show(false)
            suMediaPlayer.attach(mediasView.medias_video_container, object : SURenderCallback {
                override fun onRenderStart() {
                    mediasView.medias_image_view.show(false)
                    mediasView.medias_video_container.alpha = 1F
                }
            })
            suMediaPlayer.setDataSource(SUHttpCacheServer.getProxyUrl(mediaEntity.url))
            mediasView.post { mediasView.medias_image_view.show(true) }
            other_profile_medias.showMuteView()
        } else {
            suMediaPlayer.detach()
            other_profile_medias.hideMuteView()
        }
    }

    override fun onMuteTapped() {

    }
}