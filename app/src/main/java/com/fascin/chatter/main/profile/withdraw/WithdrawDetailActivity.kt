package com.fascin.chatter.main.profile.withdraw

import android.os.Bundle
import android.view.LayoutInflater
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.utils.SUDateUtils
import com.iandroid.allclass.lib_common.base.ChatterBaseActivity
import com.iandroid.allclass.lib_common.beans.SubItem
import com.iandroid.allclass.lib_common.beans.WithdrawListBean
import com.iandroid.allclass.lib_common.utils.exts.getColorEx
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.toPx
import com.iandroid.allclass.lib_common.widgets.LeftRightTextView
import kotlinx.android.synthetic.main.activity_withdraw_detail.llWithdrawAccountDetail
import kotlinx.android.synthetic.main.activity_withdraw_detail.llWithdrawReason
import kotlinx.android.synthetic.main.activity_withdraw_detail.tvApplyAmount
import kotlinx.android.synthetic.main.activity_withdraw_detail.tvApplyDate
import kotlinx.android.synthetic.main.activity_withdraw_detail.tvChatterId
import kotlinx.android.synthetic.main.activity_withdraw_detail.tvPassedTime
import kotlinx.android.synthetic.main.activity_withdraw_detail.tvPaymentTime
import kotlinx.android.synthetic.main.activity_withdraw_detail.tvState
import kotlinx.android.synthetic.main.activity_withdraw_detail.tvWithdrawReason

/**
 * Created by: LXL
 * Date: 2024/10/10
 * Time: 14:09
 * 提现明细
 */
class WithdrawDetailActivity : ChatterBaseActivity() {
    private var withdrawInfoEntity: WithdrawListBean? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_withdraw_detail)
        setTitle("Withdrawl Details")
    }

    override fun initBaseContent() {
        super.initBaseContent()
        AppModule.userActive()
        withdrawInfoEntity = parseJsonParams<WithdrawListBean>()

        withdrawInfoEntity?.apply {
            tvChatterId.setRightText(this.cId)
            tvApplyAmount.setRightText("$currencyUnit $amount")
            tvApplyDate.setRightText(SUDateUtils.convertTimestampToDate(applyTime))

            //动态添加主播收款信息
            kotlin.runCatching {
                subItems?.forEach { subItem ->
                    val leftRightTextView = createLeftRightTextView(subItem)
                    llWithdrawAccountDetail.addView(leftRightTextView)
                }
            }

            //提现单状态 1-审核中 2-审核通过 3-审核未通过 4-提现成功 5-提现失败
            when (status) {
                1 -> {
                    tvState.setRightText("Pending")
                    tvState.setRightTextColor(getColorEx(com.iandroid.allclass.lib_common.R.color.color_0B81EE))
                }

                2 -> {
                    tvState.setRightText("Pending Payment")
                    tvState.setRightTextColor(getColorEx(com.iandroid.allclass.lib_common.R.color.color_0B81EE))
                }

                3 -> {
                    tvState.setRightText("Failed")
                    tvState.setRightTextColor(getColorEx(com.iandroid.allclass.lib_common.R.color.color_F5222D))
                }

                4 -> {  //提现成功
                    tvState.setRightText("Withdrawl Successed")
                    tvState.setRightTextColor(getColorEx(com.iandroid.allclass.lib_common.R.color.color_24C004))
                }

                5 -> { //提现失败
                    tvState.setRightText("Withdrawl Failed")
                    tvState.setRightTextColor(getColorEx(com.iandroid.allclass.lib_common.R.color.color_F5222D))
                }

                else -> {  //Pending
                    tvState.setRightText("Pending")
                    tvState.setRightTextColor(getColorEx(com.iandroid.allclass.lib_common.R.color.color_0B81EE))
                }
            }

            tvPassedTime.setRightText(if (auditTime == 0L) "-" else SUDateUtils.convertTimestampToDate(auditTime))
            tvPaymentTime.setRightText(if (grantTime == 0L) "-" else SUDateUtils.convertTimestampToDate(grantTime))

            llWithdrawReason.show(refuseReason.isNotEmpty())
            tvWithdrawReason.text = refuseReason
        }
    }

    // 动态创建 LeftRightTextView
    private fun createLeftRightTextView(subItem: SubItem): LeftRightTextView {
        val leftRightTextView = LayoutInflater.from(this)
            .inflate(R.layout.item_left_right_text_view, null) as LeftRightTextView
        leftRightTextView.setLeftText(subItem.key)
        leftRightTextView.setRightText(subItem.value)
        leftRightTextView.setPadding(0, 16.toPx, 0, 0)
        return leftRightTextView
    }
}