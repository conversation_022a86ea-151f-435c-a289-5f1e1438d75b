package com.fascin.chatter.main.profile.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.bean.ModelUserEntity
import com.iandroid.allclass.lib_common.GlideLoader.loadImageCircleCrop
import com.iandroid.allclass.lib_common.beans.UserEntity
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.routeActionByParam
import kotlinx.android.synthetic.main.itemview_me_model.view.modelHeadImg
import kotlinx.android.synthetic.main.itemview_me_model.view.modelId
import kotlinx.android.synthetic.main.itemview_me_model.view.modelNickName

/**
 * Me：Model List adapter
 */
class ModelListAdapter(val context: Context) : RecyclerView.Adapter<ModelListAdapter.ViewHolder>() {

    private val dataList = mutableListOf<ModelUserEntity>()

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

    fun updateData(data: List<ModelUserEntity>?) {
        dataList.clear()
        if (!data.isNullOrEmpty()) {
            dataList.addAll(data)
        }
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.itemview_me_model, parent, false)
        )

    }

    override fun getItemCount(): Int = dataList.size

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = dataList[position]
        holder.itemView.modelNickName.text = item.nickname
        holder.itemView.modelId.text = item.userId
        holder.itemView.modelHeadImg.loadImageCircleCrop(context, item.avatarUrl)
        holder.itemView.setOnClickListener { view ->
            // 跳转他人个人主页
            view.context?.routeActionByParam<UserEntity>(ActionType.actionOtherProfile) {
                it.userId = item.userId
            }
        }
    }
}