package com.fascin.chatter.main.schedule

import android.os.Bundle
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.bean.SchedulePerTabEntity
import com.fascin.chatter.bean.SchedulePerTabItemEntity
import com.fascin.chatter.bean.event.UIRefreshScheduleEvent
import com.iandroid.allclass.lib_common.base.ChatterBaseActivity
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import kotlinx.android.synthetic.main.activity_schedule.btnRequest
import kotlinx.android.synthetic.main.activity_schedule.scheduleBack
import kotlinx.android.synthetic.main.activity_schedule.spContentLayout
import kotlinx.android.synthetic.main.activity_schedule.sptLayout
import kotlinx.android.synthetic.main.activity_schedule.tvRulesContent

/**
 * @Desc: 排班展示页
 * @Created: Quan
 * @Date: 2024/11/15
 */
class ScheduleActivity : ChatterBaseActivity() {

    private var viewModel: ScheduleModel? = null
    private var tabEntity: SchedulePerTabEntity? = null
    private var currTab = ScheduleConst.TAB_THIS // 0 last  1 this  2 next

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_schedule)
    }

    override fun initBaseContent() {
        super.initBaseContent()
        AppModule.userActive()
        setModel()
        setListener()
    }

    private fun setModel() {
        viewModel = ViewModelProvider(this).get(ScheduleModel::class.java)
        viewModel?.scheduleTabResult?.observe(this) {
            if (it != null) {
                tabEntity = it
                sptLayout.setViewData(it, currTab)
            }
        }
        viewModel?.scheduleTabError?.observe(this) {

        }
        viewModel?.scheduleContentResult?.observe(this) {
            if (it != null) {
                ScheduleDate.SERVER_CURRENT_DATE = it.currentDay
                spContentLayout.setViewData(it, getTabEntity())
            }
        }
        viewModel?.scheduleContentError?.observe(this) {

        }

        viewModel?.scheduleContentRuleResult?.observe(this) {
            tvRulesContent.text = it.content
        }

        // 刷新排班数据
        viewModel?.compositeDisposable?.add(
            SimpleRxBus.observe(UIRefreshScheduleEvent::class) {
                viewModel?.getSchedulePreContent(
                    getTabEntity()?.startDay.orEmpty(),
                    getTabEntity()?.endDay.orEmpty()
                )
            }
        )
        viewModel?.getSchedulePreTab()
        viewModel?.getSchedulePreRule()
    }

    private fun setListener() {
        scheduleBack.clickWithTrigger {
            finish()
        }
        sptLayout.setSelectListener {
            currTab = it
            viewModel?.getSchedulePreContent(
                getTabEntity()?.startDay.orEmpty(),
                getTabEntity()?.endDay.orEmpty()
            )
        }

        btnRequest.clickWithTrigger {
            routeAction(ActionType.actionScheduleRequest)
        }
    }

    /**
     * tab对应的内容
     */
    private fun getTabEntity(): SchedulePerTabItemEntity? {
        return when (currTab) {
            ScheduleConst.TAB_LAST -> tabEntity?.lastWeek
            ScheduleConst.TAB_THIS -> tabEntity?.thisWeek
            ScheduleConst.TAB_NEXT -> tabEntity?.nextWeek
            else -> null
        }
    }

    override fun immersiveIntoStatusBar(): Boolean {
        return false
    }

    override fun isShowTitleBar(): Boolean {
        return false
    }
}