package com.fascin.chatter.main.profile.invite

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.widget.TextView
import androidx.activity.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.viewpager2.widget.ViewPager2
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.databinding.ActivityInviteFriendsBinding
import com.fascin.chatter.main.profile.InvitePager
import com.fascin.chatter.main.profile.InvitePagerAdapter
import com.google.android.material.tabs.TabLayoutMediator
import com.iandroid.allclass.lib_basecore.utils.SpanUtil
import com.iandroid.allclass.lib_common.base.ChatterBaseActivity
import com.iandroid.allclass.lib_common.utils.ToastUtils
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.getCompatColor
import com.iandroid.allclass.lib_common.utils.exts.immersiveStatusBarWithMargin
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.toPx

/**
 *  @author: LXL
 *  @description: 邀请好友
 *  @date: 2024/9/2 16:18
 */
class InviteFriendsActivity : ChatterBaseActivity() {

    private lateinit var binding: ActivityInviteFriendsBinding

    private val titles: List<InvitePager> = InvitePager.values().toList()

    private lateinit var invitePagerAdapter: InvitePagerAdapter
    private lateinit var onPagerChangeCallback: ViewPager2.OnPageChangeCallback

    private val viewModel: InviteFriendsViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityInviteFriendsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initView()
        initData()
    }

    override fun onDestroy() {
        if (this::onPagerChangeCallback.isInitialized) {
            binding.viewPager.unregisterOnPageChangeCallback(onPagerChangeCallback)
        }
        super.onDestroy()
    }

    private fun initView() {
        AppModule.userActive()
        immersiveStatusBarWithMargin(binding.ivInviteBack)

        invitePagerAdapter = InvitePagerAdapter(this, titles)

        binding.viewPager.adapter = invitePagerAdapter
        onPagerChangeCallback = object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                
                when(titles.getOrNull(position)) {
                    InvitePager.Activated -> {
                        binding.tabActivated.isSelected = true
                        binding.tabQualified.isSelected = false
                    }
                    InvitePager.Qualified -> {
                        binding.tabActivated.isSelected = false
                        binding.tabQualified.isSelected = true
                    }
                    else -> {}
                }
            }
        }
        binding.viewPager.registerOnPageChangeCallback(onPagerChangeCallback)


        binding.ivInviteBack.clickWithTrigger {
            finish()
        }

        binding.tabActivated.clickWithTrigger {
            binding.viewPager.currentItem = InvitePager.Activated.ordinal
        }

        binding.tabQualified.clickWithTrigger {
            binding.viewPager.currentItem = InvitePager.Qualified.ordinal
        }

        binding.ivInviteHistory.clickWithTrigger {
            startActivity(Intent(this, InviteHistoryActivity::class.java))
        }
    }

    private fun initData() {
        viewModel.inviteFriendResult.observe(this) { entity ->
            binding.tvEarn.setEarnMoney(entity.money)
            binding.tvInviteTime.text = "${entity.st} ~ ${entity.et}"
            binding.tvActive.text = entity.bindNum
            binding.tvQualified.text = entity.successNum
            binding.tvInviteBonus.text = entity.successMoney
            SpanUtil.create().addSection(getString(R.string.invite_copy_code))
                .addForeColorSection(" ${entity.code}", getCompatColor(R.color.white))
                .showIn(binding.tvCopyCode)

            binding.tvCopyCode.clickWithTrigger {
                copyToClipboard(entity.code)
            }
        }

        viewModel.inviteFriends()
    }

    private fun copyToClipboard(text: String) {
        val clipboard = getSystemService(CLIPBOARD_SERVICE) as ClipboardManager
        val clip = ClipData.newPlainText("Copied Text", text)
        clipboard.setPrimaryClip(clip)
        ToastUtils.showToast("Copied successfully")
    }

    private fun TextView.setEarnMoney(money: String) {
        val text = getString(R.string.invite_earn_text, money)
        SpanUtil.create().addSection(text)
            .setForeColor(money, getCompatColor(R.color.cl_ff0004))
            .setAbsSize(money, 17)
            .showIn(this)
    }

    override fun immersiveIntoStatusBar(): Boolean {
        return true
    }
}