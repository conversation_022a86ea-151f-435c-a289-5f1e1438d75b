package com.fascin.chatter.main.profile.view

import android.content.Context
import android.graphics.Paint
import android.graphics.Rect
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.bean.PenaltyEntity
import com.fascin.chatter.utils.isShieldModel
import com.iandroid.allclass.lib_basecore.utils.DeviceUtils
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RvItem
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.GlideLoader.loadImage
import com.iandroid.allclass.lib_common.utils.ToastUtils
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.castObject
import com.iandroid.allclass.lib_common.utils.exts.getCompatColor
import com.iandroid.allclass.lib_common.utils.exts.show
import io.rong.imkit.utils.RouteUtils
import io.rong.imlib.model.Conversation
import kotlinx.android.synthetic.main.itemview_penalties.view.ivPenaltiesModeAvatar
import kotlinx.android.synthetic.main.itemview_penalties.view.ivPenaltiesUserAvatar
import kotlinx.android.synthetic.main.itemview_penalties.view.tvPenaltiesDate
import kotlinx.android.synthetic.main.itemview_penalties.view.tvPenaltiesModelName
import kotlinx.android.synthetic.main.itemview_penalties.view.tvPenaltiesReason
import kotlinx.android.synthetic.main.itemview_penalties.view.tvPenaltiesSessionDetail
import kotlinx.android.synthetic.main.itemview_penalties.view.tvPenaltiesStatus
import kotlinx.android.synthetic.main.itemview_penalties.view.tvPenaltiesTitle
import kotlinx.android.synthetic.main.itemview_penalties.view.tvPenaltiesUserName


/**
 *  @author: LXL
 *  @description: 质检处罚
 *  @date: 2024/6/4 16:16
 */
@RvItem(id = AppViewType.penaltiesItemView, spanCount = 1)
class PenaltiesItemView(context: Context, fm: FragmentManager, parent: ViewGroup) :
    BaseRvItemView(context, fm, parent) {

    override fun attachLayoutId(): Int {
        return R.layout.itemview_penalties
    }

    override fun initView(context: Context?, view: View?) {

    }

    override fun setView() {
        val entity = getItemData() ?: return
        itemView?.run {
            tvPenaltiesTitle.text = entity.title
            when (entity.penaltyLevel) {   //1 红 2 黄 3 绿
                1 -> {
                    tvPenaltiesTitle.setTextColor(context.getCompatColor(com.iandroid.allclass.lib_basecore.R.color.cl_f5222d))
                }

                2 -> {
                    tvPenaltiesTitle.setTextColor(context.getCompatColor(com.iandroid.allclass.lib_basecore.R.color.cl_9370DB))
                }

                3 -> {
                    tvPenaltiesTitle.setTextColor(context.getCompatColor(com.iandroid.allclass.lib_common.R.color.color_24C004))
                }
            }
            tvPenaltiesDate.text = entity.penaltyDate
            tvPenaltiesModelName.text = entity.modelName
            tvPenaltiesUserName.text = entity.userName
            tvPenaltiesReason.text = entity.penaltyReason
            tvPenaltiesStatus.text = "Revoked"
            tvPenaltiesStatus.show(entity.status == 2)
            context?.let {
                ivPenaltiesModeAvatar.loadImage(it, entity.modelImg, 0)
                ivPenaltiesUserAvatar.loadImage(it, entity.userImg, 0)
            }

            tvPenaltiesSessionDetail.paint.flags = Paint.UNDERLINE_TEXT_FLAG
            tvPenaltiesSessionDetail.paint.isAntiAlias = true
            tvPenaltiesSessionDetail.clickWithTrigger {
                if (isShieldModel(entity.imid)) {
                    RouteUtils.routeToConversationActivity(
                        context, Conversation.ConversationType.PRIVATE, entity.imid
                    )
                } else {
                    ToastUtils.showToast(context.getString(R.string.penalties_removed))
                }
            }
        }
    }

    private fun getItemData(): PenaltyEntity? = data?.castObject<PenaltyEntity>()

    override fun getItemOffsets(
        recyclerView: RecyclerView,
        view: View,
        outRect: Rect,
        position: Int
    ): Boolean {
        val edge: Int = DeviceUtils.dp2px(context, 16.0f)
        outRect.bottom = edge
        return true
    }
}