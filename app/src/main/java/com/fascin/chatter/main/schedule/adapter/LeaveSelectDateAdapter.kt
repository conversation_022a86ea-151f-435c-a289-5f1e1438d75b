package com.fascin.chatter.main.schedule.adapter

import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.main.schedule.ScheduleDate
import com.iandroid.allclass.lib_common.beans.ComContentEntity
import com.iandroid.allclass.lib_common.utils.exts.textColorResource
import kotlinx.android.synthetic.main.itemview_leave_select_date.view.tvContent

/**
 * @Desc: 请假日期选择adapter
 * @Created: Quan
 * @Date: 2024/11/19
 */
class LeaveSelectDateAdapter : RecyclerView.Adapter<LeaveSelectDateAdapter.ViewHolder>() {

    var selectedPosition = 1 // 选中的item，默认选中position为1的
    private val dataList = mutableListOf<ComContentEntity>()

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

    fun updateData(data: List<ComContentEntity>) {
        dataList.clear()
        if (!data.isNullOrEmpty()) {
            dataList.addAll(data)
        }
        notifyDataSetChanged()
    }

    fun getSelectedItem(): ComContentEntity? {
        return if (selectedPosition != -1) dataList[selectedPosition] else null
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.itemview_leave_select_date, parent, false)
        )
    }

    override fun getItemCount(): Int = dataList.size

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = dataList[position]
        holder.itemView?.also {
            it.tvContent.text = ScheduleDate.getMDWStr(item.content)
            if (selectedPosition == position) {
                it.tvContent.textColorResource = com.iandroid.allclass.lib_common.R.color.cl_262626
                it.tvContent.setTextSize(TypedValue.COMPLEX_UNIT_SP, 17f)
            } else {
                it.tvContent.textColorResource = R.color.cr_bfbfbf
                it.tvContent.setTextSize(TypedValue.COMPLEX_UNIT_SP, 15f)
            }
        }
    }
}