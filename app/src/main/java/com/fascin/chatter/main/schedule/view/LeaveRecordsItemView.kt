package com.fascin.chatter.main.schedule.view

import android.content.Context
import android.graphics.Rect
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.bean.LeaveRecordEntity
import com.fascin.chatter.main.schedule.LeaveRecordCallback
import com.fascin.chatter.main.schedule.ScheduleConst
import com.fascin.chatter.main.schedule.ScheduleDate
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RvItem
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.castObject
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.toPx
import kotlinx.android.synthetic.main.itemview_leave_records.view.btnClick
import kotlinx.android.synthetic.main.itemview_leave_records.view.tvEndDate
import kotlinx.android.synthetic.main.itemview_leave_records.view.tvStartDate
import kotlinx.android.synthetic.main.itemview_leave_records.view.tvSubmitTime
import kotlinx.android.synthetic.main.itemview_leave_records.view.tvTitle

/**
 * @Desc:请假记录列表item
 * @Created: Quan
 * @Date: 2024/11/20
 */
@RvItem(id = AppViewType.leaveRecordsItemView, spanCount = 1)
class LeaveRecordsItemView(context: Context, fm: FragmentManager, parent: ViewGroup) :
    BaseRvItemView(context, fm, parent) {
    override fun attachLayoutId(): Int {
        return R.layout.itemview_leave_records
    }

    override fun initView(context: Context?, view: View?) {

    }

    override fun setView() {
        val entity = getItemData() ?: return
        itemView?.run {
            tvTitle.text = when (entity.status) {
                ScheduleConst.LEAVE_STATUS_NOT_START -> "Not started"
                ScheduleConst.LEAVE_STATUS_ONGOING -> "Ongoing"
                ScheduleConst.LEAVE_STATUS_CANCELED -> "Canceled"
                ScheduleConst.LEAVE_STATUS_ENDED, ScheduleConst.LEAVE_STATUS_STOP -> "Ended"
                else -> ""
            }
            tvStartDate.text = ScheduleDate.getMDWStrForYMD(entity.startDate)
            tvEndDate.text = ScheduleDate.getMDWStrForYMD(entity.endDate)
            tvSubmitTime.text = entity.submitTime
            when (entity.status) {
                ScheduleConst.LEAVE_STATUS_NOT_START -> {
                    btnClick.text = getStringById(com.iandroid.allclass.lib_basecore.R.string.cancel)
                    btnClick.show(true)
                }

                ScheduleConst.LEAVE_STATUS_ONGOING -> {
                    btnClick.text = getStringById(R.string.text_suspend)
                    btnClick.show(true)
                }

                else -> {
                    btnClick.show(false)
                }
            }
            btnClick.clickWithTrigger {
                btnCallBack()?.onItemClick(entity)
            }
        }
    }

    override fun getItemOffsets(parent: RecyclerView, view: View?, outRect: Rect, position: Int): Boolean {
        outRect.top = if (position == 0) 16.toPx else 0
        outRect.left = 16.toPx
        outRect.right = 16.toPx
        outRect.bottom = 16.toPx
        return true
    }

    private fun getItemData(): LeaveRecordEntity? = data?.castObject<LeaveRecordEntity>()

    private fun btnCallBack(): LeaveRecordCallback? = callBack?.castObject<LeaveRecordCallback>()
}