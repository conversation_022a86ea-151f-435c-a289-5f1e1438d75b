package com.fascin.chatter.main.profile.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import kotlinx.android.synthetic.main.itemview_improve_method.view.tvContent

/**
 * @Desc: 数据中心 Improve Method adapter
 * @Created: Quan
 * @Date: 2024/3/20
 */
class ImproveMethodAdapter : RecyclerView.Adapter<ImproveMethodAdapter.ViewHolder>() {

    private val dataList = mutableListOf<String>()

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

    fun updateData(data: List<String>?) {
        dataList.clear()
        if (!data.isNullOrEmpty()) {
            dataList.addAll(data)
        }
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.itemview_improve_method, parent, false)
        )

    }

    override fun getItemCount(): Int = dataList.size

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = dataList[position]
        holder.itemView.tvContent.text = item
    }
}