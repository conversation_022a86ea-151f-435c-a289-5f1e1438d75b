package com.fascin.chatter.main.course

import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.R
import com.fascin.chatter.bean.EmptyEntity
import com.fascin.chatter.main.HomeConfig
import com.fascin.chatter.main.IRvItemAction
import com.fascin.chatter.main.MainActivity
import com.iandroid.allclass.lib_basecore.base.BaseUiFragment
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemInfo
import com.iandroid.allclass.lib_basecore.view.recyclerview.PullBaseView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RecyclerViewSupport
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.beans.EventCourseChange
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.exts.castObject
import com.iandroid.allclass.lib_common.utils.exts.statusBarHeightForImmersive
import kotlinx.android.synthetic.main.fragment_course_sort.courseSortRv
import kotlinx.android.synthetic.main.fragment_course_sort.rl_statusBar
import kotlinx.android.synthetic.main.layout_title_bar.titleBarTitle

/**
 * @Desc:课程分类列表页
 * @Created: Quan
 * @Date: 2024/3/13
 */
class CourseSortFragment : BaseUiFragment(), IRvItemAction {

    private var viewModel: CourseViewModel? = null
    private var recyclerViewSupport: RecyclerViewSupport? = null

    override fun attachLayoutId(): Int {
        return R.layout.fragment_course_sort
    }

    override fun initView(view: View?) {
        super.initView(view)
        initTitleBar()
        initRv()
        // 设置骨骼视图
        setPreView()
        setListener()
        getData()
    }

    private fun initTitleBar() {
        rl_statusBar.statusBarHeightForImmersive(requireContext())
        titleBarTitle.text = getText(R.string.main_tab_course)
    }

    private fun initRv() {
        viewModel = ViewModelProvider(this).get(CourseViewModel::class.java)
        recyclerViewSupport = RecyclerViewSupport(childFragmentManager, courseSortRv, null).also {
            it.setCanPullDown(true)
            it.setCanPullUp(false)
            it.recyclerView.setOnRefreshListener(object : PullBaseView.OnRefreshListener {
                override fun onHeaderRefresh() {
                    getData()
                }

                override fun onFooterRefresh() {
                }
            })
        }
    }

    private fun setPreView() {
        updateData(
            ArrayList<BaseRvItemInfo?>().also {
                for (i in 1..8) {
                    it.add(BaseRvItemInfo(Any(), AppViewType.courseSortPlaceItemView, this))
                }
            })
    }

    private fun setListener() {
        viewModel?.courseSortResult?.observe(this) { sort ->
            recyclerViewSupport?.onHeaderRefreshComplete()
            recyclerViewSupport?.onFooterRefreshComplete()
            // 更新主页course tab 红点
            getMainPage()?.updateCourseRedDot(sort?.any { it.hasNew == 1 } == true)
            updateData(
                ArrayList<BaseRvItemInfo?>().also { list ->
                    sort?.forEach { course ->
                        list.add(BaseRvItemInfo(course, AppViewType.courseSortItemView, this))
                    }
                }
            )
        }

        viewModel?.courseSortError?.observe(this) {
            recyclerViewSupport?.onHeaderRefreshComplete()
            recyclerViewSupport?.onFooterRefreshComplete()
            addErrorView()
        }

        viewModel?.compositeDisposable?.add(
            SimpleRxBus.observe(EventCourseChange::class) {
                getData()
            }
        )
    }

    private fun getData() {
        HomeConfig.courseNeedRefresh = false
        viewModel?.getCoursesSort()
    }

    private fun updateData(itemTemp: ArrayList<BaseRvItemInfo?>) {
        if (itemTemp.isNotEmpty()) recyclerViewSupport?.updateData(itemTemp, true)
        else addEmptyView()
    }

    override fun onResume() {
        super.onResume()
        if (HomeConfig.courseNeedRefresh) getData()
    }

    private fun addEmptyView() {
        val emptyEntity = EmptyEntity().also {
            it.title = getString(R.string.page_data_tips)
            it.icRes = R.mipmap.ic_msg_setting_nodata
        }

        emptyEntity.also { data ->
            updateData(ArrayList<BaseRvItemInfo?>().also {
                it.add(BaseRvItemInfo(data, AppViewType.comEmptyView, this))
            })
        }
    }

    private fun addErrorView() {
        updateData(ArrayList<BaseRvItemInfo?>().also {
            it.add(BaseRvItemInfo(Any(), AppViewType.exceptionView, this))
        })
    }

    override fun startRefresh() {
        super.startRefresh()
        getData()
    }

    override fun onRefresh() {
        getData()
    }

    private fun getMainPage(): MainActivity? = context.castObject<MainActivity>()
}