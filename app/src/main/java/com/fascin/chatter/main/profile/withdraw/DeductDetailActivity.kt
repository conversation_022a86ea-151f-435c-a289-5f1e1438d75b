package com.fascin.chatter.main.profile.withdraw

import android.os.Bundle
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.iandroid.allclass.lib_common.base.ChatterBaseActivity
import com.iandroid.allclass.lib_common.beans.ProfitListBean
import com.iandroid.allclass.lib_common.utils.exts.show
import kotlinx.android.synthetic.main.activity_deduct_detail.llDeductReason
import kotlinx.android.synthetic.main.activity_deduct_detail.tvDeductAmount
import kotlinx.android.synthetic.main.activity_deduct_detail.tvDeductChatterId
import kotlinx.android.synthetic.main.activity_deduct_detail.tvDeductDate
import kotlinx.android.synthetic.main.activity_deduct_detail.tvDeductReason

/**
 * Created by: LXL
 * Date: 2024/10/10
 * Time: 14:09
 * 处罚扣减明细
 */
class DeductDetailActivity : ChatterBaseActivity() {
    private var profitInfoBean: ProfitListBean? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_deduct_detail)
    }

    override fun initBaseContent() {
        super.initBaseContent()
        AppModule.userActive()
        profitInfoBean = parseJsonParams<ProfitListBean>()

        profitInfoBean?.apply {
            setTitle(title)
            tvDeductChatterId.setRightText(cid)

            if (status == 4) {  //后台处罚
                tvDeductDate.setLeftText("Deduct Date")
                tvDeductDate.setRightText(createdAt)

                tvDeductAmount.setLeftText("Deduct")
                tvDeductAmount.setRightText("$moneySymbol $money")
            } else {  //撤销
                tvDeductDate.setLeftText("Date")
                tvDeductDate.setRightText(createdAt)

                tvDeductAmount.setLeftText("Amount")
                tvDeductAmount.setRightText("$moneySymbol $money")
            }
            details?.let {
                llDeductReason.show(it.reason.isNotEmpty())
                tvDeductReason.text = it.reason
            }
        }
    }
}