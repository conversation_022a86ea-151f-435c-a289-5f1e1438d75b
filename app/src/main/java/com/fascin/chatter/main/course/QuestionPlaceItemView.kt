package com.fascin.chatter.main.course

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.fascin.chatter.R
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RvItem
import com.iandroid.allclass.lib_common.AppViewType
import kotlinx.android.synthetic.main.itemview_question_place.view.shimmerLayout


/**
 * @Created: QuanZH
 * @Date: 2023/9/22
 */
@RvItem(id = AppViewType.questionPlaceItemView, spanCount = 1)
class QuestionPlaceItemView(context: Context, fm: FragmentManager, parent: ViewGroup) :
    BaseRvItemView(context, fm, parent) {

    override fun setView() {
        itemView?.run {
            shimmerLayout.startShimmerAnimation()
        }
    }

    override fun attachLayoutId(): Int {
        return R.layout.itemview_question_place
    }

    override fun initView(context: Context?, view: View?) {

    }
}