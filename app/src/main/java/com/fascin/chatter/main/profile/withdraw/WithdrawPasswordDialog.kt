package com.fascin.chatter.main.profile.withdraw

import android.content.Context
import android.os.Bundle
import android.text.InputType
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.R
import com.fascin.chatter.component.views.SUButtonStatus
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.base.BaseDialogFragment
import com.iandroid.allclass.lib_common.utils.ToastUtils
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.castObject
import kotlinx.android.synthetic.main.dialog_withdraw_password.btnWithdrawNext
import kotlinx.android.synthetic.main.dialog_withdraw_password.etEnterPw
import kotlinx.android.synthetic.main.dialog_withdraw_password.ivClosePw
import kotlinx.android.synthetic.main.dialog_withdraw_password.ivWithdrawPwToggle

/**
 * Created by: Keep
 * Date: 2024/10/12
 * Time: 12:28
 * 密码确认
 */
class WithdrawPasswordDialog : BaseDialogFragment {
    private lateinit var amount: String
    private lateinit var ciphertext: String
    private var onWithSuccess: (() -> Unit)? = null
    var isPasswordVisible = false

    private val viewModel by lazy {
        ViewModelProvider(this, WithdrawViewModel.ViewModeFactory())[WithdrawViewModel::class.java]
    }

    // 无参构造函数
    constructor() : super()

    // 带参数的构造函数
    constructor(amount: String, ciphertext: String, onWithSuccess: (() -> Unit)? = null) : super() {
        this.amount = amount
        this.ciphertext = ciphertext
        this.onWithSuccess = onWithSuccess
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return inflater.inflate(R.layout.dialog_withdraw_password, container, false)
    }

    override fun onStart() {
        super.onStart()
        setBottomPopupAttr(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        isCancelable = false
        etEnterPw.postDelayed({
            etEnterPw.requestFocus()
            val imm = context?.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            imm.showSoftInput(etEnterPw, InputMethodManager.SHOW_IMPLICIT)
        }, 150)

        ivClosePw.clickWithTrigger {
            dismissAllowingStateLoss()
        }
        btnWithdrawNext.setText("Next")
        btnWithdrawNext.setButtonStatus(SUButtonStatus.Activated)

        btnWithdrawNext.clickWithTrigger {
            if (etEnterPw.text.toString().isNotEmpty()) {
                btnWithdrawNext.setButtonStatus(SUButtonStatus.Loading)
                viewModel.chatterWithdraw(amount.toLong(), etEnterPw.text.toString(), ciphertext)
            } else {
                ToastUtils.showToast("Please enter your account password")
            }
        }
        viewModel.withdrawResult.observe(this) {
            if (it) {
                onWithSuccess?.invoke()
                dismissAllowingStateLoss()
            } else {
                btnWithdrawNext.setButtonStatus(SUButtonStatus.Activated)
            }
        }

        ivWithdrawPwToggle.setOnClickListener {
            if (isPasswordVisible) {
                etEnterPw.inputType = InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_VARIATION_PASSWORD
                ivWithdrawPwToggle.setImageResource(R.mipmap.ic_password_visible)
            } else {
                etEnterPw.inputType = InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD
                ivWithdrawPwToggle.setImageResource(R.mipmap.ic_password_hide)
            }
            etEnterPw.setSelection(etEnterPw.text?.length ?: 0)
            isPasswordVisible = !isPasswordVisible
        }
    }

    companion object {
        fun showWithdrawPasswordDialog(
            amount: String,
            ciphertext: String,
            onWithSuccess: (() -> Unit)? = null
        ) {
            val fragmentActivity = AppContext.getTopActivity()?.castObject<FragmentActivity>()
            fragmentActivity?.let {
                WithdrawPasswordDialog(amount, ciphertext, onWithSuccess).show(
                    it.supportFragmentManager, WithdrawPasswordDialog::class.java.name
                )
            }
        }
    }
}