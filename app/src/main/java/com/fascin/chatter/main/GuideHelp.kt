package com.fascin.chatter.main

import com.fascin.chatter.R
import com.fascin.chatter.bean.FeatureGuideEntity

/**
 * 引导页数据
 */
class GuideHelp {

    /**
     * 引导汇总页标题数据
     */
    private val featureTitles = arrayListOf(
        "1) PPV Page :  How To Send Private Content",
        "2) Chat Page:  Use Tools Effectively for Conversations",
        "3) Task Tab:  Two Ways to Make Money",
        "4) Chats Tab: Use Tools to Convert Quickly",
        "5) Me Tab:  Withdrawals"
    )


    /**
     * 首页chat引导页数据
     */
    private val chatTabGuides = arrayListOf(
        R.drawable.ic_guide_main_chat_1,
        R.drawable.ic_guide_main_chat_2,
        R.drawable.ic_guide_main_chat_3,
        R.drawable.ic_guide_main_chat_4,
        R.drawable.ic_guide_main_chat_5
    )

    /**
     * 首页chat 10个match后的引导页数据
     */
    val chatMatchGuides = arrayListOf(
        R.drawable.ic_guide_main_chat_match
    )

    /**
     * 首页chat引导汇总页数据
     */
    private val chatFeature = arrayListOf(
        "4.1 Your Models",
        "4.2 Colored Model Tags",
        "4.3 User Active Status",
        "4.4 Keywords: Shifts,Active,Level",
        "4.5 User Tags"
    )

    /**
     * 首页task的引导页数据
     */
    private val taskTabGuides = arrayListOf(
        R.drawable.ic_guide_main_task_1,
        R.drawable.ic_guide_main_task_2,
        R.drawable.ic_guide_main_task_3,
        R.drawable.ic_guide_main_task_4
    )

    /**
     * task引导汇总页数据
     */
    private val taskFeature = arrayListOf(
        "3.1 Task",
        "3.2 Activity Entrance",
        "3.3 All Hosts Daily Ranking",
        "3.4 History Task"
    )

    /**
     * 首页Me的引导页数据
     */
    private
    val meTabGuides = arrayListOf(
        R.drawable.ic_guide_main_me_1,
        R.drawable.ic_guide_main_me_2
    )

    /**
     * 首页me首次展示添加model按钮的引导页数据
     */
    val meTabAddGuides = arrayListOf(
        R.drawable.ic_guide_main_me_add
    )

    /**
     * me引导汇总页数据
     */
    private val meFeature = arrayListOf(
        "5.1 MPC Center",
        "5.2 Your Wallet",
        "5.3 Add Models"
    )

    /**
     * 聊天页的引导页数据
     */
    private val imGuides = arrayListOf(
        R.drawable.ic_guide_im_1,
        R.drawable.ic_guide_im_2,
        R.drawable.ic_guide_im_3,
        R.drawable.ic_guide_im_4
    )

    /**
     * 聊天页Fc按钮首次显示的引导页数据
     */
    private val imFcGuides = arrayListOf(
        R.drawable.ic_guide_im_fc
    )

    /**
     * 聊天页首次展示Nearby入口的引导页数据
     */
    private val imNearbyGuides = arrayListOf(
        R.drawable.ic_guide_im_nearby
    )

    /**
     * im引导汇总页数据
     */
    private val imFeature = arrayListOf(
        "2.1 Public,PPVs,Emoji,Msg",
        "2.2 Chat Level",
        "2.3 User Location",
        "2.4 User Local Time",
        "2.5 Instant Chat",
        "2.6 Nearby Tag",
    )

    /**
     * 私密相册的引导页数据
     */
    val privacyGuides = arrayListOf(
        R.drawable.ic_guide_privacy_list_1,
        R.drawable.ic_guide_privacy_list_2,
        R.drawable.ic_guide_privacy_list_3,
        R.drawable.ic_guide_privacy_list_4
    )

    /**
     * ppv引导汇总页数据
     */
    private val ppvFeature = arrayListOf(
        "1.1 Private Type",
        "2.2 Select one or more PPVs",
        "2.3 Unlocked Tag",
        "4.4 Send Tag"
    )

    fun getMainGuides(tabId: Int): List<Int> {
        return when (tabId) {
            HomeConfig.tabIndexChats -> {
                chatTabGuides
            }

            HomeConfig.tabIndexTask -> {
                taskTabGuides
            }

            HomeConfig.tabIndexMe -> {
                meTabGuides
            }

            else -> emptyList()
        }
    }

    fun getImGuides(type: Int): List<Int> {
        return when (type) {
            0 -> {
                imGuides
            }

            1 -> {
                imFcGuides
            }

            2 -> {
                imNearbyGuides
            }

            else -> emptyList()
        }
    }

    /**
     * 获取引导汇总页数据
     */
    fun getFeatureGuideData(): List<FeatureGuideEntity> {
        val featureMap = mapOf(
            0 to ppvFeature,
            1 to imFeature,
            2 to taskFeature,
            3 to chatFeature,
            4 to meFeature
        )
        val picMap = mapOf(
            0 to privacyGuides,
            1 to imGuides,
            2 to taskTabGuides,
            3 to chatTabGuides,
            4 to meTabGuides
        )
        val featureGuides = arrayListOf<FeatureGuideEntity>()
        featureTitles.forEachIndexed { index, title ->
            featureMap[index]?.forEachIndexed { position, content ->
                featureGuides.add(
                    FeatureGuideEntity().apply {
                        if (position == 0) this.title = title
                        this.content = content
                        this.picRes =
                                // 聊天页的引导页FC
                            if (index == 1 && position == 4) imFcGuides[0]
                            // 聊天页的引导页nearby
                            else if (index == 1 && position == 5) imNearbyGuides[0]
                            // 会话列表页的引导页10次match引导页
                            else if (index == 3 && position == 4) chatMatchGuides[0]
                            // me页的引导页add
                            else if (index == 4 && position == 2) meTabAddGuides[0]
                            else picMap[index]?.get(position) ?: 0
                    }
                )
            }
        }
        return featureGuides
    }
}