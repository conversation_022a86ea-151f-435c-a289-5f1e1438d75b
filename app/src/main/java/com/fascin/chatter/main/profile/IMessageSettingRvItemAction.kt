package com.fascin.chatter.main.profile

import com.fascin.chatter.main.IRvItemAction

/**
 * @Desc:MessageSetting Rv 相关接口
 * @Created: QuanZH
 * @Date: 2023/7/13
 */
interface IMessageSettingRvItemAction : IRvItemAction {

    /**
     * 新增问候语/消息
     */
    fun addMessage(userID: String, nickName: String)

    /**
     * 删除问候语/消息
     */
    fun delMessage(msgID: Int, userID: String)

    /**
     * 修改问候语/消息
     */
    fun modifyMessage(msgID: Int, msgContent: String, userID: String, nickName: String)
}