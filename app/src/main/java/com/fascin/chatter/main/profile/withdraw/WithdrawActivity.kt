package com.fascin.chatter.main.profile.withdraw

import android.os.Bundle
import android.text.Editable
import android.text.InputFilter
import android.text.TextWatcher
import com.daimajia.androidanimations.library.Techniques
import com.daimajia.androidanimations.library.YoYo
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.iandroid.allclass.lib_common.base.ChatterBaseActivity
import com.iandroid.allclass.lib_common.beans.PropertyInfoEntity
import com.iandroid.allclass.lib_common.beans.WebIntent
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.getColorEx
import com.iandroid.allclass.lib_common.utils.exts.immersiveStatusBarWithMargin
import kotlinx.android.synthetic.main.activity_withdraw.btnApplyWithdraw
import kotlinx.android.synthetic.main.activity_withdraw.cl_receving_account
import kotlinx.android.synthetic.main.activity_withdraw.etWithdrawAmount
import kotlinx.android.synthetic.main.activity_withdraw.tvWithdrawAll
import kotlinx.android.synthetic.main.activity_withdraw.tvWithdrawCurrency
import kotlinx.android.synthetic.main.activity_withdraw.tvWithdrawCurrentBalance
import kotlinx.android.synthetic.main.activity_withdraw.tvWithdrawRules
import kotlinx.android.synthetic.main.activity_withdraw.tv_receving_account
import kotlinx.android.synthetic.main.activity_withdraw.withdrawBack
import kotlinx.android.synthetic.main.activity_withdraw.withdrawToolbar

/**
 * Created by: LXL
 * Date: 2024/10/10
 * Time: 14:09
 * 提现
 */
class WithdrawActivity : ChatterBaseActivity() {
    private var propertyInfoEntity: PropertyInfoEntity? = null
    private var isInputAmount = false  //输入金额是否正确
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_withdraw)
    }

    override fun initBaseContent() {
        super.initBaseContent()
        AppModule.userActive()
        immersiveStatusBarWithMargin(withdrawToolbar)
        clickListener()
        propertyInfoEntity = parseJsonParams<PropertyInfoEntity>()

        propertyInfoEntity?.apply {
            cl_receving_account.isEnabled = from == 0
            tv_receving_account.text = this.receivingAccount
            tvWithdrawCurrency.text = this.currency
            tvWithdrawCurrentBalance.text = "Current balance: ${this.currency}${this.balance}"
            withdrawAmountEditText(this)
            tvWithdrawRules.text = this.ruleContent
        }
    }

    private fun clickListener() {
        //收款账户
        cl_receving_account.clickWithTrigger {
            routeAction(ActionType.actionTypeToWebActivity) {
                it.param = WebIntent().also { webIntent ->
                    webIntent.showTitle = true
                    webIntent.url = propertyInfoEntity?.payrollUrl
                }
            }
        }

        //提现
        btnApplyWithdraw.clickWithTrigger {
            if (isValidInput(etWithdrawAmount.text.toString()) && isInputAmount) {
                WithdrawPasswordDialog.showWithdrawPasswordDialog(
                    etWithdrawAmount.text.toString(),
                    propertyInfoEntity?.ciphertext.orEmpty()
                ) {
                    finish()
                }
            } else {
                if (isInputAmount) {
                    tvWithdrawCurrentBalance.setTextColor(getColorEx(com.iandroid.allclass.lib_common.R.color.color_F5222D))
                    tvWithdrawCurrentBalance.text = propertyInfoEntity!!.multipleErrStr
                }
                YoYo.with(Techniques.Shake)
                    .duration(500)
                    .repeat(1)
                    .playOn(tvWithdrawCurrentBalance)
            }
        }

        //全部提现金额
        tvWithdrawAll.clickWithTrigger {
            propertyInfoEntity?.apply {
                etWithdrawAmount.setText(balance.toPlainString())
                etWithdrawAmount.setSelection(balance.toPlainString().length)
            }
        }
        withdrawBack.setOnClickListener {
            finish()
        }
    }

    private fun withdrawAmountEditText(propertyInfo: PropertyInfoEntity) {
        // 首字符不能是. 和 01 这种
        val filter = InputFilter { source, start, end, dest, dstart, dend ->
            val result = dest.toString() + source.toString()
            if (result.matches(Regex("^(0(\\.[0-9]*)?|[1-9][0-9]*\\.?[0-9]*)$")) && !result.startsWith(".")) {
                null // 输入合法，允许输入
            } else {
                "" // 输入不合法，阻止输入
            }
        }
        etWithdrawAmount.filters = arrayOf(filter)


        etWithdrawAmount.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

            override fun afterTextChanged(s: Editable?) {
                if (s.toString().isNotEmpty()) {
                    val amount = s.toString().toDouble()
                    if (amount > propertyInfo.balance.toDouble()) {
                        isInputAmount = false
                        tvWithdrawCurrentBalance.text = "Exceeding current balance"
                        tvWithdrawCurrentBalance.setTextColor(getColorEx(com.iandroid.allclass.lib_common.R.color.color_F5222D))
                    } else if (amount < propertyInfo.minAmount) {
                        isInputAmount = false
                        tvWithdrawCurrentBalance.text =
                            "At least enter ${propertyInfo.currency}${propertyInfo.minAmount}"
                        tvWithdrawCurrentBalance.setTextColor(getColorEx(com.iandroid.allclass.lib_common.R.color.color_F5222D))
                    } else {
                        isInputAmount = true
                        tvWithdrawCurrentBalance.text =
                            "Current balance: ${propertyInfo.currency}${propertyInfo.balance}"
                        tvWithdrawCurrentBalance.setTextColor(getColorEx(com.iandroid.allclass.lib_basecore.R.color.cr_000000_60))
                    }
                } else {
                    isInputAmount = false
                    tvWithdrawCurrentBalance.text =
                        "Current balance: ${propertyInfo.currency}${propertyInfo.balance}"
                    tvWithdrawCurrentBalance.setTextColor(getColorEx(com.iandroid.allclass.lib_basecore.R.color.cr_000000_60))
                }
            }
        })
    }

    /**
     * 提现金额检测
     * 1. 菲律宾：110，仅支持10的倍数
     * 2. 非洲：1500，仅支持100的倍数
     * 2024/12/23 改成后端动态配置
     */
    private fun isValidInput(input: String): Boolean {
        val value = input.toIntOrNull() ?: return false
        return value >= propertyInfoEntity!!.minAmount && value % propertyInfoEntity!!.multiple == 0
    }

    override fun immersiveIntoStatusBar(): Boolean {
        return true
    }

    override fun isShowTitleBar(): Boolean {
        return false
    }
}