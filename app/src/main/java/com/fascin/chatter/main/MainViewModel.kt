package com.fascin.chatter.main

import android.util.Log
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.bean.AppGuideEntity
import com.fascin.chatter.bean.GoalsEntity
import com.fascin.chatter.bean.RevitalizeEntity
import com.fascin.chatter.bean.TodayLeaveEntity
import com.fascin.chatter.repository.AppRepository
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.UserController
import com.iandroid.allclass.lib_common.base.BaseViewModel
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.utils.ToastUtils

/**
created by wangkm
on 2020/9/12.
 */
class MainViewModel : BaseViewModel() {

    val hasMpcNoticeResult = MutableLiveData<Boolean>()
    val hasGoalsResult = MutableLiveData<GoalsEntity>()
    val hasGoalsError = MutableLiveData<Any>()
    val revitalizeListResult = MutableLiveData<RevitalizeEntity>()
    val revitalizeListError = MutableLiveData<Any>()

    val revitalizeSendResult = MutableLiveData<Boolean>()
    val revitalizeSendError = MutableLiveData<Any>()
    val checkShowRepossessionResult = MutableLiveData<Boolean>()

    val checkTodayLeaveResult = MutableLiveData<TodayLeaveEntity>()

    val checkGuideResult = MutableLiveData<AppGuideEntity>()
    val uploadAppInfoResult = MutableLiveData<Boolean>()

    /**
     *  MPC更新气泡通知检查
     */
    fun checkHasMpcNotice() {
        compositeDisposable?.add(
            AppRepository.checkHasMpcNotice().subscribe({
                hasMpcNoticeResult.postValue(it.updateTime > 0)
            }, {
                hasMpcNoticeResult.postValue(false)
            })
        )
    }

    /**
     * 试岗优化弹窗是否显示
     */
    fun checkHasGoals() {
        compositeDisposable?.add(
            AppRepository.checkHasGoals().subscribe({
                hasGoalsResult.postValue(it)
            }, {
                hasGoalsError.postValue(it)
            })
        )
    }

    /**
     * 获取所有未读公告
     */
    fun getAnnouncement() {
        compositeDisposable?.add(
            AppRepository.getAnnouncement().subscribe({ entity ->
                if (!entity.isNullOrEmpty() && entity.size > 0) {
                    AppContext.getTopActivity().routeAction(ActionType.actionNoticeDialog) {
                        it.param = entity[0]
                    }
                }
            }, {
            })
        )
    }

    /**
     * 获取VIP老用户召回数据列表
     */
    fun getRevitalizeList() {
        compositeDisposable?.add(
            AppRepository.getRevitalizeData().subscribe({
                revitalizeListResult.postValue(it)
            }, {
                ToastUtils.showToast(it.message)
                revitalizeListError.postValue(it)
            })
        )
    }

    /**
     *  VIP老用户召回消息发送
     */
    fun revitalizeSendMsg(ids: String, msg: String) {
        compositeDisposable?.add(
            AppRepository.sentRevitalizeMsg(ids, msg).subscribe({
                revitalizeSendResult.postValue(true)
            }, {
                ToastUtils.showToast(it.message)
                revitalizeListError.postValue(it)
            })
        )
    }

    /**
     * 检查是否需要弹回收预警弹窗
     */
    fun checkShowRepossession() {
        compositeDisposable?.add(
            AppRepository.checkShowRepossession().subscribe({
                checkShowRepossessionResult.postValue(it?.showNotice == 1)
            }, {
                ToastUtils.showToast(it.message)
                checkShowRepossessionResult.postValue(false)
            })
        )
    }

    /**
     * 检查今日是否已经请假
     */
    fun checkTodayLeave() {
        compositeDisposable?.add(
            AppRepository.checkTodayLeave().subscribe({
                checkTodayLeaveResult.postValue(it)
            }, {

            })
        )
    }

    /**
     * 获取引导页配置
     */
    fun checkAppGuidePage() {
        compositeDisposable?.add(
            AppRepository.checkAppGuidePage().subscribe({
                checkGuideResult.postValue(it)
            }, {

            })
        )
    }

    /**
     * 上传应用列表数据
     */
    fun uploadAppListInfo(appListInfoJson: String) {
        Log.e("uploadAppListInfo", appListInfoJson)
        compositeDisposable?.add(
            AppRepository.uploadAppListInfo(UserController.getUserId(), appListInfoJson).subscribe({
                uploadAppInfoResult.postValue(true)
            }, {
                uploadAppInfoResult.postValue(false)
            })
        )
    }

    class ViewModeFactory : ViewModelProvider.Factory {
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            return MainViewModel() as T
        }
    }
}