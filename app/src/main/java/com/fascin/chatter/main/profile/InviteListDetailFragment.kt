package com.fascin.chatter.main.profile

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.bean.InviteHistoryEntity
import com.fascin.chatter.databinding.FragmentInviteListDetailBinding
import com.fascin.chatter.databinding.ItemInviteActivatedBinding
import com.fascin.chatter.databinding.ItemInviteQualifiedBinding
import com.fascin.chatter.main.profile.invite.InviteFriendsViewModel

class InviteListDetailFragment : Fragment(R.layout.fragment_invite_list_detail) {

    companion object {
        private const val TYPE_KEY = "type"

        fun newInstance(type: InvitePager): InviteListDetailFragment {
            return InviteListDetailFragment().apply {
                arguments = Bundle().apply {
                    putSerializable(TYPE_KEY, type)
                }
            }
        }
    }

    private val viewModel: InviteFriendsViewModel by viewModels(ownerProducer = { requireActivity() })

    private lateinit var type: InvitePager
    private lateinit var detailAdapter: DetailAdapter

    private var binding: FragmentInviteListDetailBinding? = null


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding = FragmentInviteListDetailBinding.bind(view)
        type = arguments?.getSerializable(TYPE_KEY) as? InvitePager ?: throw IllegalArgumentException("type is null")

        initRv()
        initData()
    }

    private fun initRv() {
        val allDetail = viewModel.inviteFriendResult.value
        val datas = when(type) {
            InvitePager.Activated -> allDetail?.activatedList
            InvitePager.Qualified -> allDetail?.qualifiedList
        } ?: emptyList()

        detailAdapter = DetailAdapter(type, datas)

        binding?.rvDetail?.apply {
            adapter = detailAdapter
            layoutManager = LinearLayoutManager(context)
        }
    }

    private fun initData() {
        viewModel.inviteFriendResult.observe(viewLifecycleOwner) {
            if (this::detailAdapter.isInitialized) {
                detailAdapter.update(
                    when(type) {
                        InvitePager.Activated -> it.activatedList
                        InvitePager.Qualified -> it.qualifiedList
                    }
                )
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }

    private class DetailAdapter(
        private val pager: InvitePager,
        data: List<InviteHistoryEntity>
    ) : RecyclerView.Adapter<DetailViewHolder>() {

        private val data = data.toMutableList()

        fun update(data: List<InviteHistoryEntity>) {
            this.data.clear()
            this.data.addAll(data)
            notifyDataSetChanged()
        }

        override fun onCreateViewHolder(
            parent: ViewGroup,
            viewType: Int
        ): DetailViewHolder {
            val layoutInflater = LayoutInflater.from(parent.context)

            return when(pager) {
                InvitePager.Activated -> ActivatedViewHolder(layoutInflater.inflate(R.layout.item_invite_activated, parent, false))
                InvitePager.Qualified -> QualifiedViewHolder(layoutInflater.inflate(R.layout.item_invite_qualified, parent, false))
            }
        }

        override fun getItemViewType(position: Int): Int {
            return when(pager) {
                InvitePager.Activated -> ActivatedViewHolder.TYPE_ACTIVATED
                InvitePager.Qualified -> QualifiedViewHolder.TYPE_QUALIFIED
            }
        }

        override fun onBindViewHolder(
            holder: DetailViewHolder,
            position: Int
        ) {
            if (position == 0) {
                holder.onBindTitle()
            } else {
                holder.onBind(data[position - 1])
            }
        }

        override fun getItemCount(): Int = data.size + 1 //1为表示header
    }


    sealed class DetailViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        abstract fun onBindTitle()

        abstract fun onBind(entity: InviteHistoryEntity)
    }

    private class ActivatedViewHolder(itemView: View) : DetailViewHolder(itemView) {
        private val binding: ItemInviteActivatedBinding = ItemInviteActivatedBinding.bind(itemView)

        override fun onBindTitle() {
            binding.tvCid.text = "CID"
            binding.tvStatus.text = "Status"
        }

        override fun onBind(entity: InviteHistoryEntity) {
            binding.tvCid.text = entity.id
            binding.tvStatus.text = "Activated"
        }


        companion object {
            const val TYPE_ACTIVATED = 0
        }
    }

    private class QualifiedViewHolder(itemView: View) : DetailViewHolder(itemView) {
        private val binding: ItemInviteQualifiedBinding = ItemInviteQualifiedBinding.bind(itemView)

        override fun onBindTitle() {
            binding.tvCid.text = "CID"
            binding.tvStatus.text = "Status"
            binding.tvBonus.text = "Bonus"
        }

        override fun onBind(entity: InviteHistoryEntity) {
            binding.tvCid.text = entity.id
            binding.tvStatus.text = "Qualified"
            binding.tvBonus.text = entity.money
        }

        companion object {
            const val TYPE_QUALIFIED = 1
        }
    }
}