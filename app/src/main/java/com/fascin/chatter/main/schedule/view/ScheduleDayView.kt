package com.fascin.chatter.main.schedule.view

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import com.fascin.chatter.R
import com.iandroid.allclass.lib_common.utils.exts.show
import kotlinx.android.synthetic.main.view_schedule_day.view.curBg
import kotlinx.android.synthetic.main.view_schedule_day.view.tvDay
import kotlinx.android.synthetic.main.view_schedule_day.view.tvWeek

/**
 * @Desc: 周对应的日item
 * @Created: Quan
 * @Date: 2024/11/18
 */
class ScheduleDayView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    init {
        View.inflate(context, R.layout.view_schedule_day, this)
    }

    fun setViewData(weekDay: String?, dayDate: String, isToDay: <PERSON>ole<PERSON>) {
        tvWeek.text = weekDay
        tvDay.text = dayDate
        curBg.show(isToDay)
    }
}