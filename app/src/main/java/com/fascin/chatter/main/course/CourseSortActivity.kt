package com.fascin.chatter.main.course

import android.R.attr.fragment
import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.FragmentTransaction
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.iandroid.allclass.lib_common.base.ChatterBaseActivity


/**
 * @Desc: 课程列表页
 * @Created: Quan
 * @Date: 2023/9/20
 */
class CourseSortActivity : ChatterBaseActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_course_sort)

        val t: FragmentTransaction = supportFragmentManager.beginTransaction()
        t.replace(R.id.ll_fragment, CourseSortFragment())
        t.commit()
    }



    override fun initBaseContent() {
        super.initBaseContent()
        AppModule.userActive()
        setTitle("Course")
    }
}