package com.fascin.chatter.main

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.bean.GlobalNotifyEntity
import com.fascin.chatter.bean.event.UIToMPCEvent
import com.fascin.chatter.databinding.ActivityMainBinding
import com.fascin.chatter.databinding.ItemMainTabBinding
import com.fascin.chatter.im.UserOnlineHelper
import com.fascin.chatter.main.profile.ProfileFragment
import com.fascin.chatter.repository.AppRepository
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.iandroid.allclass.lib_basecore.base.BaseActivity
import com.iandroid.allclass.lib_basecore.base.BaseFragment
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.AppController
import com.iandroid.allclass.lib_common.UserController
import com.iandroid.allclass.lib_common.Values
import com.iandroid.allclass.lib_common.beans.AppItemInfo
import com.iandroid.allclass.lib_common.beans.EventTaskFragment
import com.iandroid.allclass.lib_common.beans.UIEventAppUpdate
import com.iandroid.allclass.lib_common.beans.UIEventSyncUnreadCount
import com.iandroid.allclass.lib_common.beans.event.UIAppForegroundEvent
import com.iandroid.allclass.lib_common.download.UpdateManager
import com.iandroid.allclass.lib_common.event.ConfigGettedEvent
import com.iandroid.allclass.lib_common.repository.CommonRepository
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.bean.ActionEntity
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.AnimationUtil
import com.iandroid.allclass.lib_common.utils.DoubleUtils
import com.iandroid.allclass.lib_common.utils.GsonUtils
import com.iandroid.allclass.lib_common.utils.SPUtils
import com.iandroid.allclass.lib_common.utils.SystemUtils
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.castObject
import com.iandroid.allclass.lib_common.utils.exts.getToTime
import com.iandroid.allclass.lib_common.utils.exts.isSameDay
import com.iandroid.allclass.lib_common.utils.exts.resetViewPagerRv2Id
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.startHomeActivity
import com.iandroid.allclass.lib_common.utils.exts.toJsonString
import com.iandroid.allclass.lib_common.views.CommonAlertDialog
import com.iandroid.allclass.lib_thirdparty.push.PushControl
import io.rong.imkit.RongIM
import io.rong.imkit.manager.UnReadMessageManager
import io.rong.imlib.IRongCoreCallback
import io.rong.imlib.IRongCoreEnum.CoreErrorCode
import io.rong.imlib.RongCoreClient
import io.rong.imlib.model.Conversation
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class MainActivity : BaseActivity(), UnReadMessageManager.IUnReadMessageObserver {

    private lateinit var binding: ActivityMainBinding
    var selectTime: Long = 0
    var newChatNum: Int = 0
    var mpcChecking = false
    var guideChecked = false
    var mpcLastNoticeDay = 0L
    var hasSysNotice = false
    var hasMpcNotice = false
    var currIndex = HomeConfig.tabIndexChats

    private val viewModel by lazy {
        ViewModelProvider(
            this,
            MainViewModel.ViewModeFactory()
        )[MainViewModel::class.java]
    }

    private val tabIcons = listOf(
        R.drawable.ic_tab_chats_selector,
        R.drawable.ic_tab_task_selector,
//        R.drawable.ic_tab_course_selector,
        R.drawable.ic_tab_profile_selector
    )

    private val tabNames = listOf(
        R.string.main_tab_chats,
        R.string.main_tab_task,
//        R.string.main_tab_course,
        R.string.main_tab_profile,
    )

    private val fragmentPagerAdapter by lazy {
        FragmentPageAdapter(
            supportFragmentManager,
            lifecycle
        )
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initViewPager2()
        initTabLayout()

        //登录im服务器
        AppController.userLogin()

        // 启动获取上线数据轮询
        if (AppController.systemConfigOk) {
            UserOnlineHelper.pollQueryOnlineUser()
        }

        RongIM.getInstance().addUnReadMessageCountChangedObserver(
            this,
            Conversation.ConversationType.PRIVATE
        )
        postDelayed({ PushControl.startRegisterPushWorker() }, 5000)
        postDelayed({
            parseSchemeProtocol(intent)
            PushControl.parsePushPendingIntent(this, intent)
        }, 300)

        postDelayed({ AppRepository.fetchAppUpgradeInfo() }, 800)

        // 1分钟后检测是否弹试岗弹窗
//        postDelayed({ checkGoals() }, 60 * 1000)

        // 5分钟后，检查账号是否有回收预警
        postDelayed({
            checkRepossession()
        }, 5 * 60 * 1000)

        checkTodayLeave()
        uploadAppList()
        viewModel?.getAnnouncement()

        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(UIEventAppUpdate::class) {
            updateMeRedDot(fromSysNotify = false)
        })

        // 配置接口请求完成
        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(ConfigGettedEvent::class) {
            // 启动上线轮询器
            UserOnlineHelper.pollQueryOnlineUser()

        })

        // 后台切换到前台
        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(UIAppForegroundEvent::class) {
            // 检查上线轮询器
            UserOnlineHelper.restartPolling()
        })

        // 配置变更推送
        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(GlobalNotifyEntity::class) {
            if (it.onlineQueryInterval > 0) {
                // 立刻重置轮询
                UserOnlineHelper.restartPolling(true)
            }
        })
        // 点击我知道了跳转到任务Fragment
        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(EventTaskFragment::class) {
            binding.mainViewPager.setCurrentItem(1, false)
            AppContext.finishAllActivityExcept(MainActivity::class.java)
        })
        // 点击试岗弹窗/账号回收预警跳转到MPC页面
        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(UIToMPCEvent::class) {
            routeAction(ActionType.actionMPC)
            mpcViewReset(false)
        })

        AppModule.startUserActive()
        binding.mainActiveTouchView.setOnTouchListener { v, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    AppModule.userActive()
                }
            }
            false
        }

        //设置消息免打扰同步消息数量
        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(UIEventSyncUnreadCount::class) {
            // 未做分页原因，暂时不使用融云的回调
//            val conversationTypes = arrayOf(Conversation.ConversationType.PRIVATE)
//            RongIM.getInstance().getUnreadCount(conversationTypes, false, object : RongIMClient.ResultCallback<Int>() {
//                override fun onSuccess(result: Int?) {
//                    newChatNum = result ?: 0
//                    updateChatNum()
//                }
//
//                override fun onError(errorCode: RongIMClient.ErrorCode?) {
//
//                }
//            })
        })

        viewModel?.hasMpcNoticeResult?.observe(this) {
            // mpc有更新 //TODO  关闭mpc通知 by mask
            /*if (it) {
                mpcBubbleNotice()
                mpcViewReset(true)
            }
            mpcChecking = false*/
        }

//        viewModel?.hasGoalsResult?.observe(this) {
//            goalsGoalsNotice(it)
//        }

        viewModel?.checkShowRepossessionResult?.observe(this) {
            if (it) routeAction(ActionType.actionRepossessionDialog)
        }

        viewModel?.checkTodayLeaveResult?.observe(this) {
            // 今日请假时，弹取消请假弹窗
            if (it.hasLeave) showCancelLeaveDialog()
        }

        viewModel?.checkGuideResult?.observe(this) {
            HomeConfig.guideConfig = it
            showGuideView(currIndex)
        }

        viewModel?.uploadAppInfoResult?.observe(this) {
            SPUtils.put(AppContext.context, Values.keyCheckAppList, System.currentTimeMillis())
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode === UpdateManager.UPDATE_REINSTALL_CODE) {
            UpdateManager.reInstallApk()
        }
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        PushControl.parsePushPendingIntent(this, intent)
        parseSchemeProtocol(intent)
        fasNotifyClick(intent)
    }

    private fun initViewPager2() = with(binding.mainViewPager) {
        resetViewPagerRv2Id(binding.mainViewPager)
        adapter = fragmentPagerAdapter
        offscreenPageLimit = fragmentPagerAdapter.itemCount
        isUserInputEnabled = false
        registerOnPageChangeCallback(object : OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
            }
        })
    }

    private fun initTabLayout() = with(binding.mainTabHost) {
        repeat(fragmentPagerAdapter.itemCount) {
            addTab(newTab())
        }
        TabLayoutMediator(this, binding.mainViewPager, true, true) { tab, position ->
            tab.id = when (position) {
                0 -> HomeConfig.tabIndexChats
                1 -> HomeConfig.tabIndexTask
//                2 -> HomeConfig.tabIndexCourse
                2 -> HomeConfig.tabIndexMe
                else -> -1
            }
            ItemMainTabBinding.inflate(layoutInflater).apply {
                mainTabIcon.setImageResource(tabIcons[position])
                mainTabName.setText(tabNames[position])
                mainTabName.setTextColor(
                    ContextCompat.getColorStateList(
                        context,
                        R.color.text_color_selector
                    )
                )
                tab.customView = this.root
            }
            tab.view.setOnClickListener {
                binding.mainViewPager.setCurrentItem(position, false)
            }
        }.attach()

        binding.mainTabHost.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                currIndex = tab.id
                updateTabSelect(tab, true)
                onSelectTab(tab, false)
                showGuideView(currIndex)
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
                updateTabSelect(tab, false)
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
                if (!DoubleUtils.isFastDoubleClick()) {
                    onSelectTab(tab, true)
                }
            }
        })

        tabMode = TabLayout.MODE_FIXED
        tabGravity = TabLayout.GRAVITY_FILL
    }

    private fun parseSchemeProtocol(intent: Intent?) {
        intent?.data?.run {
            routeAction(this)
        }
    }

    /**
     * 在后台时，自定义站外通知点击（FasMsgEx内的sys那些）
     */
    private fun fasNotifyClick(intent: Intent?) {
        val pushString = intent?.getStringExtra(Values.intentJsonParam) ?: return
        routeAction(GsonUtils.fromJson(pushString, ActionEntity::class.java))
    }

    private fun onSelectTab(tab: TabLayout.Tab?, isReselect: Boolean) {
        if (tab == null) return

        if (!isReselect) {
            selectTime = System.currentTimeMillis()
        } else {
            if (System.currentTimeMillis() - selectTime > 10000)
                for (i in 0 until binding.mainTabHost.tabCount) {
                    binding.mainTabHost.getTabAt(i)?.takeIf { it == tab }?.view?.also {
                        fragmentPagerAdapter.getFragment(i)?.castObject<BaseFragment>()?.also {
                            mHandler.postDelayed({
                                it.startRefresh()
                            }, 150)
                        }
                        return
                    }
                }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        RongIM.getInstance().removeUnReadMessageCountChangedObserver(this)
        UserOnlineHelper.stopPolling()
    }


    override fun isShowTitleBar(): Boolean {
        return false
    }

    override fun immersiveIntoStatusBar(): Boolean {
        return true
    }

    override fun onBackPressed() {
        startHomeActivity()
    }

    /**
     * 新消息变化
     */
    override fun onCountChanged(count: Int) {
        // 未做分页原因，暂时不使用融云的回调
//        newChatNum = count
//        updateChatNum()
    }

    /**
     * 会话列表新消息变化
     */
    fun onMsgCountChanged(count: Int) {
        newChatNum = count
        updateChatNum()
    }

    private fun updateChatNum() {
        val msgTotal = newChatNum
        binding.mainTabHost.getTabAt(HomeConfig.tabIndexChats)?.view?.run {
            findViewById<View>(R.id.unreadView).show(msgTotal > 0)
            findViewById<AppCompatTextView>(R.id.unreadView).text =
                if (msgTotal > 99) "99+" else "$msgTotal"
        }
    }

    fun updateTabSelect(tab: TabLayout.Tab?, selected: Boolean) {
        for (i in 0 until binding.mainTabHost.tabCount) {
            binding.mainTabHost.getTabAt(i)?.takeIf { it == tab }?.view?.also {
                fragmentPagerAdapter.getFragment(i)?.castObject<BaseFragment>()?.also {
                    it.fragmentVisible(selected)
                }
                return
            }
        }
    }

    /**
     * 更新课程红点
     */
    fun updateCourseRedDot(show: Boolean) {
        //TODO close course by mask
//        binding.mainTabHost.getTabAt(HomeConfig.tabIndexCourse)?.view?.run {
//            findViewById<View>(R.id.unreadMinView).show(show)
//        }
    }

    /**
     * 更新Me SysNotice红点
     */
    fun updateMeRedDot(
        sysNoticeCount: Boolean = hasSysNotice,
        mpcNotice: Boolean = hasMpcNotice,
        fromSysNotify: Boolean
    ) {
        if (fromSysNotify) {
            hasSysNotice = sysNoticeCount
            hasMpcNotice = mpcNotice
            binding.mainTabHost.getTabAt(HomeConfig.tabIndexMe)?.view?.run {
                findViewById<View>(R.id.unreadMinView).show(sysNoticeCount || mpcNotice || AppController.isNeedShowUpdateRedDot())
            }
        } else {
            RongCoreClient.getInstance().getUnreadCount(
                Conversation.ConversationType.SYSTEM,
                AppController.getNoticeAccount(),
                object : IRongCoreCallback.ResultCallback<Int>() {
                    override fun onSuccess(integer: Int) {
                        binding.mainTabHost.post {
                            updateMeRedDot(integer > 0, fromSysNotify = true)
                        }
                    }

                    override fun onError(e: CoreErrorCode) {}
                })
        }
    }

    /**
     * MPC更新检查
     */
    private fun mpcShowCheck() {
        if (mpcChecking) return
        if (mpcLastNoticeDay <= 0) {
            mpcLastNoticeDay = SPUtils.getLong(
                AppContext.context,
                UserController.attachAccount(Values.keyCheckMPCNotice),
                0L
            )
            hasMpcNotice = SPUtils.getBoolean(
                AppContext.context,
                UserController.attachAccount(Values.keyMPCNoticeRead),
                false
            )
        }
        // 同一天，表示mpcBubble已经显示过了
        if (isSameDay(mpcLastNoticeDay, System.currentTimeMillis())) return

        // 新的一天，重置为已读
        SPUtils.put(
            AppContext.context,
            UserController.attachAccount(Values.keyMPCNoticeRead),
            false
        )
        hasMpcNotice = false
        val delay = getToTime(0, 10)
        if (delay < 1000) {
            mpcChecking = true
            // 已经可更新了，直接更新
            viewModel?.checkHasMpcNotice()
        } else if (delay in 1000L..(10 * 60 * 1000L)) {
            mpcChecking = true
            // 离可更新时间还差1s至10分钟时，倒计时更新
            lifecycleScope.launch {
                withContext(Dispatchers.IO) {
                    delay(delay)
                    viewModel?.checkHasMpcNotice()
                }
            }
        }
    }

    /**
     * MPC更新气泡通知
     */
    private fun mpcBubbleNotice() {
        mpcLastNoticeDay = System.currentTimeMillis()
        SPUtils.put(
            AppContext.context,
            UserController.attachAccount(Values.keyCheckMPCNotice),
            mpcLastNoticeDay
        )
        binding.icCloseMpcBubble.setOnClickListener {
            binding.mpcBubbleRoot.show(false)
        }
        binding.tvMpcBubble.clickWithTrigger {
            routeAction(ActionType.actionMPC)
            mpcViewReset(false)
        }
        //显示动画
        AnimationUtil.startMpcBubbleAnimation(binding.mpcBubbleRoot)
    }

    /**
     * 进入了MPC页面
     */
    fun mpcViewReset(show: Boolean) {
        // 设置新数据已读状态
        SPUtils.put(
            AppContext.context,
            UserController.attachAccount(Values.keyMPCNoticeRead),
            show
        )
        binding.mpcBubbleRoot.show(show)
        updateMeRedDot(mpcNotice = show, fromSysNotify = true)
        for (i in 0 until fragmentPagerAdapter.itemCount) {
            if (fragmentPagerAdapter.getFragment(i) is ProfileFragment) {
                fragmentPagerAdapter.getFragment(i).castObject<ProfileFragment>()?.resetMpcNew(show)
                return
            }
        }
    }

    /**
     * 检查账号是否有回收预警
     */
    private fun checkRepossession() {
        val noticeTime = SPUtils.getLong(
            AppContext.context,
            UserController.attachAccount(Values.keyRepossessionNotice)
        )
        // 同一天时
        if (isSameDay(noticeTime, System.currentTimeMillis())) return
        viewModel?.checkShowRepossession()
    }

    private fun checkTodayLeave() {
        val showTime = SPUtils.getLong(
            AppContext.context,
            UserController.attachAccount(Values.keyCheckCancelLeave)
        )
        // 同一天时
        if (isSameDay(showTime, System.currentTimeMillis())) return
        // 3分钟后检测是否弹终止请假弹窗
        postDelayed({
            viewModel?.checkTodayLeave()
        }, 3 * 60 * 1000)
    }

    /**
     * 提前结束请假，开始工作
     */
    private fun showCancelLeaveDialog() {
        AppContext.getTopActivity()?.takeIf { it is FragmentActivity }?.also {
            SPUtils.put(
                this,
                UserController.attachAccount(Values.keyCheckCancelLeave),
                System.currentTimeMillis()
            )
            CommonAlertDialog.Builder()
                .setCancelable(true)
                .setCancel("No, I don’t") {}
                .setConfirm("Yes, I do") {
                    routeAction(ActionType.actionScheduleLeaveRecords)
                }
                .setContextGravity(Gravity.CENTER)
                .setTitle(getString(R.string.text_main_cancel_leave_title))
                .setContext(getString(R.string.text_main_cancel_leave_content))
                .create()
                .show(supportFragmentManager, CommonAlertDialog::javaClass.name)
        }
    }

    fun checkGuide() {
        if (!guideChecked) {
            guideChecked = true
            viewModel?.checkAppGuidePage()
        }
    }

    /**
     * 显示引导页
     */
    private fun showGuideView(tabId: Int) {
        val guideConfig = HomeConfig.guideConfig ?: return
        if (HomeConfig.newChatterStatus != 2) return
        when (tabId) {
            HomeConfig.tabIndexChats -> {
                if (guideConfig.chatGuide) {
                    if (!guideConfig.chatMatchGuide) {
                        guideConfig.chatMatchGuide = true
                        val guides = arrayListOf(R.drawable.ic_guide_main_chat_match)
                        setGuide(guides)
                        CommonRepository.guideRead(HomeConfig.guideChatTag)
                    }
                    return
                }
                guideConfig.also {
                    guideConfig.chatGuide = true
                }
                CommonRepository.guideRead(HomeConfig.guideChatTab)
            }

            HomeConfig.tabIndexTask -> {
                if (guideConfig.taskGuide) return
                guideConfig.also {
                    guideConfig.taskGuide = true
                }
                CommonRepository.guideRead(HomeConfig.guideTaskTab)
            }

            HomeConfig.tabIndexMe -> {
                if (guideConfig.meGuide) return
                guideConfig.also {
                    guideConfig.meGuide = true
                }
                CommonRepository.guideRead(HomeConfig.guideMeTab)
            }

            else -> {
                return
            }
        }
        val guideHelp = GuideHelp()
        val guides = guideHelp.getMainGuides(tabId)
        setGuide(guides)
    }

    /**
     * 显示引导页
     */
    fun setGuide(guides: List<Int>) {
        if (guides.isNotEmpty()) {
            setGuideParent(binding.mainGuideLayout)
            setGuideViewUI(guides)
        }
    }

    /**
     * 新主播收口页
     */
    fun showNewbieCenterBtn(show: Boolean, isRoute: Boolean) {
        binding.btnNewbieCenter.show(show)
        if (show) {
            if (isRoute) {
                val goalsNoticeTime = SPUtils.getLong(
                    AppContext.context,
                    UserController.attachAccount(Values.keyGoalsNotice2)
                )
                // 第一次，或者不是同一天时
                if (goalsNoticeTime == 0L || !isSameDay(goalsNoticeTime, System.currentTimeMillis())) {
                    SPUtils.put(
                        AppContext.context,
                        UserController.attachAccount(Values.keyGoalsNotice2),
                        System.currentTimeMillis()
                    )
                    routeAction(ActionType.actionNewbieCenter)
                }
            }
            binding.btnNewbieCenter.clickWithTrigger {
                routeAction(ActionType.actionNewbieCenter)
            }
        }
    }

    /**
     * 上传应用列表数据
     */
    private fun uploadAppList() {
        try {
            val uploadTime = SPUtils.getLong(AppContext.context, Values.keyCheckAppList)
            // 7天上报一次
            if ((System.currentTimeMillis() - uploadTime) < 7 * 24 * 60 * 60 * 1000L) return
            val appList = ArrayList<AppItemInfo>()
            val list = SystemUtils.getLaunchNonSystemApps(this)
            for (item in list) {
                appList.add(AppItemInfo().apply {
                    appName = item.activityInfo.loadLabel(packageManager).toString()
                    appPackage = item.activityInfo.packageName
                })
            }
            viewModel?.uploadAppListInfo(appList.toJsonString())
        } catch (_: Exception) {

        }
    }

    override fun onRestart() {
        super.onRestart()
        AppModule.userActive()
    }

    override fun onResume() {
        super.onResume()
        //TODO 关闭mpc更新通知 by mask
        /*postDelayed({
            mpcShowCheck()
        }, 1000)*/
    }
}