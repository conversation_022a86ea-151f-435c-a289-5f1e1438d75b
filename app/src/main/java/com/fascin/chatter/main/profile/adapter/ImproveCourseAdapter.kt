package com.fascin.chatter.main.profile.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.bean.ImproveCourseEntity
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import kotlinx.android.synthetic.main.itemview_improve_course.view.btnLearn
import kotlinx.android.synthetic.main.itemview_improve_method.view.tvContent

/**
 * @Desc: 数据中心 Improve Course adapter
 * @Created: Quan
 * @Date: 2024/3/20
 */
class ImproveCourseAdapter(private var block: (ImproveCourseEntity) -> Unit) :
    RecyclerView.Adapter<ImproveCourseAdapter.ViewHolder>() {

    private val dataList = mutableListOf<ImproveCourseEntity>()

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

    fun updateData(data: List<ImproveCourseEntity>?) {
        dataList.clear()
        if (!data.isNullOrEmpty()) {
            dataList.addAll(data)
        }
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.itemview_improve_course, parent, false)
        )

    }

    override fun getItemCount(): Int = dataList.size

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = dataList[position]
        holder.itemView.tvContent.text = item.title
        holder.itemView.btnLearn.clickWithTrigger {
            if (item.id > 0) {
                block.invoke(item)
            }
        }
    }
}