package com.fascin.chatter.main.profile.withdraw

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.repository.AppRepository
import com.iandroid.allclass.lib_common.base.BaseViewModel
import com.iandroid.allclass.lib_common.beans.ProfitListEntity
import com.iandroid.allclass.lib_common.beans.PropertyInfoEntity
import com.iandroid.allclass.lib_common.beans.WithdrawListEntity
import com.iandroid.allclass.lib_common.network.ErrorCodeCheckUtils
import com.iandroid.allclass.lib_common.utils.ToastUtils

/**
 * Created by: WithU
 * Date: 2024/10/9
 * Time: 19:40
 * 提现相关
 */
class WithdrawViewModel : BaseViewModel() {

    val propertyInfoResult = MutableLiveData<PropertyInfoEntity>()
    val propertyInfoError = MutableLiveData<Any>()
    val withdrawResult = MutableLiveData<Boolean>()
    val withdrawListResult = MutableLiveData<WithdrawListEntity>()
    val withdrawListError = MutableLiveData<Any>()

    val profitListResult = MutableLiveData<ProfitListEntity>()
    val profitListError = MutableLiveData<Any>()

    /**
     * 获取主播薪资余额相关数据
     */
    fun getPropertyInfo() {
        compositeDisposable?.add(
            AppRepository.getPropertyInfo()
                .subscribe({
                    propertyInfoResult.postValue(it)
                }, {
                    propertyInfoError.postValue(Any())
                    ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
                })
        )
    }

    /**
     * 查询剩余提现次数
     */
    fun queryWithdrawNum() {
        compositeDisposable?.add(
            AppRepository.queryWithdrawNum()
                .subscribe({

                }, {
                    ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
                })
        )
    }

    /**
     * 主播提现
     */
    fun chatterWithdraw(amount: Long, password: String, ciphertext: String?) {
        compositeDisposable?.add(
            if (ciphertext.isNullOrEmpty()) {
                AppRepository.chatterWithdraw(amount, password)
                    .subscribe({
                        withdrawResult.postValue(true)
                    }, {
                        withdrawResult.postValue(false)
                        ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
                    })
            } else {
                AppRepository.passChatterWithdraw(amount, password, ciphertext)
                    .subscribe({
                        withdrawResult.postValue(true)
                    }, {
                        withdrawResult.postValue(false)
                        ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
                    })
            }
        )
    }

    /**
     * 提现单列表（包含详情数据）
     */
    fun withdrawList(startTime: Long, endTime: Long, page: Int = 1) {
        compositeDisposable?.add(
            AppRepository.withdrawList(startTime, endTime, page)
                .subscribe({
                    withdrawListResult.postValue(it)
                }, {
                    withdrawListError.postValue(Any())
                    ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
                })
        )
    }

    /**
     * 收益流水单列表
     */
    fun profitList(type: String = "in", startTime: Long, endTime: Long, page: Int = 1) {
        compositeDisposable?.add(
            AppRepository.profitList(type, startTime, endTime, page)
                .subscribe({
                    profitListResult.postValue(it)
                }, {
                    profitListError.postValue(Any())
                    ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
                })
        )
    }

    class ViewModeFactory : ViewModelProvider.Factory {
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            return WithdrawViewModel() as T
        }
    }
}