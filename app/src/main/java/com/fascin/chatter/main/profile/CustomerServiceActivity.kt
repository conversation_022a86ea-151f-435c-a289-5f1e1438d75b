package com.fascin.chatter.main.profile

import android.os.Bundle
import android.widget.TextView
import com.fascin.chatter.AppModule
import com.fascin.chatter.AppModule.compositeDisposable
import com.fascin.chatter.R
import com.fascin.chatter.bean.CustomerServiceIntent
import com.fascin.chatter.bean.event.UICSUnderCountEvent
import com.iandroid.allclass.lib_common.AppController
import com.iandroid.allclass.lib_common.base.ChatterBaseActivity
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.show
import io.rong.imkit.config.RongConfigCenter
import io.rong.imkit.utils.RongDateUtils
import io.rong.imlib.IRongCoreCallback
import io.rong.imlib.IRongCoreEnum
import io.rong.imlib.RongCoreClient
import io.rong.imlib.model.Conversation
import kotlinx.android.synthetic.main.activity_customer_service.clQA
import kotlinx.android.synthetic.main.activity_customer_service.clQualityAssurance
import kotlinx.android.synthetic.main.activity_customer_service.clSalary
import kotlinx.android.synthetic.main.activity_customer_service.tvQAContent
import kotlinx.android.synthetic.main.activity_customer_service.tvQANum
import kotlinx.android.synthetic.main.activity_customer_service.tvQATime
import kotlinx.android.synthetic.main.activity_customer_service.tvQualityAssuranceContent
import kotlinx.android.synthetic.main.activity_customer_service.tvQualityAssuranceNum
import kotlinx.android.synthetic.main.activity_customer_service.tvQualityAssuranceTime
import kotlinx.android.synthetic.main.activity_customer_service.tvSalaryContent
import kotlinx.android.synthetic.main.activity_customer_service.tvSalaryNum
import kotlinx.android.synthetic.main.activity_customer_service.tvSalaryTime


/**
 *  @author: LXL
 *  @description: 客服选择
 *  @date: 2024/6/5 14:40
 */
class CustomerServiceActivity : ChatterBaseActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_customer_service)
    }

    override fun initBaseContent() {
        super.initBaseContent()
        AppModule.userActive()
        setTitle(R.string.customer_service)

        clQualityAssurance.clickWithTrigger {
            routeAction(ActionType.actionOnlineSupport) {
                it.param = CustomerServiceIntent().also { intent ->
                    intent.title = getString(R.string.customer_service_quality)
                    intent.accountId = AppController.getQualityAccount()
                }
            }
        }
        clQA.clickWithTrigger {
            routeAction(ActionType.actionOnlineSupport) {
                it.param = CustomerServiceIntent().also { intent ->
                    intent.title = getString(R.string.customer_service_qa)
                    intent.accountId = AppController.getQAAccount()
                }
            }
        }
        clSalary.clickWithTrigger {
            routeAction(ActionType.actionOnlineSupport) {
                it.param = CustomerServiceIntent().also { intent ->
                    intent.title = getString(R.string.customer_service_salary)
                    intent.accountId = AppController.getSalaryAccount()
                }
            }
        }
        compositeDisposable?.add(SimpleRxBus.observe(UICSUnderCountEvent::class) {
            updateConversionUI()
        })
    }

    override fun onResume() {
        super.onResume()
        updateConversionUI()
    }

    private fun updateConversionUI() {
        fetchAndDisplayConversation(
            AppController.getQualityAccount(),
            tvQualityAssuranceContent,
            tvQualityAssuranceTime
        )
        fetchAndDisplayConversation(AppController.getQAAccount(), tvQAContent, tvQATime)
        fetchAndDisplayConversation(AppController.getSalaryAccount(), tvSalaryContent, tvSalaryTime)
        getUnreadCount(AppController.getQualityAccount(), tvQualityAssuranceNum)
        getUnreadCount(AppController.getQAAccount(), tvQANum)
        getUnreadCount(AppController.getSalaryAccount(), tvSalaryNum)
    }

    private fun fetchAndDisplayConversation(account: String, tvContent: TextView, tvTime: TextView) {
        RongCoreClient.getInstance().getConversation(
            Conversation.ConversationType.PRIVATE,
            account,
            object : IRongCoreCallback.ResultCallback<Conversation?>() {
                override fun onSuccess(conversation: Conversation?) {
                    if (conversation != null) {
                        tvContent.show(true)
                        tvContent.text = RongConfigCenter.conversationConfig()
                            .getMessageSummary(this@CustomerServiceActivity, conversation)


                        val time = RongDateUtils.getConversationListFormatDate(
                            conversation.sentTime,
                            this@CustomerServiceActivity,
                            ""
                        )
                        tvTime.text = time
                    }
                }

                override fun onError(e: IRongCoreEnum.CoreErrorCode?) {

                }
            }
        )
    }

    private fun getUnreadCount(account: String, tvNumber: TextView) {
        RongCoreClient.getInstance().getUnreadCount(
            Conversation.ConversationType.PRIVATE,
            account,
            object : IRongCoreCallback.ResultCallback<Int>() {
                override fun onSuccess(integer: Int) {
                    tvNumber.text = "$integer"
                    tvNumber.show(integer > 0)
                }

                override fun onError(e: IRongCoreEnum.CoreErrorCode) {}
            })
    }
}