package com.fascin.chatter.main.profile

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.bean.CourseListIntent
import com.fascin.chatter.main.profile.adapter.ImproveCourseAdapter
import com.fascin.chatter.main.profile.adapter.ImproveMethodAdapter
import com.fascin.chatter.repository.AppRepository
import com.iandroid.allclass.lib_basecore.utils.DeviceUtils
import com.iandroid.allclass.lib_common.base.BaseDialogFragment
import com.iandroid.allclass.lib_common.event.EventKey
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.bean.ActionEntity
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.utils.exts.show
import kotlinx.android.synthetic.main.dialog_mpc_improve.includePlace
import kotlinx.android.synthetic.main.dialog_mpc_improve.ivClose
import kotlinx.android.synthetic.main.dialog_mpc_improve.llCourse
import kotlinx.android.synthetic.main.dialog_mpc_improve.llImprove
import kotlinx.android.synthetic.main.dialog_mpc_improve.rvImprove
import kotlinx.android.synthetic.main.dialog_mpc_improve.rvImproveCourse

/**
 * @Desc: 数据中心 Improve Method
 * @Created: Quan
 * @Date: 2024/3/19
 */
class MpcImproveDialog : BaseDialogFragment {

    private var improveId: String? = null
    private var improveAdapter: ImproveMethodAdapter? = null
    private var courseAdapter: ImproveCourseAdapter? = null
    private var tab: String? = null
    private var block: String? = null
    private var itemTitle: String? = null

    constructor() : super()
    constructor(improveId: String?, tab: String, block: String, itemTitle: String) : super() {
        this.improveId = improveId
        this.tab = tab
        this.block = block
        this.itemTitle = itemTitle
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(
            R.layout.dialog_mpc_improve,
            container,
            false
        )
    }

    override fun onStart() {
        super.onStart()
        val screenHeight = DeviceUtils.getScreenHeight(context).toFloat()
        val height = screenHeight * 0.8
        setBottomPopupAttr(
            WindowManager.LayoutParams.MATCH_PARENT,
            height.toInt()
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        AppModule.userActive()
        ivClose.setOnClickListener { dismissAllowingStateLoss() }
        initRv()
        getData()
    }

    private fun initRv() {
        rvImprove.layoutManager = LinearLayoutManager(context)
        rvImproveCourse.layoutManager = LinearLayoutManager(context)
        improveAdapter = ImproveMethodAdapter()
        courseAdapter = ImproveCourseAdapter {
            context.routeAction(ActionEntity().apply {
                id = ActionType.actionCourseList
                param = CourseListIntent().apply {
                    id = it.id
                    name = it.title
                }
            })
            AppRepository.eventTrace(EventKey.mpc_im_c_c) {
                "tab" to tab
                "block" to block
                "itemTitle" to itemTitle
                "CourseId" to it.id
                "CourseTitle" to it.title
            }
            dismissAllowingStateLoss()
        }
        rvImprove.adapter = improveAdapter
        rvImproveCourse.adapter = courseAdapter
    }

    private fun getData() {
        val viewModel = ViewModelProvider(this).get(ProfileViewModel::class.java)
        viewModel.improveResult.observe(this) {
            improveAdapter?.updateData(it.tips)
            courseAdapter?.updateData(it.courseList)
            llImprove.show(it.tips?.isNotEmpty() == true)
            llCourse.show(it.courseList?.isNotEmpty() == true)
            includePlace.show(false)
        }
        viewModel.getImproveMethod(improveId.orEmpty())
    }
}