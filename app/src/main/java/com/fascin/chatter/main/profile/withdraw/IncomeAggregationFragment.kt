package com.fascin.chatter.main.profile.withdraw

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.config.TabConfig
import com.fascin.chatter.databinding.FragmentIncomeAggregationBinding
import com.iandroid.allclass.lib_basecore.base.BaseUiFragment
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.textColorResource
import net.csdn.roundview.RoundTextView


/**
 * 收入聚合页，他有2个tab，tab页面分别是：
 *
 * 1. 总收入列表
 * 2. 礼物收入列表
 */
class IncomeAggregationFragment : BaseUiFragment() {
    private var binding: FragmentIncomeAggregationBinding? = null

    private var tabId = TabConfig.balanceIncome
    private var startTime = 0L

    private var allFragment: BalanceFragment? = null
    private var giftFragment: BalanceFragment? = null

    private var balancePageAdapter: BalancePageAdapter? = null

    override fun attachLayoutId(): Int {
        return R.layout.fragment_income_aggregation
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentIncomeAggregationBinding.inflate(inflater, container, false)
        return binding?.root
    }

    override fun onDestroyView() {
        binding = null
        super.onDestroyView()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        AppModule.userActive()
        initTabsView()
        initViewPager()
    }

    private fun initTabsView() {
        allFragment = BalanceFragment().apply {
            setTabId(TabConfig.balanceIncomeAll)
            setQueryTime(startTime)
        }

        giftFragment = BalanceFragment().apply {
            setTabId(TabConfig.balanceIncomeNoGift)
            setQueryTime(startTime)
        }

        binding?.tvNoGift?.clickWithTrigger {
            binding?.cbNoGift?.let { cb ->
                cb.isChecked = !cb.isChecked
            }
        }

        binding?.cbNoGift?.setOnCheckedChangeListener { _, isChecked ->
            selectTab(if (isChecked) 1 else 0)
        }
    }

    private fun initViewPager() {
        binding?.vpBalance?.apply {
            balancePageAdapter = BalancePageAdapter(childFragmentManager, lifecycle, listOfNotNull(allFragment, giftFragment))

            adapter = balancePageAdapter
            isUserInputEnabled = false
        }
    }

    fun setQueryTime(time: Long, isUpdate: Boolean = false) {
        startTime = time
        val position = binding?.vpBalance?.currentItem ?: 0
        balancePageAdapter?.getFragment(position)?.setQueryTime(startTime, isUpdate)
    }

    fun setTabId(tabId: Int) {
        this.tabId = tabId
    }

    private fun selectTab(index: Int) {
        binding?.vpBalance?.currentItem = index
    }


    class BalancePageAdapter(
        fragmentManager: FragmentManager,
        lifecycle: Lifecycle,
        private val fragments: List<BalanceFragment> = emptyList()
    ) : FragmentStateAdapter(fragmentManager, lifecycle) {
        fun getFragment(position: Int): BalanceFragment? = fragments.getOrNull(position)

        override fun getItemCount(): Int {
            return fragments.size
        }

        override fun createFragment(position: Int): BalanceFragment {
            return fragments[position]
        }
    }
}