package com.fascin.chatter.main.schedule.view

import android.content.Context
import android.graphics.Rect
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RvItem
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.utils.exts.toPx

/**
 * @Desc: 排班请假记录预览页
 * @Created: Quan
 * @Date: 2024/11/20
 */
@RvItem(id = AppViewType.leaveRecordsPlaceItemView, spanCount = 1)
class LeaveRecordsPlaceItemView(context: Context, fm: FragmentManager, parent: ViewGroup) :
    BaseRvItemView(context, fm, parent) {
    override fun attachLayoutId(): Int {
        return R.layout.itemview_leave_records_place
    }

    override fun initView(context: Context?, view: View?) {

    }

    override fun setView() {

    }

    override fun getItemOffsets(parent: RecyclerView, view: View?, outRect: Rect, position: Int): Boolean {
        outRect.top = if (position == 0) 16.toPx else 0
        outRect.left = 16.toPx
        outRect.right = 16.toPx
        outRect.bottom = 16.toPx
        return true
    }
}