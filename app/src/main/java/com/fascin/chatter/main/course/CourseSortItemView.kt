package com.fascin.chatter.main.course

import android.content.Context
import android.graphics.Rect
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.bean.CourseListIntent
import com.fascin.chatter.bean.CourseSortEntity
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RvItem
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.GlideLoader.loadImage
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.bean.ActionEntity
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.castObject
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.toPx
import kotlinx.android.synthetic.main.itemview_course_sort.view.ivImg
import kotlinx.android.synthetic.main.itemview_course_sort.view.newTag
import kotlinx.android.synthetic.main.itemview_course_sort.view.tvDesc
import kotlinx.android.synthetic.main.itemview_course_sort.view.tvTitle

/**
 * Course sort Item
 * @Created: QuanZH
 * @Date: 2023/9/20
 */
@RvItem(id = AppViewType.courseSortItemView, spanCount = 2)
class CourseSortItemView(context: Context, fm: FragmentManager, parent: ViewGroup) :
    BaseRvItemView(context, fm, parent) {

    override fun attachLayoutId(): Int {
        return R.layout.itemview_course_sort
    }

    override fun initView(context: Context?, view: View?) {

    }

    override fun setView() {
        val entity = getItemData() ?: return
        itemView?.run {
            ivImg.loadImage(AppContext.context, entity.imgUrl, isCenterCrop = false)
            tvTitle.text = entity.name
            tvDesc.text = entity.desc
            newTag.show(entity.hasNew == 1, true)

            clickWithTrigger {
                AppModule.userActive()
                if (entity.id > 0) {
                    context.routeAction(ActionEntity().apply {
                        id = ActionType.actionCourseList
                        param = CourseListIntent().apply {
                            id = entity.id
                            name = entity.name
                        }
                    })
                }
            }
        }
    }

    override fun getItemOffsets(parent: RecyclerView, view: View?, outRect: Rect, position: Int): Boolean {
        outRect.top = if (position == 0 || position == 1) 8.toPx else 0
        outRect.left = if (position % 2 == 0) 16.toPx else 12.toPx
        outRect.right = if (position % 2 == 0) 0 else 12.toPx
        outRect.bottom = 8.toPx
        return true
    }

    private fun getItemData(): CourseSortEntity? = data?.castObject<CourseSortEntity>()
}