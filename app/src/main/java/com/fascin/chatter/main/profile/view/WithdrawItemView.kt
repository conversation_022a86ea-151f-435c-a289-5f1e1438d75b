package com.fascin.chatter.main.profile.view

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.fascin.chatter.R
import com.iandroid.allclass.lib_basecore.utils.DateUtils
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RvItem
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.beans.WithdrawListBean
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.castObject
import kotlinx.android.synthetic.main.itemview_balance.view.ivIcon
import kotlinx.android.synthetic.main.itemview_balance.view.tvMoney
import kotlinx.android.synthetic.main.itemview_balance.view.tvTag
import kotlinx.android.synthetic.main.itemview_balance.view.tvTime
import kotlinx.android.synthetic.main.itemview_balance.view.tvTitle

/**
 * @Created: QuanZH
 */
@RvItem(id = AppViewType.withdrawItemView, spanCount = 1)
class WithdrawItemView(context: Context, fm: FragmentManager, parent: ViewGroup) :
    BaseRvItemView(context, fm, parent) {

    override fun attachLayoutId(): Int {
        return R.layout.itemview_withdraw
    }

    override fun initView(context: Context?, view: View?) {

    }

    override fun setView() {
        val entity = getItemData() ?: return
        itemView?.run {
            tvTitle.text = buildString {
                append(entity.currencyUnit)
                append(entity.amount)
            }
            tvTime.text = DateUtils.formatDate(entity.applyTime.times(1000L), "MM/dd/yyyy")
            tvMoney.text = buildString {
                if (entity.payDirection == "in") append("+")
                else append("-")
                append(entity.currencyUnit)
                append(entity.amount)
            }
            tvTag.text = when (entity.status) {
                4 -> context?.getString(R.string.balance_success)
                3, 5 -> context?.getString(R.string.balance_failed)
                1, 2 -> context?.getString(R.string.balance_pending)

                else -> ""
            }
            tvTag.setBackgroundResource(
                when (entity.status) {
                    4 -> com.iandroid.allclass.lib_common.R.color.color_24C004
                    3, 5 -> com.iandroid.allclass.lib_common.R.color.color_F74E57
                    1, 2 -> R.color.color_0B81EE
                    else -> 0
                }
            )
            ivIcon.setImageResource(
                when (entity.status) {
                    4 -> R.drawable.ic_balance_success
                    3, 5 -> R.drawable.ic_balance_failed
                    1, 2 -> R.drawable.ic_balance_pending

                    else -> com.iandroid.allclass.lib_common.R.drawable.bg_square_gray_r
                }
            )
            clickWithTrigger {
                if (entity.status != 1 && entity.status != 2){
                    context.routeAction(ActionType.actionWithdrawDetail) {
                        it.param = entity
                    }
                }
            }
        }
    }

    private fun getItemData(): WithdrawListBean? = data?.castObject<WithdrawListBean>()
}