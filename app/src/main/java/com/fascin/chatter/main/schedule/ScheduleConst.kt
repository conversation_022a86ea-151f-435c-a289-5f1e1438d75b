package com.fascin.chatter.main.schedule

/**
 * @Desc: 排班功能相关常量
 * @Created: Quan
 * @Date: 2024/11/18
 */
object ScheduleConst {

    /**
     * 排班状态
     */
    const val STATUS_DEFAULT = 0 // 无排班
    const val STATUS_WORK = 1 // 有排班
    const val STATUS_LEAVE = 2 // 请假申请
    const val STATUS_CANCELED = 3 // 被取消的排班

    /**
     * 班次异常状态
     */
    const val STATUS_LATE = 1 // 迟到
    const val STATUS_EARLY = 2 // 早退

    // 周缩写
    val weekDayMap = mapOf(
        1 to "Sun",
        2 to "Mon",
        3 to "Tue",
        4 to "Wed",
        5 to "Thu",
        6 to "Fri",
        7 to "Sat"
    )


    const val TAB_LAST = 0
    const val TAB_THIS = 1
    const val TAB_NEXT = 2

    /**
     * 换班类型
     */
    const val TYPE_ONE_WEEK = 1
    const val TYPE_FOREVER = 2

    /**
     * 换班开始时间
     */
    const val START_THIS_WEEK = 1
    const val START_NEXT_WEEK = 2

    /**
     * 请假状态
     */
    const val LEAVE_STATUS_NOT_START = 0
    const val LEAVE_STATUS_ONGOING = 1
    const val LEAVE_STATUS_CANCELED = 2
    const val LEAVE_STATUS_STOP = 3
    const val LEAVE_STATUS_ENDED = 4
}