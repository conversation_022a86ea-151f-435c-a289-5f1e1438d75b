package com.fascin.chatter.main

import android.content.Context
import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.iandroid.allclass.lib_basecore.base.BaseRvFragment
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemInfo
import com.iandroid.allclass.lib_basecore.view.recyclerview.PullBaseView
import com.iandroid.allclass.lib_common.Values
import com.iandroid.allclass.lib_common.beans.MixBaseRvItemInfo
import com.iandroid.allclass.lib_common.beans.MixPageEntity
import com.iandroid.allclass.lib_common.utils.exts.castObject
import com.iandroid.allclass.lib_common.utils.exts.objFromBundleParam

/**
created by kun
on 2020/9/12.
 */
open class MixListFragment : BaseRvFragment() {
    var mixListViewModel: MixListViewModel? = null
    var mixPageEntity: MixPageEntity? = null
    var pageIndex: Int = Values.fristPage
    override fun initView(view: View?) {
        super.initView(view)
        mixPageEntity = arguments?.objFromBundleParam<MixPageEntity>(Values.intentJsonParam)
        initTitleBar()
        recyclerViewSupport?.setCanPullDown(true)
        recyclerViewSupport?.setCanPullUp(canPullUp())
        showTopForNum(showToTopForNum())
        recyclerView?.setOnRefreshListener(object : PullBaseView.OnRefreshListener {
            override fun onHeaderRefresh() {
                requestData(refresh = true)
            }

            override fun onFooterRefresh() {
                requestData(refresh = false)
            }
        })
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        mixListViewModel = ViewModelProvider(this).get(MixListViewModel::class.java)

        mixListViewModel?.mixBaseRvItemInfo?.observe(this) {
            updateData(it)
        }

        mixListViewModel?.apiErrMsg?.observe(this) {
            onFetchPageDataFail(it)
        }
    }

    open fun updateData(result: MixBaseRvItemInfo) {
        onRefreshComplete()
        pageIndex = result.pageIndex
        updateData(
            result.mixDataList?.castObject<ArrayList<BaseRvItemInfo>>() ?: null,
            result.pageIndex == Values.fristPage
        )
        var dataSize = result.mixDataList?.size ?: 0
        recyclerViewSupport?.setCanPullUp(dataSize > 40)
        onFetchPageDataSuccess(pageIndex, result)
    }

    override fun fetchPageData(refresh: Boolean) {
        super.fetchPageData(refresh)
        requestData(refresh)
    }

    open fun requestData(refresh: Boolean) {
        mixPageEntity?.api_url?.let {
            lastFetchDataTime = System.currentTimeMillis()
            recyclerViewSupport?.showLoading()
            if (refresh) pageIndex = Values.fristPage
            mixListViewModel?.fetchMixData(
                it,
                if (refresh) Values.fristPage else pageIndex + 1,
                this
            )
        }
    }

    open fun onHeaderRefreshComplete() {
        recyclerViewSupport?.onHeaderRefreshComplete()
    }

    open fun onRefreshComplete() {
        onHeaderRefreshComplete()
        onFooterRefreshComplete()
    }

    override fun isAutoRefrechWhileVisible(): Boolean {
        return true
    }

    open fun getHeaderViewItem(): BaseRvItemInfo? {
        return null
    }

    open fun canPullUp(): Boolean {
        return true
    }

    open fun showToTopForNum(): Int {
        return 0
    }

    open fun initTitleBar() {
    }

    open fun getPageDataEmptyMsg(): String? {
        return null
    }

    open fun onFooterRefreshComplete() {
        recyclerViewSupport?.onFooterRefreshComplete()
    }

    open fun getPageDataEmptyIconRes(): Int {
        return 0
    }

    open fun onFetchPageDataSuccess(page: Int, result: MixBaseRvItemInfo) {
        if (recyclerViewSupport?.hasData() == false) {
            //没有数据
            addEmptyView()
        }
    }

    open fun onFetchPageDataFail(errorMsg: String) {
        onRefreshComplete()
        if (recyclerViewSupport?.hasData() == false) {
            //没有数据
            addErrorView()
        }
    }

    open fun addEmptyView() {

    }

    open fun addErrorView() {

    }
}