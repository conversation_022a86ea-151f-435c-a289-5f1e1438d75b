package com.fascin.chatter.main.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.bean.GoalsProgressEntity
import com.iandroid.allclass.lib_common.utils.exts.show
import kotlinx.android.synthetic.main.item_dialog_tiral_goals.view.progressBar
import kotlinx.android.synthetic.main.item_dialog_tiral_goals.view.tvCount
import kotlinx.android.synthetic.main.item_dialog_tiral_goals.view.tvDesc
import kotlinx.android.synthetic.main.item_dialog_tiral_goals.view.tvTitle

class TrialGoalsAdapter : RecyclerView.Adapter<TrialGoalsAdapter.ViewHolder>() {

    private val dataList = mutableListOf<GoalsProgressEntity?>()

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

    fun updateData(data: List<GoalsProgressEntity>?) {
        dataList.clear()
        if (!data.isNullOrEmpty()) {
            dataList.addAll(data)
        }
        notifyDataSetChanged()
    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(LayoutInflater.from(parent.context).inflate(R.layout.item_dialog_tiral_goals, parent, false))
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        dataList[position]?.also {
            holder.itemView.tvTitle.text = it.title
            holder.itemView.tvDesc.text = it.subTitle
            holder.itemView.tvDesc.show(it.subTitle.isNotEmpty())
            holder.itemView.progressBar.setMaxValue(it.progressMax)
            holder.itemView.progressBar.setCurrentValue(it.progressValue.coerceAtMost(it.progressMax))
            holder.itemView.tvCount.text = it.progressMax.toString()
//            holder.itemView.tvStatus.show(it.status >= 0)
//            if (it.status == 1) {
//                holder.itemView.tvStatus.text = "Done"
//                holder.itemView.tvStatus.setBackgroundResource(R.color.color_8fe673)
//            } else if (it.status == 0)  {
//                holder.itemView.tvStatus.text = "Undone"
//                holder.itemView.tvStatus.setBackgroundResource(com.iandroid.allclass.lib_common.R.color.cl_595959)
//            }
        }
    }

    override fun getItemCount(): Int {
        return dataList.size
    }
}