package com.fascin.chatter.main.schedule

import android.os.Bundle
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.R
import com.fascin.chatter.bean.event.UIRefreshScheduleEvent
import com.fascin.chatter.component.views.SUButtonStatus
import com.fascin.chatter.main.schedule.dialog.RequestLeaveDialog
import com.iandroid.allclass.lib_common.base.ChatterBaseActivity
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.ToastUtils
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.textSpanValue
import kotlinx.android.synthetic.main.activity_request_leave.btnSubmit
import kotlinx.android.synthetic.main.activity_request_leave.endView
import kotlinx.android.synthetic.main.activity_request_leave.startView
import kotlinx.android.synthetic.main.activity_request_leave.tvAvailable
import kotlinx.android.synthetic.main.activity_request_leave.tvRules
import kotlinx.android.synthetic.main.activity_request_leave.tvRulesContent

/**
 * @Desc: 请假页
 * @Created: Quan
 * @Date: 2024/11/19
 */
class RequestLeaveActivity : ChatterBaseActivity() {

    private var viewModel: ScheduleModel? = null
    private var startDate: String = ""
    private var endDate: String = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_request_leave)
    }

    override fun initBaseContent() {
        super.initBaseContent()
        setTitle(R.string.text_request_leave)
        setView()
        setModel()
        setListener()
    }

    private fun setView() {
        val startText = textSpanValue(
            this,
            buildString {
                append(getString(R.string.text_start_date))
                append(" *")
            },
            "*",
            R.color.cr_ed2b2b
        )
        val endText = textSpanValue(
            this,
            buildString {
                append(getString(R.string.text_end_date))
                append(" *")
            },
            "*",
            R.color.cr_ed2b2b
        )
        startView.setTitle(startText)
        endView.setTitle(endText)
        btnSubmit.setText("Submit")
    }

    private fun setModel() {
        viewModel = ViewModelProvider(this).get(ScheduleModel::class.java)

        viewModel?.requestLeaveResult?.observe(this) {
            btnSubmit.setButtonStatus(SUButtonStatus.Activated)
            ToastUtils.showToast("Success!")
            SimpleRxBus.post(UIRefreshScheduleEvent())
            finish()
        }
        viewModel?.requestLeaveError?.observe(this) {
            btnSubmit.setButtonStatus(SUButtonStatus.Activated)
            ToastUtils.showToast(it)
        }

        viewModel?.requestLeaveRuleResult?.observe(this) {
            if (it != null) {
                tvAvailable.show(it.leaveLimit.isNotEmpty())
                tvRules.show(it.leaveRule.isNotEmpty())
                tvRulesContent.show(it.leaveRule.isNotEmpty())
                tvAvailable.text = it.leaveLimit
                tvRulesContent.text = it.leaveRule
            }
        }
        viewModel?.requestLeaveRuleError?.observe(this) {
            ToastUtils.showToast(it)
        }

        viewModel?.getLeaveRule()
    }

    private fun setListener() {
        startView.clickWithTrigger {
            showSelectDateDialog(true)
        }

        endView.clickWithTrigger {
            showSelectDateDialog(false)
        }

        btnSubmit.clickWithTrigger {
            btnSubmit.setButtonStatus(SUButtonStatus.Loading)
            viewModel?.requestLeave(startDate, endDate)
        }
    }

    private fun showSelectDateDialog(isStart: Boolean) {
        RequestLeaveDialog(isStart) { contentEntity, start ->
            val date = contentEntity.content.orEmpty()
            if (start) {
                startDate = ScheduleDate.getYMDStr(date, false)
                if (endDate.isNotEmpty() && ScheduleDate.isEarlier(endDate, startDate)) {
                    endDate = startDate
                    ToastUtils.showToast(getString(R.string.text_schedule_select_error))
                    endView.setContent(ScheduleDate.getMDWStr(date))
                }
                startView.setContent(ScheduleDate.getMDWStr(date))
            } else {
                endDate = ScheduleDate.getYMDStr(date, false)
                if (startDate.isNotEmpty() && ScheduleDate.isEarlier(endDate, startDate)) {
                    startDate = endDate
                    ToastUtils.showToast(getString(R.string.text_schedule_select_error))
                    startView.setContent(ScheduleDate.getMDWStr(date))
                }
                endView.setContent(ScheduleDate.getMDWStr(date))
            }
            if (startDate.isNotEmpty() && endDate.isNotEmpty() && !btnSubmit.isLoadingStatus()) {
                btnSubmit.setButtonStatus(SUButtonStatus.Activated)
            }
        }.show(supportFragmentManager, RequestLeaveDialog::javaClass.name)
    }


}