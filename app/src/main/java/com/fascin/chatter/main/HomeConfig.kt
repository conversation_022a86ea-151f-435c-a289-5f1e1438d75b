package com.fascin.chatter.main

import com.fascin.chatter.bean.AppGuideEntity
import com.fascin.chatter.config.TabConfig

object HomeConfig {
    val tabIndexChats = 0
    val tabIndexTask = 1
//    val tabIndexCourse = 2
    val tabIndexMe = 2

    var likesWlmNew = false
    var likesCupidChatNew = false


    var wlmNeedRefresh = false
    var cupidchatNeedRefresh = false
    var ilikeNeedRefresh = false

    var courseNeedRefresh = false

    fun likesTabHasNew(): Boolean {
        return likesWlmNew || likesCupidChatNew
    }

    fun likesTabHasNew(tabId: Int): Boolean {
        if (tabId == TabConfig.homeLikeTabWlm) {
            return likesWlmNew
        } else if (tabId == TabConfig.homeLikeTabCupidChat) {
            return likesCupidChatNew
        }
        return false
    }

    // 新手激活状态 0 未完成，1 未读 恭喜 通知，2 完成
    var newChatterStatus: Int = -1

    var isNewChatter: Int = 0

    // app引导页配置
    var guideConfig: AppGuideEntity? = AppGuideEntity()

    // 引导页已读上报参数
    val guideChatTab: String = "chat_tab_pop_up"
    val guideChatTag: String = "chat_tab_tags_pop_up"
    val guideTaskTab: String = "task_tab_pop_up"
    val guideMeTab: String = "me_tab_pop_up"
    val guideMeAdd: String = "me_tab_add_model_pop_up"
    val guideIM: String = "im_pop_up"
    val guideIMFC: String = "im_fc_pop_up"
    val guideIMNearby: String = "im_nearby_pop_up"
    val guidePPV: String = "ppv_pop_up"
}