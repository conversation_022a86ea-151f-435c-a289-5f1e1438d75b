package com.fascin.chatter.main.schedule.view

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import androidx.recyclerview.widget.LinearLayoutManager
import com.fascin.chatter.R
import com.fascin.chatter.bean.SchedulePerItemEntity
import com.fascin.chatter.main.schedule.ScheduleConst
import com.fascin.chatter.main.schedule.adapter.SchedulePreUnitAdapter
import kotlinx.android.synthetic.main.view_schedule_day_content.view.flPreContent
import kotlinx.android.synthetic.main.view_schedule_day_content.view.rvScheduleContent
import kotlinx.android.synthetic.main.view_schedule_day_content.view.svDayView

/**
 * @Desc:排班预览页 天 内容容器
 * @Created: Quan
 * @Date: 2024/11/18
 */
class ScheduleDayContentView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private var itemHeight = 0f

    init {
        View.inflate(context, R.layout.view_schedule_day_content, this)
        itemHeight = resources.getDimension(R.dimen.schedule_item_height)
    }

    fun setViewData(
        dataList: List<SchedulePerItemEntity>,
        weekDayId: Int,
        itemDay: String,
        dateSize: Int,
        isToday: Boolean
    ) {
        svDayView.setViewData(ScheduleConst.weekDayMap[weekDayId], itemDay, isToday)
        setPreRv(dateSize)
        setContentView(dataList, isToday)
    }

    /**
     * 设置基础表格View，预览时，只做UI填充，无需真实数据
     */
    private fun setPreRv(dateSize: Int) {
        val preDatas = ArrayList<SchedulePerItemEntity>()
        repeat(dateSize) {
            preDatas.add(SchedulePerItemEntity())
        }
        val adapter = SchedulePreUnitAdapter()
        rvScheduleContent.layoutManager = LinearLayoutManager(context)
        rvScheduleContent.adapter = adapter
        adapter.updateData(preDatas)
    }

    /**
     * 设置排班内容
     *
     */
    private fun setContentView(dataList: List<SchedulePerItemEntity>, isToday: Boolean) {
        flPreContent.removeAllViews()
        if (dataList.isEmpty()) return
        val splitList = splitListByCondition(dataList.sortedBy { it.shiftId })
        splitList.forEach {
            val topMarginPx = (it[0].shiftId - 1).minus(0) * itemHeight
            val height = it.size * itemHeight
            flPreContent.addView(
                SchedulePreItemView(context).apply {
                    layoutParams =
                        MarginLayoutParams(LayoutParams.MATCH_PARENT, height.toInt()).apply {
                            setMargins(0, topMarginPx.toInt(), 0, 0) // 设置左、上、右、下的边距
                        }
                    setViewData(it[0].abnormalStatus, it[0].status, isToday)
                }
            )
        }
    }

    /**
     * 将列表按条件分组
     */
    private fun splitListByCondition(entities: List<SchedulePerItemEntity>): List<List<SchedulePerItemEntity>> {

        val result = mutableListOf<List<SchedulePerItemEntity>>()
        var currentList = mutableListOf<SchedulePerItemEntity>()

        for (i in entities.indices) {
            // 将当前元素加入当前子列表
            currentList.add(entities[i])

            // 检查是否需要分组
            val isLastElement = (i == entities.lastIndex) // 是否为最后一个元素
            val isNewGroup = !isLastElement && // 非最后一个元素且以下条件不满足
                    (entities[i + 1].shiftId != entities[i].shiftId + 1
                            || margeStatus(entities[i + 1]) != margeStatus(entities[i]))

            if (isLastElement || isNewGroup) {
                // 当前子列表已完成，添加到结果中并清空
                result.add(currentList)
                currentList = mutableListOf()
            }
        }
        return result
    }

    /**
     * 重整status，用于item合并展示计算高度
     */
    private fun margeStatus(entity: SchedulePerItemEntity): Int {
        return if (entity.status == ScheduleConst.STATUS_CANCELED) 15
        else if (entity.abnormalStatus == ScheduleConst.STATUS_LATE) 11
        else if (entity.abnormalStatus == ScheduleConst.STATUS_EARLY) 12
        else if (entity.status == ScheduleConst.STATUS_WORK) 13
        else if (entity.status == ScheduleConst.STATUS_LEAVE) 14
        else 10
    }

}