package com.fascin.chatter.main.profile

import android.content.Context
import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.R
import com.fascin.chatter.bean.EmptyEntity
import com.fascin.chatter.bean.event.MessageSettingChangeEvent
import com.fascin.chatter.config.TabConfig
import com.fascin.chatter.main.MixListFragment
import com.fascin.chatter.main.profile.edit.MessageSettingEditDialog
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemInfo
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.Values
import com.iandroid.allclass.lib_common.Values.Companion.messageSettingTypeGreeting
import com.iandroid.allclass.lib_common.Values.Companion.messageSettingTypeMessage
import com.iandroid.allclass.lib_common.beans.MessageSettingEntity
import com.iandroid.allclass.lib_common.beans.MsgContentEntity
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.exts.castObject

/**
 * @Created: QuanZH
 * @Date: 2023/7/11
 */
class MessageSettingFragment : MixListFragment(), IMessageSettingRvItemAction {

    private val viewModel by lazy {
        ViewModelProvider(
            this,
            ProfileViewModel.ViewModeFactory()
        )[ProfileViewModel::class.java]
    }

    private var type = messageSettingTypeGreeting

    override fun initView(view: View?) {
        super.initView(view)
        // 当前frag是greeting还是message
        if (mixPageEntity?.tabId == TabConfig.meMessageSettingMsg)
            type = messageSettingTypeMessage
        // 此页面数据一次性加载完成，无需刷新与加载更多
        recyclerViewSupport?.setCanPullDown(true)
        recyclerViewSupport?.setCanPullUp(false)
        //骨骼
        updateData(
            ArrayList<BaseRvItemInfo>().also {
                it.add(BaseRvItemInfo(Any(), AppViewType.messageSettingPlaceItemView))
                it.add(BaseRvItemInfo(Any(), AppViewType.messageSettingPlaceItemView))
                it.add(BaseRvItemInfo(Any(), AppViewType.messageSettingPlaceItemView))
                it.add(BaseRvItemInfo(Any(), AppViewType.messageSettingPlaceItemView))
            },
            true
        )
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        // 删除成功
        viewModel.messageSettingDelAll.observe(this@MessageSettingFragment) {
            delMessageSuccess(it)
        }

        // 刷新数据
        SimpleRxBus.observe(MessageSettingChangeEvent::class) {
            if (it.type == type)
                requestData(true)
        }
    }

    override fun requestData(refresh: Boolean) {
        mixPageEntity?.api_url?.let {
            lastFetchDataTime = System.currentTimeMillis()
            recyclerViewSupport?.showLoading()
            if (refresh) pageIndex = Values.fristPage
            mixListViewModel?.fetchMessageSettingMixData(
                it,
                type,
                this@MessageSettingFragment
            )
        }
    }

    override fun addEmptyView() {
        val emptyEntity: EmptyEntity?
        when (mixPageEntity?.tabId) {
            TabConfig.meMessageSettingGreeting -> {
                emptyEntity = EmptyEntity().also {
                    it.content = getString(R.string.page_nodata_message_setting_greet)
                    it.icRes = R.mipmap.ic_msg_setting_nodata
                }
            }

            TabConfig.meMessageSettingMsg -> {
                emptyEntity = EmptyEntity().also {
                    it.content = getString(R.string.page_nodata_message_setting_message)
                    it.icRes = R.mipmap.ic_msg_setting_nodata
                }
            }

            else -> {
                emptyEntity = EmptyEntity().also {
                    it.title = getString(R.string.page_data_tips)
                    it.icRes = R.mipmap.ic_msg_setting_nodata
                }
            }
        }

        emptyEntity.also { data ->
            updateData(
                ArrayList<BaseRvItemInfo?>().also {
                    it.add(BaseRvItemInfo(data, AppViewType.comEmptyView, this))
                }, true
            )
        }
    }

    override fun addMessage(userID: String, nickName: String) {
        // 跳转添加页
        MessageSettingEditDialog(
            MsgContentEntity("", -1, 0, userID),
            nickName,
            type
        ) {
            addMessageSuccess(it)
        }.show(childFragmentManager, MessageSettingEditDialog::javaClass.name)
    }

    override fun delMessage(msgID: Int, userID: String) {
        // 删除指定消息
        viewModel.delMessageOrGreeting(userID, msgID, true)
    }

    override fun modifyMessage(msgID: Int, msgContent: String, userID: String, nickName: String) {
        // 跳转添加页,修改指定消息
        MessageSettingEditDialog(
            MsgContentEntity(msgContent, msgID, 0, userID),
            nickName,
            type
        ) {
            modifyMessageSuccess(it)
        }.show(childFragmentManager, MessageSettingEditDialog::javaClass.name)
    }

    override fun onRefresh() {
        requestData(true)
    }

    /**
     * 添加成功
     */
    private fun addMessageSuccess(contentEntity: MsgContentEntity) {
        recyclerViewSupport?.infos?.also { rvItems ->
            for (index in rvItems.indices) {
                val item = rvItems[index]
                if (item.viewType == AppViewType.messageSettingItemView) {
                    val tempData = item.data?.castObject<MessageSettingEntity>()
                    if (contentEntity.mid.isNotEmpty() && tempData?.userId == contentEntity.mid) {
                        tempData.contentList.also {
                            // 新添加的放在最前面
                            it.add(0, contentEntity)
                            // 新增成功，currentNum加1
                            tempData.currentNum++
                            // 超过了允许展示的条数时，删除最后一条
                            if (contentEntity.maxShowNum < tempData.currentNum)
                                it.removeLast()
                        }
                        recyclerViewSupport?.adapter?.notifyItemChanged(index)
                        break
                    }
                }
            }
        }
    }

    /**
     * 修改成功
     */
    private fun modifyMessageSuccess(contentEntity: MsgContentEntity) {
        recyclerViewSupport?.infos?.also { rvItems ->
            for (index in rvItems.indices) {
                val item = rvItems[index]
                if (item.viewType == AppViewType.messageSettingItemView) {
                    val tempData = item.data?.castObject<MessageSettingEntity>()
                    if (contentEntity.mid.isNotEmpty() && tempData?.userId == contentEntity.mid) {
                        for (contentIndex in tempData.contentList.indices) {
                            if (tempData.contentList[contentIndex].id == contentEntity.id) {
                                tempData.contentList[contentIndex].content = contentEntity.content
                                tempData.contentList[contentIndex].status = 1
                                recyclerViewSupport?.adapter?.notifyItemChanged(index)
                                break
                            }
                        }
                        break
                    }
                }
            }
        }
    }

    /**
     * 删除成功
     */
    private fun delMessageSuccess(modelEntity: MessageSettingEntity) {
        recyclerViewSupport?.infos?.also { rvItems ->
            for (index in rvItems.indices) {
                val item = rvItems[index]
                if (item.viewType == AppViewType.messageSettingItemView) {
                    val tempData = item.data?.castObject<MessageSettingEntity>()
                    if (modelEntity.userId.isNotEmpty() && tempData?.userId == modelEntity.userId) {
                        tempData.currentNum = modelEntity.currentNum
                        tempData.contentList = modelEntity.contentList
                        recyclerViewSupport?.adapter?.notifyItemChanged(index)
                        break
                    }
                }
            }
        }
    }

    override fun addErrorView() {
        updateData(
            ArrayList<BaseRvItemInfo?>().also {
                it.add(BaseRvItemInfo(Any(), AppViewType.exceptionView, this))
            }, true
        )
    }

    override fun fragmentVisible(visible: Boolean) {
        super.fragmentVisible(visible)
//        if (visible) {
//            // 当前fragment状态变成显示，可以进行一些操作
//        }
    }
}