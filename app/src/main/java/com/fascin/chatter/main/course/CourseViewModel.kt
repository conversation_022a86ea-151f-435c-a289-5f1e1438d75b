package com.fascin.chatter.main.course

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.bean.CourseClassEntity
import com.fascin.chatter.bean.CourseSortEntity
import com.fascin.chatter.bean.ExaminationEntity
import com.fascin.chatter.bean.SubmitAnswerEntity
import com.fascin.chatter.repository.AppRepository
import com.iandroid.allclass.lib_common.base.BaseViewModel
import com.iandroid.allclass.lib_common.utils.ToastUtils

/**
 * @Desc: 课程相关 view model
 * @Created: Quan
 * @Date: 2023/9/20
 */
class CourseViewModel : BaseViewModel() {

    val courseSortResult = MutableLiveData<List<CourseSortEntity>>()
    val courseSortError = MutableLiveData<Boolean>()

    val coursesResult = MutableLiveData<List<CourseClassEntity>>()
    val coursesError = MutableLiveData<Boolean>()

    val questionsResult = MutableLiveData<ExaminationEntity>()
    val questionsError = MutableLiveData<Boolean>()

    val submitAnswersResult = MutableLiveData<SubmitAnswerEntity>()
    val submitAnswersError = MutableLiveData<Boolean>()

    fun getCoursesSort(){
        compositeDisposable?.add(
            AppRepository.getCoursesSort()
                .subscribe({
                    courseSortResult.postValue(it)
                }, {
                    courseSortError.postValue(true)
                })
        )
    }

    fun getCourses(lastId: Int, categoryId: Int) {
        compositeDisposable?.add(
            AppRepository.getCourses(lastId, categoryId)
                .subscribe({
                    coursesResult.postValue(it)
                }, {
                    coursesError.postValue(true)
                })
        )
    }

    /**
     * 获取试卷信息
     */
    fun getQuestions(courseId: Int) {
        compositeDisposable?.add(
            AppRepository.getQuestions(courseId)
                .subscribe({
                    questionsResult.postValue(it)
                }, {
                    ToastUtils.showToast(it.message)
                    questionsError.postValue(true)
                })
        )
    }

    /**
     * 提交答题信息
     * @param id 试卷id
     * @param version 试卷版本id
     * @param answers 用户填写的答案（格式：[[1],[3],[1,3],[2]]）
     */
    fun submitAnswers(id: Int, version: Int, answers: String) {
        compositeDisposable?.add(
            AppRepository.submitAnswers(id, version, answers)
                .subscribe({
                    submitAnswersResult.postValue(it)
                }, {
                    submitAnswersError.postValue(true)
                    ToastUtils.showToast(it.message)
                })
        )
    }


    class ViewModeFactory : ViewModelProvider.Factory {
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            return CourseViewModel() as T
        }
    }
}