package com.fascin.chatter.main.profile

import android.os.Bundle
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.bean.EmptyEntity
import com.fascin.chatter.bean.PenaltyEntity
import com.fascin.chatter.bean.PenaltyUnreadEntity
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemInfo
import com.iandroid.allclass.lib_basecore.view.recyclerview.PullBaseView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RecyclerViewSupport
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.base.ChatterBaseActivity
import com.iandroid.allclass.lib_common.beans.WebIntent
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import kotlinx.android.synthetic.main.activity_penalties.rvPenalties
import kotlinx.android.synthetic.main.activity_penalties.tvChatPolicy

/**
 *  @author: LXL
 *  @description: 质检处罚
 *  @date: 2024/6/4 11:46
 */
class PenaltiesActivity : ChatterBaseActivity() {
    private var recyclerViewSupport: RecyclerViewSupport? = null
    private val viewModel by lazy {
        ViewModelProvider(
            this,
            ProfileViewModel.ViewModeFactory()
        )[ProfileViewModel::class.java]
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_penalties)
    }

    override fun initBaseContent() {
        super.initBaseContent()
        AppModule.userActive()
        setTitle(R.string.penalties)
        initRv()
        setPreView()
        viewModel.getPenaltyHistory()
        viewModel.penaltyHistoryResult.observe(this) { penaltyHistoryEntity ->
            recyclerViewSupport?.onHeaderRefreshComplete()
            tvChatPolicy.clickWithTrigger {
                routeAction(ActionType.actionTypeToWebActivity) {
                    it.param = WebIntent().also { webIntent ->
                        webIntent.showTitle = true
                        webIntent.url = penaltyHistoryEntity.keepChatPolicyUrl
                    }
                }
            }
            updateData(
                ArrayList<BaseRvItemInfo?>().also { list ->
                    penaltyHistoryEntity?.list?.forEach { penaltyEntity ->
                        list.add(BaseRvItemInfo(penaltyEntity, AppViewType.penaltiesItemView, this))
                    }
                }, true
            )
            SimpleRxBus.post(PenaltyUnreadEntity().apply {
                num = 0
            })
        }

    }

    private fun initRv() {
        recyclerViewSupport = RecyclerViewSupport(supportFragmentManager, rvPenalties, null).also {
            it.setCanPullDown(true)
            it.setCanPullUp(false)
            it.recyclerView.setOnRefreshListener(object : PullBaseView.OnRefreshListener {
                override fun onHeaderRefresh() {
                    viewModel.getPenaltyHistory()
                }

                override fun onFooterRefresh() {
                }
            })
        }
        val penaltiesList = listOf(PenaltyEntity(), PenaltyEntity(), PenaltyEntity())
        updateData(
            ArrayList<BaseRvItemInfo?>().also { list ->
                penaltiesList.forEach { penaltyEntity ->
                    list.add(BaseRvItemInfo(penaltyEntity, AppViewType.penaltiesItemView, this))
                }
            }, true
        )
    }

    private fun updateData(itemTemp: ArrayList<BaseRvItemInfo?>, needClear: Boolean = true) {
        if (itemTemp.isNotEmpty()) recyclerViewSupport?.updateData(itemTemp, needClear)
        else if (needClear) addEmptyView()
    }

    private fun setPreView() {
        updateData(ArrayList<BaseRvItemInfo?>().also { list ->
            repeat(2) {
                list.add(BaseRvItemInfo(Any(), AppViewType.penaltiesPlaceItemView, this))
            }
        })
    }

    private fun addEmptyView() {
        val emptyEntity = EmptyEntity().also {
            it.title = getString(R.string.penalties_no_record)
            it.content = getString(R.string.penalties_very_good)
            it.icRes = R.mipmap.ic_penalties_nodata
        }

        emptyEntity.also { data ->
            updateData(ArrayList<BaseRvItemInfo?>().also {
                it.add(BaseRvItemInfo(data, AppViewType.comEmptyView, this))
            })
        }
    }
}