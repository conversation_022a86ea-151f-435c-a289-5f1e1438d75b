package com.fascin.chatter.main.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.bean.BonusMatchHubItemEntity
import com.fascin.chatter.component.views.OnMediaChangedCallback
import com.fascin.chatter.im.UserOnlineHelper
import com.iandroid.allclass.lib_common.beans.MediaEntity
import com.iandroid.allclass.lib_common.utils.exts.show
import kotlinx.android.synthetic.main.itemview_bonus_match_hub.view.bonusNiceName
import kotlinx.android.synthetic.main.itemview_bonus_match_hub.view.bonusUserAge
import kotlinx.android.synthetic.main.itemview_bonus_match_hub.view.bonusUserInterests
import kotlinx.android.synthetic.main.itemview_bonus_match_hub.view.bonusUserLocation
import kotlinx.android.synthetic.main.itemview_bonus_match_hub.view.bonusUserMedias
import kotlinx.android.synthetic.main.itemview_bonus_match_hub.view.bonusUserOnline
import kotlinx.android.synthetic.main.itemview_bonus_match_hub.view.bonusUserSign
import kotlinx.android.synthetic.main.itemview_bonus_match_hub.view.bonusUserVip

/**
 *  @author: LXL
 *  @description: 定向建联
 *  @date: 2024/4/17 15:35
 */
class BonusMatchHubAdapter() : RecyclerView.Adapter<BonusMatchHubAdapter.ViewHolder>() {
    private var mListener: OnItemRemovedListener? = null

    fun setOnItemRemovedListener(listener: OnItemRemovedListener) {
        mListener = listener
    }

    private var dataList = mutableListOf<BonusMatchHubItemEntity>()

    fun getBonusUserData(): List<BonusMatchHubItemEntity> {
        return dataList
    }

    fun getUserId(): String {
        return if (dataList.size > 0) dataList[0].userID else ""
    }

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

    fun updateData(data: List<BonusMatchHubItemEntity>) {
        dataList.clear()
        if (!data.isNullOrEmpty()) {
            dataList.addAll(data)
        }
        notifyDataSetChanged()
    }

    fun removeItem(position: Int) {
        if (position >= 0 && position < dataList.size) {
            dataList.removeAt(position)
            notifyItemRemoved(position)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.itemview_bonus_match_hub, parent, false)
        )
    }

    override fun getItemCount(): Int = dataList.size

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val data = dataList[position]
        holder.itemView.apply {
            this.bonusNiceName.text = data.nickName
            this.bonusUserAge.text = ",${data.age}"
            this.bonusUserSign.text = data.sign
            this.bonusUserSign.show(data.sign.isNotEmpty())

            this.bonusUserInterests.setDataSource(data.tags)
            this.bonusUserInterests.show(data.tags?.isNotEmpty() ?: false)

            this.bonusUserLocation.text = data.address
            this.bonusUserLocation.show(data.address.isNotEmpty())
            this.bonusUserOnline.show(UserOnlineHelper.isOnline(data.userID))
            this.bonusUserVip.show(data.vip == 1)

            this.bonusUserMedias.enableUserInputEvent(true)
            this.bonusUserMedias.setDataSource(data.mediaList, showTopIndicator = 1)
            this.bonusUserMedias.setOnMediaChangedCallback(object : OnMediaChangedCallback {
                override fun onMediaChanged(mediaEntity: MediaEntity) {
                    mListener?.onItemMediaChanged(dataList.indexOf(data), mediaEntity)
                }

                override fun onMuteTapped() {

                }
            })
        }
    }
}

interface OnItemRemovedListener {

    fun onItemMediaChanged(position: Int, mediaEntity: MediaEntity)

    fun onMuteTapped()
}