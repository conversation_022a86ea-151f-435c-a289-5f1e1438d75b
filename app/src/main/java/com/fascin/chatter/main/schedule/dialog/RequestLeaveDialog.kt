package com.fascin.chatter.main.schedule.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.LinearSnapHelper
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.main.schedule.ScheduleDate
import com.fascin.chatter.main.schedule.adapter.LeaveSelectDateAdapter
import com.iandroid.allclass.lib_basecore.utils.DeviceUtils
import com.iandroid.allclass.lib_common.base.BaseDialogFragment
import com.iandroid.allclass.lib_common.beans.ComContentEntity
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import kotlinx.android.synthetic.main.dialog_request_leave.ivClose
import kotlinx.android.synthetic.main.dialog_request_leave.rvDate
import kotlinx.android.synthetic.main.dialog_request_leave.tvCancel
import kotlinx.android.synthetic.main.dialog_request_leave.tvConfirm
import kotlinx.android.synthetic.main.dialog_request_leave.tvTitle

/**
 * @Desc: 请假选择时间的弹窗
 * @Created: Quan
 * @Date: 2024/11/19
 */
class RequestLeaveDialog() : BaseDialogFragment() {

    private var completeBlock: ((ComContentEntity, Boolean) -> Unit)? = null
    private var isStart: Boolean = true
    private var adapter: LeaveSelectDateAdapter? = null

    constructor(isStart: Boolean, completeBlock: (ComContentEntity, Boolean) -> Unit) : this() {
        this.isStart = isStart
        this.completeBlock = completeBlock
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return inflater.inflate(R.layout.dialog_request_leave, container, false)
    }

    override fun onStart() {
        super.onStart()
        val screenWidth = DeviceUtils.getScreenWidth(context).toFloat()
        val width = screenWidth * 0.86
        setCenterPopupAttr(width.toInt(), WindowManager.LayoutParams.WRAP_CONTENT)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        tvTitle.text = getString(if (isStart) R.string.text_start_date else R.string.text_end_date)
        setRvView()
        setListener()
    }

    private fun setRvView() {
        rvDate.layoutManager = LinearLayoutManager(context)
        // 使用 LinearSnapHelper 实现居中效果
        val snapHelper = LinearSnapHelper()
        snapHelper.attachToRecyclerView(rvDate)
        adapter = LeaveSelectDateAdapter()
        rvDate.adapter = adapter
        adapter?.updateData(getDates())

        rvDate.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    val layoutManager = recyclerView.layoutManager as LinearLayoutManager
                    val centerView = snapHelper.findSnapView(layoutManager)
                    val position = layoutManager.getPosition(centerView!!)
                    adapter?.selectedPosition = position
                    adapter?.notifyDataSetChanged() // 更新选中状态
                }
            }
        })
    }

    private fun setListener() {
        ivClose.clickWithTrigger {
            dismissAllowingStateLoss()
        }

        tvCancel.clickWithTrigger {
            dismissAllowingStateLoss()
        }

        tvConfirm.clickWithTrigger {
            adapter?.getSelectedItem()?.let {
                completeBlock?.invoke(it, isStart)
            }
            dismissAllowingStateLoss()
        }
    }

    /**
     * 获取30天内的日期
     * 前后加一个item，由于RecyclerView + LinearSnapHelper实现竖向选择器居中效果时，
     * 无法将第一个和最后一个滚到中间，所以在前后各加一个item
     */
    private fun getDates(): List<ComContentEntity> {
        val list = ArrayList<ComContentEntity>()
        list.add(ComContentEntity())
        val start = ScheduleDate.getTodayDateStr()
        ScheduleDate.getNextXDays(start, 30).forEachIndexed { index, date ->
            list.add(ComContentEntity().apply {
                id = index
                content = date
            })
        }
        list.add(ComContentEntity())
        return list
    }


}