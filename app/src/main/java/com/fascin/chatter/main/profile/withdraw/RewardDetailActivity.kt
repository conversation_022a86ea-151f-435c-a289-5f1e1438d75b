package com.fascin.chatter.main.profile.withdraw

import android.os.Bundle
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.databinding.ActivityRewardDetailBinding
import com.iandroid.allclass.lib_common.base.ChatterBaseActivity
import com.iandroid.allclass.lib_common.beans.ProfitListBean
import com.iandroid.allclass.lib_common.utils.exts.show

/**
 * Created by: LXL
 * Date: 2024/10/10
 * Time: 14:09
 * 奖励明细
 */
class RewardDetailActivity : ChatterBaseActivity() {

    private lateinit var binding: ActivityRewardDetailBinding

    private var profitInfoBean: ProfitListBean? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityRewardDetailBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun initBaseContent() {
        super.initBaseContent()
        AppModule.userActive()
        profitInfoBean = parseJsonParams<ProfitListBean>()

        when(profitInfoBean?.vtype) {
            "gift_receive" -> initGiftReward()
            else -> initNormalReward()
        }
    }

    private fun initNormalReward() {
        profitInfoBean?.apply {
            setTitle(title)
            binding.tvInChatterId.setRightText(cid)
            binding.tvInRewardDate.setRightText(createdAt)

            binding.llInTaskReward.show(type == 1)

            details?.let {
                binding.tvInTaskTime.show(it.period.isNotEmpty())
                binding.tvInTaskTime.setRightText(it.period)

                binding.tvInTaskLevel.setRightText(it.taskLvlName)
                binding.tvInTaskPP.setRightText("$moneySymbol ${it.rewardPP}")
                binding.tvInTaskPV.setRightText("$moneySymbol ${it.rewardPV}")

                binding.tvInUnlockedPP.setRightText(it.pp)
                binding.tvInUnlockedPV.setRightText(it.pv)
                binding.tvInUnlockedPPV.setRightText(it.ppvCommission)
                binding.tvInPPVCommission.setRightText("$moneySymbol ${it.ppvMoney}")

                binding.llInReason.show(it.reason.isNotEmpty())
                binding.tvInReason.text = it.reason
            }
            binding.tvInReward.setRightText("$moneySymbol $money")
        }
    }

    private fun initGiftReward() {
        profitInfoBean?.let {
            setTitle(getString(R.string.gift))

            binding.tvInChatterId.setRightText(it.cid)

            binding.tvInRewardDate.setLeftText(getString(R.string.gift_received_date))
            binding.tvInRewardDate.setRightText(it.createdAt)

            binding.tvInTaskTime.setLeftText(getString(R.string.gift_name))
            binding.tvInTaskTime.setRightText(it.details?.desc.orEmpty())


            binding.tvInReward.setLeftText(getString(R.string.gift_commission))
            binding.tvInReward.setRightText("${it.moneySymbol} ${it.money}")

            binding.llInReason.show(false) //不展示
            binding.llInTaskReward.show(false) //不展示
        }
    }
}