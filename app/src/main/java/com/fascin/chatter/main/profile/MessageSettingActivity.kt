package com.fascin.chatter.main.profile

import android.os.Bundle
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.config.TabConfig
import com.fascin.chatter.main.adapter.TabAdapter
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.iandroid.allclass.lib_basecore.base.BaseFragment
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.Values
import com.iandroid.allclass.lib_common.base.ChatterBaseActivity
import com.iandroid.allclass.lib_common.beans.HomeTabEntity
import com.iandroid.allclass.lib_common.beans.MixPageEntity
import com.iandroid.allclass.lib_common.beans.UserEntity
import com.iandroid.allclass.lib_common.utils.exts.castObject
import com.iandroid.allclass.lib_common.utils.exts.objToJsonString
import kotlinx.android.synthetic.main.activity_message_setting.tabLayout
import kotlinx.android.synthetic.main.activity_message_setting.tabViewpager

/**
 * @Created: QuanZH
 * @Date: 2023/7/11
 */
class MessageSettingActivity : ChatterBaseActivity(), TabAdapter.IFragmentTabCreator {

    var curUser: UserEntity? = null
    var tabAdapter: TabAdapter? = null

    // 是否打开的是所有model的MessageSetting
    var isAll: Boolean = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_message_setting)
    }

    override fun initBaseContent() {
        super.initBaseContent()
        AppModule.userActive()
        curUser = parseJsonParams<UserEntity>()
        isAll = curUser == null || curUser?.userId.isNullOrEmpty()
        showTitleBar(true)
        setTitle(R.string.profile_message_setting)
        if (isAll)
            setTitle(R.string.profile_message_setting)
        else
            setTitle(String.format(getString(R.string.message_setting_name), curUser?.nickname))
        initTabAndViewPager()
        setTabViewPagerCallback()
    }

    /**
     * 初始化tabLayout和ViewPager
     */
    private fun initTabAndViewPager() {
        tabViewpager.offscreenPageLimit = 2
        // curUser为null，表示查看全部model的
        var tabList =
            if (isAll) TabConfig.getMessageSettingTabList() else TabConfig.getUserMessageSettingTabList()
        tabAdapter = TabAdapter(tabList, supportFragmentManager, lifecycle)
        val autoShowIndex = getDefaultShowTabIndex(tabList)
        tabViewpager.adapter = tabAdapter.also { it?.iTabFragmentCreator = this }
        TabLayoutMediator(tabLayout, tabViewpager) { tab, position ->
            tab.text = tabList[position].title
        }.attach()

        for (index in tabList.indices) {
            val tab: TabLayout.Tab? = tabLayout.getTabAt(index)
            tab?.setCustomView(R.layout.layout_home_tab_item)
            tab?.customView?.run {
                val tabItemViewTitle = findViewById<TextView>(R.id.tv_tab_name)
                tabItemViewTitle.text = tabList[index].title
                if (index == autoShowIndex) {
                    tabItemViewTitle.isSelected = true
                    tabItemViewTitle.setTextColor(
                        ContextCompat.getColor(
                            AppContext.context, R.color.like_tab_sel
                        )
                    )
                }
            }
        }

        tabLayout.postDelayed({
            tabViewpager?.setCurrentItem(autoShowIndex, false)
        }, 50)
    }

    /**
     * tab和vp的一些监听
     */
    private fun setTabViewPagerCallback() {
        tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                tab?.customView?.run {
                    var tabTitle = findViewById<TextView>(R.id.tv_tab_name)
                    tabTitle.isSelected = true
                    tabTitle.setTextColor(
                        ContextCompat.getColor(
                            AppContext.context, R.color.like_tab_sel
                        )
                    )

                    tabAdapter.castObject<TabAdapter>()?.getFragment(tab.position)
                        ?.castObject<BaseFragment>()?.also {
                            it.fragmentVisible(true)
                        }
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
                tab?.customView?.run {
                    var tabTitle = findViewById<TextView>(R.id.tv_tab_name)
                    tabTitle.isSelected = false
                    tabTitle.setTextColor(
                        ContextCompat.getColor(
                            AppContext.context,
                            R.color.like_tab_nor
                        )
                    )

                    tabAdapter.castObject<TabAdapter>()?.getFragment(tab.position)
                        ?.castObject<BaseFragment>()?.also {
                            it.fragmentVisible(false)
                        }
                }
            }

            override fun onTabReselected(p0: TabLayout.Tab?) {
            }
        })
    }

    /**
     * 默认先展示的tab
     */
    private fun getDefaultShowTabIndex(homeTabList: List<HomeTabEntity>): Int {
        for (index in homeTabList.indices) {
            if (homeTabList[index].default > 0) return index
        }
        return 0
    }

    /**
     * 给tab设置fragment
     */
    override fun onCreateTabFragment(tab: HomeTabEntity): Fragment? {
        return if (isAll)
            MessageSettingFragment().also { fragment ->
                fragment.arguments = Bundle().also {
                    it.putString(
                        Values.intentJsonParam,
                        MixPageEntity(tab.title, tab.url, tab.id).objToJsonString<MixPageEntity>()
                    )
                }
            }
        else
            UserMessageSettingFragment().also { fragment ->
                fragment.arguments = Bundle().also {
                    it.putString(
                        Values.intentJsonParam,
                        MixPageEntity(tab.title, tab.url, tab.id).objToJsonString<MixPageEntity>()
                    )
                    it.putString(
                        Values.userJsonParam,
                        curUser.objToJsonString<UserEntity>()
                    )
                }
            }
    }
}