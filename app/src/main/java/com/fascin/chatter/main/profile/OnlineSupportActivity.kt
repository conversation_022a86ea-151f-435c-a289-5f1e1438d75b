package com.fascin.chatter.main.profile

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.app.AlertDialog
import android.content.Intent
import android.content.pm.ActivityInfo
import android.graphics.Paint
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.Toast
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.bean.CustomerServiceIntent
import com.fascin.chatter.bean.ImMsgAction
import com.fascin.chatter.bean.event.UICSUnderCountEvent
import com.fascin.chatter.im.msg.FasPenaltiesMsg
import com.fascin.chatter.im.view.IMRefreshHeader
import com.google.gson.Gson
import com.iandroid.allclass.lib_common.UserController
import com.iandroid.allclass.lib_common.base.FasBaseActivity
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.show
import io.rong.common.RLog
import io.rong.imkit.IMCenter
import io.rong.imkit.config.RongConfigCenter
import io.rong.imkit.conversation.MessageListAdapter
import io.rong.imkit.conversation.extension.RongExtensionManager
import io.rong.imkit.conversation.messgelist.processor.IConversationUIRenderer
import io.rong.imkit.conversation.messgelist.status.MessageProcessor
import io.rong.imkit.conversation.messgelist.viewmodel.MessageViewModel
import io.rong.imkit.event.Event
import io.rong.imkit.event.uievent.PageDestroyEvent
import io.rong.imkit.event.uievent.PageEvent
import io.rong.imkit.event.uievent.ScrollEvent
import io.rong.imkit.event.uievent.ScrollMentionEvent
import io.rong.imkit.event.uievent.ScrollToEndEvent
import io.rong.imkit.event.uievent.ShowLoadMessageDialogEvent
import io.rong.imkit.event.uievent.SmoothScrollEvent
import io.rong.imkit.event.uievent.ToastEvent
import io.rong.imkit.feature.destruct.DestructManager
import io.rong.imkit.feature.mention.RongMentionManager
import io.rong.imkit.manager.SendImageManager
import io.rong.imkit.manager.SendMediaManager
import io.rong.imkit.model.UiMessage
import io.rong.imkit.picture.PictureSelector
import io.rong.imkit.picture.config.PictureMimeType
import io.rong.imkit.picture.tools.ToastUtils
import io.rong.imkit.utils.PermissionCheckUtil
import io.rong.imkit.utils.RongUtils
import io.rong.imkit.utils.keyboard.KeyboardHeightObserver
import io.rong.imkit.utils.keyboard.KeyboardHeightProvider
import io.rong.imkit.widget.FixedLinearLayoutManager
import io.rong.imkit.widget.adapter.BaseAdapter
import io.rong.imkit.widget.adapter.IViewProviderListener
import io.rong.imkit.widget.adapter.ViewHolder
import io.rong.imkit.widget.refresh.constant.RefreshState
import io.rong.imlib.RongIMClient
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.ConversationIdentifier
import io.rong.imlib.model.Message
import io.rong.message.TextMessage
import kotlinx.android.synthetic.main.activity_online_support.inputOpenMedia
import kotlinx.android.synthetic.main.activity_online_support.llCSCheck
import kotlinx.android.synthetic.main.activity_online_support.mBoardContainer
import kotlinx.android.synthetic.main.activity_online_support.mEditText
import kotlinx.android.synthetic.main.activity_online_support.mRefreshLayout
import kotlinx.android.synthetic.main.activity_online_support.mSupportRv
import kotlinx.android.synthetic.main.activity_online_support.osActiveTouchView
import kotlinx.android.synthetic.main.activity_online_support.sendBtn
import kotlinx.android.synthetic.main.activity_online_support.tvCheckPolicy
import kotlin.math.roundToLong

/**
 * @Desc: 在线客服
 * @Created: Quan
 * @Date: 2023/11/27
 */
class OnlineSupportActivity : FasBaseActivity(), IViewProviderListener<UiMessage> {

    private var permissionRequestCode = 100861
    private var mRequestCode = 100860
    private var selfId: String? = null
    private var mTargetId: String? = null
    private var conversationIdentifier: ConversationIdentifier? = null
    private var mAdapter: MessageListAdapter? = null
    private var mMessageViewModel: MessageViewModel? = null
    private var isSoftInputShow: Boolean = false
    private var bindToConversation: Boolean = false
    private var activitySoftInputMode = 0
    private var keyboardHeightProvider: KeyboardHeightProvider? = null
    private var editTextIsFocused: Boolean = false
    private var customerServiceIntent: CustomerServiceIntent? = null

    // 滑动结束是否
    private var onScrollStopRefreshList = false


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_online_support)
        initConstant()
        initStatusBarMode()
        setTitle(customerServiceIntent?.title)
        setMsgRV()
        setEditViewUI()
        setViewModel()
        setListener()
        osActiveTouchView.setOnTouchListener { v, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    AppModule.userActive()
                }
            }
            false
        }
        checkPenalty()
    }

    private fun initConstant() {
        customerServiceIntent = parseJsonParams<CustomerServiceIntent>()
        selfId = UserController.getUserId()
        mTargetId = customerServiceIntent?.accountId
        conversationIdentifier = ConversationIdentifier.obtain(Conversation.ConversationType.PRIVATE, mTargetId, "")
    }

    /**
     * 检查并跳转显示惩罚
     */
    private fun checkPenalty() {
        tvCheckPolicy.paint.flags = Paint.UNDERLINE_TEXT_FLAG
        tvCheckPolicy.paint.isAntiAlias = true
        if (customerServiceIntent?.title == getString(R.string.customer_service_quality)) {
            llCSCheck.show(true)
        }

        kotlin.runCatching {
            var currentPenaltyIndex = -1
            tvCheckPolicy.setOnClickListener {
                val penaltyItems = mAdapter?.data?.mapIndexedNotNull { index, item ->
                    if (item.message.content is FasPenaltiesMsg) index else null
                } ?: emptyList()

                if (penaltyItems.isNotEmpty()) {
                    // 获取当前可见的项的位置
                    val visiblePositions = getVisibleItemPositions(mSupportRv)

                    // 过滤掉当前可见的项
                    val filteredPenaltyItems = penaltyItems.filterNot { it in visiblePositions }

                    if (filteredPenaltyItems.isNotEmpty()) {
                        if (currentPenaltyIndex == -1 || currentPenaltyIndex >= filteredPenaltyItems.size) {
                            currentPenaltyIndex = filteredPenaltyItems.size - 1
                        }
                        val position = filteredPenaltyItems[currentPenaltyIndex]
                        mSupportRv.scrollToPosition(position)
                        currentPenaltyIndex =
                            if (currentPenaltyIndex - 1 < 0) filteredPenaltyItems.size - 1 else currentPenaltyIndex - 1
                    } else {
                        // 如果没有非可见的项，重置 currentPenaltyIndex
                        currentPenaltyIndex = -1
                    }
                }
            }
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun setMsgRV() {
        mAdapter = onResolveAdapter()
        mSupportRv?.layoutManager = createLayoutManager()

        // 关闭动画
        mSupportRv?.adapter = mAdapter
        mSupportRv?.itemAnimator = null
        mRefreshLayout.setOnTouchListener { _, _ ->
            forceSetSoftInputKeyBoard(false, clearFocus = true)
            false
        }
        mAdapter?.setItemClickListener(object : BaseAdapter.OnItemClickListener {
            override fun onItemClick(view: View, holder: ViewHolder, position: Int) {
                forceSetSoftInputKeyBoard(false, clearFocus = true)
            }

            override fun onItemLongClick(
                view: View, holder: ViewHolder, position: Int
            ): Boolean {
                return false
            }
        })
        val gd = GestureDetector(this, object : GestureDetector.SimpleOnGestureListener() {
            override fun onScroll(
                e1: MotionEvent, e2: MotionEvent, distanceX: Float, distanceY: Float
            ): Boolean {
                forceSetSoftInputKeyBoard(false, clearFocus = true)
                return super.onScroll(e1, e2, distanceX, distanceY)
            }
        })
        mSupportRv?.addOnItemTouchListener(object : RecyclerView.OnItemTouchListener {
            override fun onInterceptTouchEvent(
                rv: RecyclerView, e: MotionEvent
            ): Boolean {
                return gd.onTouchEvent(e)
            }

            override fun onTouchEvent(rv: RecyclerView, e: MotionEvent) {
                // Do nothing
            }

            override fun onRequestDisallowInterceptTouchEvent(
                disallowIntercept: Boolean
            ) {
                // Do nothing
            }
        })

        mRefreshLayout.isNestedScrollingEnabled = false
        mRefreshLayout.setRefreshHeader(IMRefreshHeader(this))
        mRefreshLayout.setEnableRefresh(true)
        mRefreshLayout.setEnableLoadMore(false)
        mSupportRv?.addOnScrollListener(mScrollListener)
    }

    private fun setEditViewUI() {
        sendBtn.isEnabled = false
        mEditText.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                if (mEditText.text.isNotEmpty()) {
                    sendBtn.isEnabled = true
                }
            } else {
                if (mEditText.text.isNullOrEmpty()) {
                    sendBtn.isEnabled = false
                }
            }
        }
        mEditText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {
                // do nothing
            }

            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
                AppModule.userActive()
                sendBtn.isEnabled = s.isNotEmpty()
                val offset = if (count == 0) {
                    -before
                } else {
                    count
                }
                if ((Conversation.ConversationType.PRIVATE
                            == conversationIdentifier?.type) && offset != 0
                ) {
                    RongIMClient.getInstance()
                        .sendTypingStatus(
                            conversationIdentifier?.type,
                            conversationIdentifier?.targetId,
                            "RC:TxtMsg"
                        )
                }
            }

            override fun afterTextChanged(s: Editable) {
                // do nothing
            }
        })
    }

    private fun setViewModel() {

        mMessageViewModel = ViewModelProvider(this).get(
            MessageViewModel::class.java
        )
        if (conversationIdentifier!!.type != null && mTargetId?.isNotEmpty() == true) {
            mMessageViewModel?.bindConversation(conversationIdentifier, null)
            bindToConversation = true
        } else {
            RLog.e(
                TAG, "Invalid intent data !!! Must put targetId and conversation type to intent."
            )
        }
        mMessageViewModel?.also {
            it.expansionLiveData.observe(this) { expansionData ->
                // 扩展字段内action处理
                expansionData.forEach { (key: String, value: String) ->
                    if (key == "data") {
                        Gson().fromJson(value, ImMsgAction::class.java)?.also { msg ->
                            routeAction(msg.action)
                        }
                    }
                }
            }

            it.pageEventLiveData.observeForever(mPageObserver)
            it.uiMessageLiveData.observeForever(mListObserver)
        }
    }

    private fun setListener() {
        mRefreshLayout.setOnRefreshListener {
            if (mMessageViewModel != null && bindToConversation) {
                mMessageViewModel!!.onRefresh()
            }
        }

        mRefreshLayout.setOnLoadMoreListener {
            if (mMessageViewModel != null && bindToConversation) {
                mMessageViewModel!!.onLoadMore()
            }
        }

        sendBtn.clickWithTrigger {
            onSendClick()
        }

        inputOpenMedia?.clickWithTrigger {
            val permissions: Array<String?> = if (RongUtils.checkSDKVersionAndTargetIsTIRAMISU(this)) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    arrayOf(
                        Manifest.permission.READ_MEDIA_IMAGES, Manifest.permission.READ_MEDIA_VIDEO
                    )
                } else {
                    arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE)
                }
            } else {
                arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE)
            }

            if (PermissionCheckUtil.checkPermissions(this, permissions)) {
                openPictureSelector()
            } else {
                PermissionCheckUtil.requestPermissions(this, permissions, permissionRequestCode)
            }
        }
    }

    private fun refreshList(data: List<UiMessage>) {
        if (!mSupportRv!!.isComputingLayout && mSupportRv!!.scrollState == RecyclerView.SCROLL_STATE_IDLE) {
            mAdapter!!.setDataCollection(data)
            IMCenter.getInstance()
                .clearMessagesUnreadStatus(
                    Conversation.ConversationType.PRIVATE,
                    customerServiceIntent?.accountId,
                    null
                )
        } else {
            onScrollStopRefreshList = true
        }
    }

    private fun createLayoutManager(): RecyclerView.LayoutManager {
        val linearLayoutManager: LinearLayoutManager = FixedLinearLayoutManager(this)
        linearLayoutManager.stackFromEnd = true
        return linearLayoutManager
    }

    /**
     * 获取 adapter. 可复写此方法实现自定义 adapter.
     *
     * @return 会话列表 adapter
     */
    private fun onResolveAdapter(): MessageListAdapter {
        return MessageListAdapter(this, null)
    }

    fun forceSetSoftInputKeyBoard(isShow: Boolean, clearFocus: Boolean) {
        if (mEditText == null) {
            return
        }
        val imm = application
            .applicationContext
            .getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
        if (isShow) {
            mEditText.requestFocus()
            imm.showSoftInput(mEditText, 0)
        } else {
            imm.hideSoftInputFromWindow(mEditText.windowToken, 0)
            if (clearFocus) {
                mEditText.clearFocus()
            }
        }
        isSoftInputShow = isShow
    }

    override fun onViewClick(clickType: Int, data: UiMessage?) {
        mMessageViewModel!!.onViewClick(clickType, data)
    }

    override fun onViewLongClick(clickType: Int, data: UiMessage?): Boolean {
        return mMessageViewModel!!.onViewLongClick(clickType, data)
    }

    private var mListObserver = Observer { uiMessages: List<UiMessage> ->
        refreshList(uiMessages)
    }

    private var mPageObserver: Observer<PageEvent> = Observer { event ->
        // 优先透传给各模块的 view 处理中心进行处理，如果返回 true, 代表事件被消费，不再处理。
        for (processor: IConversationUIRenderer in RongConfigCenter.conversationConfig().viewProcessors) {
            if (processor.handlePageEvent(event)) {
                return@Observer
            }
        }
        if (event is Event.RefreshEvent) {
            if (event.state == RefreshState.RefreshFinish) {
                mRefreshLayout.finishRefresh()
            } else if (event.state == RefreshState.LoadFinish) {
                mRefreshLayout.finishLoadMore()
            }
        } else if (event is ToastEvent) {
            val msg = event.message
            if (!TextUtils.isEmpty(msg)) {
                Toast.makeText(this, msg, Toast.LENGTH_SHORT).show()
            }
        } else if (event is ScrollToEndEvent) {
            mSupportRv.scrollToPosition(mAdapter!!.itemCount - 1)
        } else if (event is ScrollMentionEvent) {
            mMessageViewModel!!.onScrolled(
                mSupportRv, 0, 0, mAdapter!!.headersCount, mAdapter!!.footersCount
            )
        } else if (event is ScrollEvent) {
            if (mSupportRv.layoutManager is LinearLayoutManager) {
                (mSupportRv.layoutManager as LinearLayoutManager?)?.scrollToPositionWithOffset(
                    mAdapter!!.headersCount + event.position, 0
                )
            }
        } else if (event is SmoothScrollEvent) {
            if (mSupportRv.layoutManager is LinearLayoutManager) {
                (mSupportRv.layoutManager as LinearLayoutManager?)?.scrollToPositionWithOffset(
                    (mAdapter!!.headersCount + event.position), 0
                )
            }
        } else if (event is PageDestroyEvent) {
            finish()
        } else if (event is ShowLoadMessageDialogEvent) {
            showLoadMessageDialog(
                event.callback, event.list
            )
        }
    }

    private fun getVisibleItemPositions(recyclerView: RecyclerView): List<Int> {
        val layoutManager = recyclerView.layoutManager as? LinearLayoutManager
            ?: return emptyList()

        val visiblePositions = mutableListOf<Int>()

        val firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition()
        val lastVisibleItemPosition = layoutManager.findLastVisibleItemPosition()

        for (i in firstVisibleItemPosition..lastVisibleItemPosition) {
            visiblePositions.add(i)
        }

        return visiblePositions
    }


    private val mScrollListener = object : RecyclerView.OnScrollListener() {
        override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
            super.onScrolled(recyclerView, dx, dy)

            mMessageViewModel!!.onScrolled(
                recyclerView, dx, dy, mAdapter!!.headersCount, mAdapter!!.footersCount
            )
        }

        override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
            if (newState == RecyclerView.SCROLL_STATE_IDLE && onScrollStopRefreshList) {
                onScrollStopRefreshList = false
                refreshList(mMessageViewModel!!.uiMessageLiveData.value!!)
            }
        }
    }

    private val mKeyboardHeightObserver =
        KeyboardHeightObserver { orientation, isOpen, keyboardHeight ->
            if (isOpen) {
                RongUtils.saveKeyboardHeight(
                    this, orientation, keyboardHeight
                )
                updateBoardContainerHeight()
                mBoardContainer.show(true)
                mSupportRv!!.postDelayed(
                    {
                        mSupportRv.scrollToPosition(mAdapter!!.itemCount - 1)
                    }, 150
                )
            } else {
                if (isSoftInputShow) {
                    forceSetSoftInputKeyBoard(false, clearFocus = false)
                }
                mBoardContainer.show(false)
            }
        }

    private fun showLoadMessageDialog(
        callback: MessageProcessor.GetMessageCallback?, list: List<Message>
    ) {
        AlertDialog.Builder(this, AlertDialog.THEME_DEVICE_DEFAULT_LIGHT)
            .setMessage(getString(io.rong.imkit.R.string.rc_load_local_message)).setPositiveButton(
                getString(io.rong.imkit.R.string.rc_dialog_ok)
            ) { _, _ -> callback?.onSuccess(list, true) }.setNegativeButton(
                getString(io.rong.imkit.R.string.rc_cancel)
            ) { _, _ -> callback?.onErrorAsk(list) }.show()
    }

    private fun onSendClick() {
        if (TextUtils.isEmpty(mEditText.text)
            || TextUtils.isEmpty(mEditText.text.toString().trim { it <= ' ' })
        ) {
            RLog.d(TAG, "can't send empty content.")
            mEditText.setText("")
            return
        }
        val text = mEditText.text.toString()
        if (text.length > 5500) {
            ToastUtils.s(
                this,
                getString(R.string.rc_message_too_long)
            )
            RLog.d(TAG, "The text you entered is too long to send.")
            return
        }
        mEditText.setText("")
        val textMessage = TextMessage.obtain(text)
        if (DestructManager.isActive()) {
            val length = text.length
            val time: Long = if (length <= 20) {
                10
            } else {
                ((length - 20) * 0.5 + 10).roundToLong()
            }
            textMessage.isDestruct = true
            textMessage.destructTime = time
        }
        val message = Message.obtain(conversationIdentifier, textMessage)
        RongMentionManager.getInstance().onSendToggleClick(message, mEditText)
        if (RongExtensionManager.getInstance().extensionEventWatcher.size > 0) {
            for (watcher in RongExtensionManager.getInstance().extensionEventWatcher) {
                watcher.onSendToggleClick(message)
            }
        }
        IMCenter.getInstance()
            .sendMessage(
                message,
                if (DestructManager.isActive())
                    resources.getString(R.string.rc_conversation_summary_content_burn)
                else null,
                null,
                null
            )
    }

    private fun updateBoardContainerHeight() {
        if (!useKeyboardHeightProvider()) {
            return
        }
        val saveKeyboardHeight = RongUtils.getSaveKeyBoardHeight(
            this, resources.configuration.orientation
        )
        val layoutParams: ViewGroup.LayoutParams = mBoardContainer.layoutParams
        if (saveKeyboardHeight <= 0 &&
            layoutParams.height != resources.getDimensionPixelSize(R.dimen.rc_extension_board_height)
        ) {
            layoutParams.height = resources.getDimensionPixelSize(R.dimen.rc_extension_board_height)
            mBoardContainer.layoutParams = layoutParams
        } else if (layoutParams.height != saveKeyboardHeight) {
            layoutParams.height = saveKeyboardHeight
            mBoardContainer.layoutParams = layoutParams
        }
    }

    private fun useKeyboardHeightProvider(): Boolean {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            return !isInMultiWindowMode
        }
        return false
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        if (PermissionCheckUtil.checkPermissionResultIncompatible(permissions, grantResults)) {
            ToastUtils.s(this, getString(io.rong.imkit.R.string.rc_permission_request_failed))
            return
        }
        if (requestCode == permissionRequestCode) {
            if (PermissionCheckUtil.checkPermissions(this, permissions)) {
                openPictureSelector()
            } else {
                IMCenter.getInstance().postPermissionEvent(
                    this,
                    requestCode, permissions, grantResults
                )
            }
            return
        }
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
    }

    private fun openPictureSelector() {
        PictureSelector.create(this)
            .openGallery(PictureMimeType.ofAll())
            .loadImageEngine(RongConfigCenter.featureConfig().kitImageEngine)
            .setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT)
            .videoDurationLimit(RongIMClient.getInstance().videoLimitTime)
            .maxSelectNum(1)
            .actionUse(1)
            .videoDurationLimit(15)
            .previewVideo(true)
            .crop(1)
            .imageSpanCount(3)
            .isGif(true)
            .forResult(mRequestCode)
    }

    override fun onStart() {
        super.onStart()
        activitySoftInputMode = window.attributes.softInputMode
        if (useKeyboardHeightProvider()) {
            resetSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING)
        } else {
            resetSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
        }
    }

    override fun onResume() {
        super.onResume()
        if (useKeyboardHeightProvider()) {
            keyboardHeightProvider = KeyboardHeightProvider(this)
            keyboardHeightProvider?.setKeyboardHeightObserver(mKeyboardHeightObserver)
        }
        postDelayed(
            {
                keyboardHeightProvider?.start()
            }, 100
        )
        if (editTextIsFocused) {
            postDelayed(
                {
                    mEditText.setSelection(mEditText.text.toString().length)
                    forceSetSoftInputKeyBoard(true, clearFocus = true)
                },
                200
            )
        }
    }

    override fun onPause() {
        super.onPause()
        if (keyboardHeightProvider != null) {
            keyboardHeightProvider!!.stop()
            keyboardHeightProvider!!.setKeyboardHeightObserver(null)
            keyboardHeightProvider = null
        }
        mEditText?.let {
            editTextIsFocused = mEditText.isFocused
            forceSetSoftInputKeyBoard(false, clearFocus = true)
        }
    }

    override fun onStop() {
        super.onStop()
        if (mMessageViewModel != null) mMessageViewModel!!.onStop()
        resetSoftInputMode(activitySoftInputMode)
    }

    override fun onDestroy() {
        super.onDestroy()
        SimpleRxBus.post(UICSUnderCountEvent())
        mSupportRv?.removeOnScrollListener(mScrollListener)
        if (mMessageViewModel != null) {
            mMessageViewModel!!.pageEventLiveData.removeObserver(mPageObserver)
            mMessageViewModel!!.uiMessageLiveData.removeObserver(mListObserver)
            mMessageViewModel!!.onDestroy()
        }
        bindToConversation = false
        mAdapter?.onViewDestroy()
    }

    private fun resetSoftInputMode(mode: Int) {
        window?.setSoftInputMode(mode)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK && requestCode == mRequestCode && data != null) {
            // 图片、视频、音频选择结果回调
            val selectList = PictureSelector.obtainMultipleResult(data)
            if (selectList != null && selectList.size > 0) {
                val sendOrigin = selectList[0].isOriginal
                for (item in selectList) {
                    val mimeType = item.mimeType
                    if (mimeType.startsWith("image")) {
                        conversationIdentifier?.let {
                            SendImageManager.getInstance().sendImage(it, item, sendOrigin)
                            if (it.type == Conversation.ConversationType.PRIVATE) {
                                RongIMClient.getInstance().sendTypingStatus(
                                    it.type,
                                    it.targetId,
                                    "RC:ImgMsg"
                                )
                            }
                        }
                    } else if (mimeType.startsWith("video")) {
                        var path = Uri.parse(item.path)
                        if (TextUtils.isEmpty(path.scheme)) {
                            path = Uri.parse("file://" + item.path)
                        }
                        conversationIdentifier?.let {
                            SendMediaManager.getInstance().sendMedia(
                                IMCenter.getInstance().context,
                                it,
                                path,
                                item.duration
                            )
                            if (it.type == Conversation.ConversationType.PRIVATE) {
                                // 通知对方：正在输入...
                                RongIMClient.getInstance().sendTypingStatus(it.type, it.targetId, "RC:SightMsg")
                            }
                        }
                    }
                }
            }
        }
    }
}