package com.fascin.chatter.main.course

import android.content.Context
import android.graphics.Rect
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RvItem
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.utils.exts.toPx

/**
 * Course Item
 * @Created: QuanZH
 * @Date: 2023/9/20
 */
@RvItem(id = AppViewType.courseSortPlaceItemView, spanCount = 2)
class CourseSortPlaceItemView(context: Context, fm: FragmentManager, parent: ViewGroup) :
    BaseRvItemView(context, fm, parent) {

    override fun attachLayoutId(): Int {
        return R.layout.itemview_course_sort_place
    }

    override fun initView(context: Context?, view: View?) {

    }

    override fun setView() {

    }

    override fun getItemOffsets(parent: RecyclerView, view: View?, outRect: Rect, position: Int): Boolean {
        outRect.top = if(position == 0 || position == 1) 12.toPx else 0
        outRect.left = if (position % 2 == 0) 16.toPx else 12.toPx
        outRect.right = if (position % 2 == 0) 0 else 16.toPx
        outRect.bottom = 12.toPx
        return true
    }

}