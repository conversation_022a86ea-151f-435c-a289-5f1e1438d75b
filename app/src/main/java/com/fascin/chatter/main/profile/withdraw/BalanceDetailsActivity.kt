package com.fascin.chatter.main.profile.withdraw

import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.config.TabConfig
import com.fascin.chatter.dialog.SelDateDialog
import com.iandroid.allclass.lib_basecore.utils.DateUtils
import com.iandroid.allclass.lib_common.base.ChatterBaseActivity
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.textColorResource
import kotlinx.android.synthetic.main.activity_balance_details.btnDateSel
import kotlinx.android.synthetic.main.activity_balance_details.btnDeduct
import kotlinx.android.synthetic.main.activity_balance_details.btnIncome
import kotlinx.android.synthetic.main.activity_balance_details.btnWithdraw
import kotlinx.android.synthetic.main.activity_balance_details.vpBalance

/**
 * Created by: LXL
 * Date: 2024/10/10
 * Time: 14:09
 * 全部余额记录
 */
class BalanceDetailsActivity : ChatterBaseActivity() {

//    private var incomeFragment: BalanceFragment? = null

    private var incomeFragment: IncomeAggregationFragment? = null

    private var withdrawFragment: BalanceFragment? = null
    private var deductFragment: BalanceFragment? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_balance_details)
        setTitle(R.string.withdraw_balance_details)
        AppModule.userActive()
        initTabView()
        intViewPager()
    }

    private fun initTabView() {
        val queryTime = DateUtils.getFirstDayOfMonthTimestamp()
        incomeFragment = IncomeAggregationFragment().apply {
            setTabId(TabConfig.balanceIncome)
            setQueryTime(queryTime)
        }
        withdrawFragment = BalanceFragment().apply {
            setTabId(TabConfig.balanceWithdraw)
            setQueryTime(queryTime)
        }
        deductFragment = BalanceFragment().apply {
            setTabId(TabConfig.balanceDeduct)
            setQueryTime(queryTime)
        }
        btnDateSel.text = DateUtils.formatDate(queryTime, "MM/yyyy")
        selTab(0)
        btnDateSel.clickWithTrigger {
            SelDateDialog { year, mon ->
                btnDateSel.text = buildString {
                    append(mon)
                    append("/")
                    append(year)
                }
                val formatDate = DateUtils.formatDate("$year/$mon/01", "yyyy/MM/dd")
                incomeFragment?.setQueryTime(formatDate, true)
                withdrawFragment?.setQueryTime(formatDate, true)
                deductFragment?.setQueryTime(formatDate, true)
            }.show(supportFragmentManager, SelDateDialog::javaClass.name)
        }
        btnIncome.clickWithTrigger {
            selTab(0)
        }
        btnWithdraw.clickWithTrigger {
            selTab(1)
        }
        btnDeduct.clickWithTrigger {
            selTab(2)
        }
    }

    private fun intViewPager() = with(vpBalance) {
        adapter = BalancePageAdapter(
            supportFragmentManager, lifecycle, arrayListOf(incomeFragment!!, withdrawFragment!!, deductFragment!!)
        )
        isUserInputEnabled = false
    }

    private fun selTab(position: Int) {
        vpBalance.setCurrentItem(position, false)
        btnIncome.textColorResource = R.color.black
        btnWithdraw.textColorResource = R.color.black
        btnDeduct.textColorResource = R.color.black
        btnIncome.setStrokeWidth(1f)
        btnWithdraw.setStrokeWidth(1f)
        btnDeduct.setStrokeWidth(1f)
        btnIncome.setBackgroundResource(R.color.white)
        btnWithdraw.setBackgroundResource(R.color.white)
        btnDeduct.setBackgroundResource(R.color.white)
        when (position) {
            0 -> {
                btnIncome.textColorResource = R.color.white
                btnIncome.setBackgroundResource(R.color.black)
                btnIncome.setStrokeWidth(0f)
            }

            1 -> {
                btnWithdraw.textColorResource = R.color.white
                btnWithdraw.setBackgroundResource(R.color.black)
                btnWithdraw.setStrokeWidth(0f)
            }

            2 -> {
                btnDeduct.textColorResource = R.color.white
                btnDeduct.setBackgroundResource(R.color.black)
                btnDeduct.setStrokeWidth(0f)
            }
        }
    }

    class BalancePageAdapter(
        fragmentManager: FragmentManager,
        lifecycle: Lifecycle,
        private val fragments: ArrayList<Fragment> = arrayListOf()
    ) : FragmentStateAdapter(fragmentManager, lifecycle) {
        fun getFragment(position: Int): Fragment = fragments[position]

        override fun getItemCount(): Int {
            return fragments.size
        }

        override fun createFragment(position: Int): Fragment {
            return fragments[position]
        }
    }
}