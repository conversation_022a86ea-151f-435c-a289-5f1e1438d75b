package com.fascin.chatter.main.profile.view

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.fascin.chatter.R
import com.fascin.chatter.main.profile.IMessageSettingRvItemAction
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RvItem
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.beans.MsgContentEntity
import com.iandroid.allclass.lib_common.utils.exts.castObject
import com.iandroid.allclass.lib_common.utils.exts.toPx
import kotlinx.android.synthetic.main.itemview_message_setting_content.view.btnDelete
import kotlinx.android.synthetic.main.itemview_message_setting_content.view.btnModify
import kotlinx.android.synthetic.main.itemview_message_setting_content.view.content
import kotlinx.android.synthetic.main.itemview_message_setting_content.view.tipExamine
import kotlinx.android.synthetic.main.itemview_message_setting_content.view.tipReject


/**
 * MessageSetting Item
 * @Created: QuanZH
 * @Date: 2023/7/11
 */
@RvItem(id = AppViewType.userMessageSettingItemView, spanCount = 1)
class UserMessageSettingItemView(context: Context, fm: FragmentManager, parent: ViewGroup) :
    BaseRvItemView(context, fm, parent) {

    override fun attachLayoutId(): Int {
        return R.layout.itemview_message_setting_content
    }

    override fun initView(context: Context?, view: View?) {

    }

    override fun setView() {
        val entity = getItemData() ?: return
        // 设置item的边距
        setMargins(
            view,
            16.toPx,
            8.toPx,
            16.toPx,
            // 给最后一个item设置底边距，以防被页面底部add按钮遮挡
            if (getPosition() == getItemCounts() - 1) 164.toPx else 0
        )
        with(entity) {
            itemView?.content?.text = content
            val passStatus = status == MsgContentEntity.EXAMINED
            val examineStatus = status == MsgContentEntity.IN_REVIEW
            val rejectStatus = status == MsgContentEntity.REJECT
            // 根据审核状态设置按钮的显隐
            itemView?.btnDelete?.visibility = if (passStatus || rejectStatus) View.VISIBLE else View.GONE
            itemView?.btnModify?.visibility = if (passStatus || rejectStatus) View.VISIBLE else View.GONE
            itemView?.tipExamine?.visibility = if (examineStatus) View.VISIBLE else View.GONE
            itemView?.tipReject?.visibility = if (rejectStatus) View.VISIBLE else View.GONE

            itemView?.btnDelete?.setOnClickListener {
                // 删除消息
                getAction()?.delMessage(id, mid)
            }
            itemView?.btnModify?.setOnClickListener {
                // 修改消息
                getAction()?.modifyMessage(id, content, mid, "")
            }
        }

    }

    private fun getItemData(): MsgContentEntity? = data?.castObject<MsgContentEntity>()

    private fun getAction(): IMessageSettingRvItemAction? {
        return info?.callBack?.castObject<IMessageSettingRvItemAction>()
    }

}