package com.fascin.chatter.main.schedule.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.bean.ScheduleDateItemEntity
import com.iandroid.allclass.lib_common.utils.exts.show
import kotlinx.android.synthetic.main.itemview_left_date.view.tvHot
import kotlinx.android.synthetic.main.itemview_left_date.view.tvTime

/**
 * @Desc: 排班预览左侧时间列表
 * @Created: Quan
 * @Date: 2024/11/18
 */
class LeftTimeAdapter : RecyclerView.Adapter<LeftTimeAdapter.ViewHolder>() {

    private val dataList = mutableListOf<ScheduleDateItemEntity>()

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

    fun updateData(data: List<ScheduleDateItemEntity>?) {
        dataList.clear()
        if (!data.isNullOrEmpty()) {
            dataList.addAll(data)
        }
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.itemview_left_date, parent, false)
        )

    }

    override fun getItemCount(): Int = dataList.size

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = dataList[position]
        holder.itemView?.also {
            it.tvTime.text = item.shiftDate
            it.tvHot.show(item.isHot)
        }
    }
}