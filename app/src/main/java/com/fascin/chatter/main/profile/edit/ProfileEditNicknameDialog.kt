package com.fascin.chatter.main.profile.edit

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.R
import com.fascin.chatter.component.views.SUButtonStatus
import com.fascin.chatter.main.profile.ProfileViewModel
import com.iandroid.allclass.lib_common.base.BaseDialogFragment
import com.iandroid.allclass.lib_common.beans.ProfileParams
import com.iandroid.allclass.lib_common.beans.ProfileSettingStep
import com.iandroid.allclass.lib_common.utils.keyboard.KeyboardUtils
import kotlinx.android.synthetic.main.dialog_profile_edit_nickname.*

class ProfileEditNicknameDialog(
    private val completeBlock: () -> Unit
): BaseDialogFragment() {

    private val viewModel by lazy {
        ViewModelProvider(
            this,
            ProfileViewModel.ViewModeFactory()
        )[ProfileViewModel::class.java]
    }
    private val profileParams by lazy { ProfileParams.get().apply {
        resetByCache()
    } }

    override fun onStart() {
        super.onStart()
        setBottomPopupAttr(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
        )
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_profile_edit_nickname, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        profile_edit_nickname_root.setOnClickListener {
            dismissAllowingStateLoss()
        }
        profile_edit_nickname_input.addTextChangedListener {
            val value = profile_edit_nickname_input.text.toString()

            if (!profile_edit_nickname_save.isLoadingStatus()) {
                profile_edit_nickname_save.setButtonStatus(
                    if (value.isNullOrEmpty()) SUButtonStatus.Disabled else SUButtonStatus.Activated
                )
            }
        }
        profile_edit_nickname_input.setText(profileParams.nickname)
        profile_edit_nickname_input.setSelection(profileParams.nickname.length)
        profile_edit_nickname_save.setText(getString(R.string.profile_edit_save))
        profile_edit_nickname_save.setOnClickListener {

            KeyboardUtils.hideKeyboard(profile_edit_nickname_input)
            profile_edit_nickname_input.isEnabled = false
            profile_edit_nickname_save.setButtonStatus(SUButtonStatus.Loading)

            profileParams.step = ProfileSettingStep.Nickname
            profileParams.nickname = profile_edit_nickname_input.text.toString()
           // viewModel.updateProfileSetting(profileParams)

        }

        profile_edit_nickname_input.postDelayed( {
            KeyboardUtils.showKeyboard(profile_edit_nickname_input)
        }, 200L)

        viewModel.profileSettingResult.observe(this) {
            endProgressButtonLoading()
            dismissAllowingStateLoss()
            completeBlock()
        }
        viewModel.profileSettingError.observe(this) {
            endProgressButtonLoading()
        }
    }

    private fun endProgressButtonLoading() {
        val value = profile_edit_nickname_input.text.toString()
        profile_edit_nickname_save.setButtonStatus(
            if (value.isNullOrEmpty()) SUButtonStatus.Disabled else SUButtonStatus.Activated
        )
        profile_edit_nickname_input.isEnabled = true
    }

}