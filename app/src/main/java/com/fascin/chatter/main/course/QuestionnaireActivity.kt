package com.fascin.chatter.main.course

import android.graphics.Color
import android.os.Bundle
import android.os.CountDownTimer
import androidx.core.text.buildSpannedString
import androidx.core.text.color
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.bean.CourseEntity
import com.fascin.chatter.bean.EmptyEntity
import com.fascin.chatter.bean.ExaminationEntity
import com.fascin.chatter.bean.QuestionEntity
import com.fascin.chatter.bean.SubmitAnswerEntity
import com.fascin.chatter.component.views.SUButtonStatus
import com.fascin.chatter.main.IRvItemAction
import com.fascin.chatter.utils.SUStringUtils
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemInfo
import com.iandroid.allclass.lib_basecore.view.recyclerview.RecyclerViewSupport
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.base.ChatterBaseActivity
import com.iandroid.allclass.lib_common.beans.EventCourseChange
import com.iandroid.allclass.lib_common.event.CourseCategoryPassEvent
import com.iandroid.allclass.lib_common.event.CourseChangeEvent
import com.iandroid.allclass.lib_common.event.UINewAnchorTaskStatusEvent
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.ToastUtils
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.isVisible
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.toPx
import kotlinx.android.synthetic.main.activity_questionnaire.btnLesson
import kotlinx.android.synthetic.main.activity_questionnaire.btnSubmit
import kotlinx.android.synthetic.main.activity_questionnaire.btnTryAgain
import kotlinx.android.synthetic.main.activity_questionnaire.ivScore
import kotlinx.android.synthetic.main.activity_questionnaire.questionRv
import kotlinx.android.synthetic.main.activity_questionnaire.rlQuestion
import kotlinx.android.synthetic.main.activity_questionnaire.rlScore
import kotlinx.android.synthetic.main.activity_questionnaire.scrollView
import kotlinx.android.synthetic.main.activity_questionnaire.tvIncorrect
import kotlinx.android.synthetic.main.activity_questionnaire.tvPoint
import kotlinx.android.synthetic.main.activity_questionnaire.tvPointDesc
import kotlinx.android.synthetic.main.activity_questionnaire.tvWrongNum
import java.util.TreeMap

/**
 * @Desc:答卷页面
 * @Created: Quan
 * @Date: 2023/9/21
 */
class QuestionnaireActivity : ChatterBaseActivity(), AnswerChangeAction, IRvItemAction {

    private val viewModel by lazy {
        ViewModelProvider(
            this,
            CourseViewModel.ViewModeFactory()
        )[CourseViewModel::class.java]
    }

    private var courseEntity: CourseEntity? = null
    private var examination: ExaminationEntity? = null
    private var recyclerViewSupport: RecyclerViewSupport? = null

    // 由于后台没返题号，所以需将答案按key排序,TreeMap默认升序排列
    private var answerMap = TreeMap<Int, List<Int>>()
    private var lessonShowed = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_questionnaire)
    }

    override fun initBaseContent() {
        super.initBaseContent()
        AppModule.userActive()
        courseEntity = parseJsonParams<CourseEntity>()
        showTitleBar(true)
        setTitle(courseEntity?.title)
        initRv()
        setPreView()
        setListener()
    }

    private fun initRv() {
        recyclerViewSupport =
            RecyclerViewSupport(supportFragmentManager, questionRv, null).also {
                it.setCanPullDown(false)
                it.setCanPullUp(false)
            }
    }

    private fun setPreView() {
        courseEntity?.let {
            // 是否答题过
            if (it.status == CourseEntity.STATUS_PASS || it.try_num > 0 && it.try_num >= it.max_num) {
                // 显示上次答题的结果
                setScoreUI()
            } else {
                // 显示试题骨骼，获取试卷
                rlScore.show(false)
                btnLesson.show(false)
                rlQuestion.show(true)
                updateData(
                    ArrayList<BaseRvItemInfo?>().also { list ->
                        for (i in 0..4) {
                            list.add(BaseRvItemInfo(Any(), AppViewType.questionPlaceItemView, this))
                        }
                    }
                )
                fetchPageData()
            }
        }
    }

    private fun setListener() {
        btnSubmit.clickWithTrigger {
            AppModule.userActive()
            if (examination?.questions?.isNotEmpty() == true
                && examination?.questions?.size == answerMap.size
            ) {
                examination?.let {
                    btnSubmit.setButtonStatus(SUButtonStatus.Loading)
                    viewModel.submitAnswers(
                        it.id,
                        it.version,
                        spliceAnswers()
                    )
                }
            }
        }

        btnTryAgain.clickWithTrigger {
            AppModule.userActive()
            courseEntity?.let {
                if (it.max_num <= it.try_num) {
                    // 没有答题次数时，toast提示
                    ToastUtils.showToast(getString(R.string.no_remaining_times))
                } else {
                    fetchPageData()
                }
            }
        }

        btnLesson.clickWithTrigger {
            AppModule.userActive()
            // 跳转课程详情
//            if (courseEntity?.h5Url?.isNotEmpty() == true) {
//                routeAction(ActionType.actionTypeToWebActivity) {
//                    it.param = WebIntent().also { webIntent ->
//                        webIntent.showTitle = true
//                        webIntent.url = courseEntity?.h5Url
//                    }
//                }
//            } else {
//                ToastUtils.showToast("Course content url error")
//            }
            finish()
        }

        scrollView.viewTreeObserver.addOnScrollChangedListener {
            bottomBtnShow()
        }

        viewModel.questionsResult.observe(this) {
            examination = it
            answerMap.clear()
            rlScore.show(false)
            btnLesson.show(false)
            rlQuestion.show(true)
            if (it.questions?.isNotEmpty() == true) {
                updateData(
                    ArrayList<BaseRvItemInfo?>().also { list ->
                        it.questions?.forEach { question ->
                            list.add(BaseRvItemInfo(question, AppViewType.questionItemView, this))
                        }
                    }, true
                )
            } else {
                addEmptyView()
            }
        }

        viewModel.questionsError.observe(this) {
            // 不合格，再次获取试卷请求失败时
            if (courseEntity == null || courseEntity?.try_num!! <= 0) {
                addErrorView()
            } else {
                // 展示请求前的分数视图
                rlQuestion.show(false)
                rlScore.show(true)
            }
        }

        viewModel.submitAnswersResult.observe(this) {
            if (courseEntity == null) courseEntity = CourseEntity()
            courseEntity?.also { course ->
                course.status =
                    if (it.pass == SubmitAnswerEntity.STATUS_PASS) CourseEntity.STATUS_PASS
                    else CourseEntity.STATUS_NOT_PASS
                course.last_score = it.score
                course.try_num = it.tryNum
                course.max_num = it.maxNum
                course.passScore = it.passScore
                course.lastAnswer = it.lastAnswer
            }
            // 将新的成绩展示
            setScoreUI()
            // 通知课程列表页修改状态
            SimpleRxBus.post(CourseChangeEvent(courseEntity))
            if (it.pass == SubmitAnswerEntity.STATUS_PASS) {
                SimpleRxBus.post(CourseCategoryPassEvent())
            }
            //新主播Quiz全部通过
            if (it.categoryPass == 1) {
                SimpleRxBus.post(EventCourseChange())
                SimpleRxBus.post(UINewAnchorTaskStatusEvent())
                AppContext.finishActivity(CourseListActivity::class.java)
//                finish()
            }
            answerMap.clear()
        }

        viewModel.submitAnswersError.observe(this) {
            setCanSubmit()
        }
    }

    private fun fetchPageData() {
        btnSubmit.setButtonStatus(SUButtonStatus.Disabled)
        courseEntity?.id?.let {
            viewModel.getQuestions(it)
        }
    }

    private fun setScoreUI() {
        rlQuestion.show(false)
        rlScore.show(true)
        bottomBtnShow()
        courseEntity?.let {
            val isPass = it.status == CourseEntity.STATUS_PASS
            ivScore.setImageResource(
                if (isPass) R.mipmap.ic_score_pass
                else R.mipmap.ic_score_not_pass
            )
            tvPoint.text = String.format(
                getString(
                    if (!isPass) R.string.score_failed_points
                    else R.string.score_pass_points
                ), it.last_score
            )
            tvPointDesc.show(!isPass)
//            btnTryAgain.show(it.status != CourseEntity.STATUS_PASS)
//            btnLesson.show(it.status != CourseEntity.STATUS_PASS)
            if (!isPass) buildWrongQuestion(it.lastAnswer)
            val surplusNum = it.max_num - it.try_num
            //富文本
            tvPointDesc.text = buildSpannedString {
                append(it.passScore.toString())
                append(" points will pass it, you still have ")
                color(Color.BLACK) {
                    append(if (surplusNum > 0) surplusNum.toString() else "0")
                    append(" chances")
                }
                append(" to take the quiz.")
            }
        }
    }

    private fun updateData(itemTemp: ArrayList<BaseRvItemInfo?>, needClear: Boolean = true) {
        recyclerViewSupport?.updateData(itemTemp, needClear)
    }

    override fun onChange(id: Int, answers: List<Int>) {
        // 答题选择发生变化
        answerMap[id] = answers
        // 未选中任何选项时，从map中移除
        if (answers.isEmpty())
            answerMap.remove(id)
        setCanSubmit()
    }

    private fun setCanSubmit() {
        // 题目不为空 & 题目数和答案数对得上时
        if (examination?.questions?.isNotEmpty() == true
            && examination?.questions?.size == answerMap.size
        ) {
            // 正在提交时，不能重置
            if (btnSubmit.getButtonStatus() != SUButtonStatus.Loading) {
                btnSubmit.setButtonStatus(SUButtonStatus.Activated)
            }
        } else {
            btnSubmit.setButtonStatus(SUButtonStatus.Disabled)
        }
    }

    private fun bottomBtnShow() {
        courseEntity?.let { course ->
            if (!rlQuestion.isVisible() && !lessonShowed && course.status == CourseEntity.STATUS_NOT_PASS) {
                val view = scrollView.getChildAt(0)
                if ((view.bottom - 60.toPx) <= (scrollView.height + scrollView.scrollY)) {
                    // 滑到底部了
                    lessonShowed = true
                    btnLesson.isEnabled = false
                    btnLesson.show(true)
                    btnLesson.text = buildString {
                        append(getString(R.string.btn_course_lesson))
                        append("(60s)")
                    }
                    setLessonText()
                }
            }
        }
    }

    private fun setLessonText() {
        //modifier: 2025.08.05 修改为10s
        object : CountDownTimer(10000, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                // 更新倒计时时间，每秒调用一次
                val secondsRemaining = millisUntilFinished / 1000
                btnLesson.setBackgroundResource(R.drawable.bg_d9d9d9_r16)
                btnLesson.text = buildString {
                    append(getString(R.string.btn_course_lesson))
                    append("(")
                    append(secondsRemaining)
                    append("s)")
                }
            }

            override fun onFinish() {
                // 倒计时结束时的处理
                btnLesson.text = getString(R.string.btn_course_lesson)
                btnLesson.setBackgroundResource(com.iandroid.allclass.lib_common.R.drawable.bg_262626_r16)
                btnLesson.isEnabled = true
            }
        }.start()
    }

    /**
     * 将选择的答案转成json字符串
     * 格式：[{"1":[1]},{"2":[1,3]}]，key为题目id，value为答案id列表
     */
    private fun spliceAnswers(): String {
        return answerMap.entries.joinToString(prefix = "[", postfix = "]") { entry ->
            val key = entry.key
            val valueList = entry.value.joinToString(prefix = "[", postfix = "]") { it.toString() }
            """{"$key":$valueList}"""
        }
    }

    private fun buildWrongQuestion(lastAnswer: List<QuestionEntity>?) {
        var wrongQuestionText = ""
        lastAnswer?.let { wrongs ->
            tvWrongNum.text = buildString {
                append("Incorrect Answers :")
                append(" ${lastAnswer.size}")
            }
            wrongQuestionText = buildString {
                wrongs.forEachIndexed { index, wrong ->
                    append(index + 1)
                    append(". ")
                    append(wrong.title)
                    append("\n")
                    wrong.let { question ->
                        question.answers?.forEachIndexed { index, answer ->
                            append(SUStringUtils.indexToABC(index))
                            append(") ")
                            append(answer)
                            append("\n")
                        }
                        append("Your Answer: ")
                        question.youAnswer.forEach {
                            append(SUStringUtils.indexToABC(it))
                        }
                        append("\n")
                    }
                    append("\n")
                }
            }
        }
        tvIncorrect.text = wrongQuestionText
        tvIncorrect.show(wrongQuestionText.isNotEmpty())
    }

    private fun addEmptyView() {
        val emptyEntity = EmptyEntity().also {
            it.title = getString(R.string.page_data_tips)
            it.icRes = R.mipmap.ic_msg_setting_nodata
        }

        emptyEntity.also { data ->
            updateData(
                ArrayList<BaseRvItemInfo?>().also {
                    it.add(BaseRvItemInfo(data, AppViewType.comEmptyView, this))
                }
            )
        }
    }

    private fun addErrorView() {
        updateData(
            ArrayList<BaseRvItemInfo?>().also {
                it.add(BaseRvItemInfo(Any(), AppViewType.exceptionView, this))
            }
        )
    }

    override fun onRefresh() {
        setPreView()
    }
}