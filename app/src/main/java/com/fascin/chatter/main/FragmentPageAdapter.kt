package com.fascin.chatter.main

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.fascin.chatter.main.chats.ConversationFragment
import com.fascin.chatter.main.course.CourseSortFragment
import com.fascin.chatter.main.profile.ProfileFragment
import com.fascin.chatter.main.task.TaskCatalogFragment

class FragmentPageAdapter(
    fragmentManager: FragmentManager,
    lifecycle: Lifecycle
) : FragmentStateAdapter(fragmentManager, lifecycle) {

    private val fragments = listOf(
        ConversationFragment(),
        //ChatsFragment(),
//        MatchFragment(),
        TaskCatalogFragment(),
//        CourseListFragment(),
        //TODO close by mask
//        CourseSortFragment(),
        ProfileFragment()
    )

    fun getFragment(position: Int): Fragment = fragments[position]

    override fun getItemCount(): Int {
        return fragments.size
    }

    override fun createFragment(position: Int): Fragment {
        return fragments[position]
    }
}