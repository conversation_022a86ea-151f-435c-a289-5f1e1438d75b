package com.fascin.chatter.main.profile

import android.graphics.Color
import android.graphics.Typeface
import android.os.Bundle
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.bean.MatchPolicyEntity
import com.fascin.chatter.bean.event.UIMatchTrafficStatusEvent
import com.iandroid.allclass.lib_basecore.utils.SpanUtil
import com.iandroid.allclass.lib_common.base.ChatterBaseActivity
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import kotlinx.android.synthetic.main.activity_traffic.trafficSwitch
import kotlinx.android.synthetic.main.activity_traffic.tv_traffic_content

/**
 *  @author: LXL
 *  @description: 流量设置
 *  @date: 2024/3/13 17:23
 */
class TrafficActivity : ChatterBaseActivity() {
    private var matchPolicyEntity: MatchPolicyEntity? = null
    private val viewModel by lazy {
        ViewModelProvider(
            this,
            ProfileViewModel.ViewModeFactory()
        )[ProfileViewModel::class.java]
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_traffic)

        //当前页面收到推送||弹窗设置开启 修改开关状态
        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(UIMatchTrafficStatusEvent::class) {
            trafficSwitch.isChecked = it.switchStatus
        })
    }

    override fun initBaseContent() {
        super.initBaseContent()
        AppModule.userActive()
        setTitle(R.string.profile_traffic)
        matchPolicyEntity = parseJsonParams<MatchPolicyEntity>()

        trafficSwitch.isChecked = matchPolicyEntity?.leaveTime != 0L
        val minutes = "${matchPolicyEntity?.time} minutes."
        SpanUtil.create()
            .addForeColorSection(getString(R.string.profile_traffic_content), Color.parseColor("#8C8C8C"))
            .addForeColorSection(minutes, Color.parseColor("#F5222D"))
            .setStyle(minutes, Typeface.BOLD)
            .showIn(tv_traffic_content)


        trafficSwitch.setOnCheckedChangeListener { buttonView, isChecked ->
            //防止初始化时触发监听
            if (!buttonView.isPressed) return@setOnCheckedChangeListener
            trafficSwitch.isEnabled = false
            viewModel.matchPolicySet(if (isChecked) 1 else 0) {
                trafficSwitch.isEnabled = true
            }
        }
    }
}