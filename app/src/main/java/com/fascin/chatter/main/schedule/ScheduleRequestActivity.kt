package com.fascin.chatter.main.schedule

import android.os.Bundle
import com.fascin.chatter.R
import com.iandroid.allclass.lib_common.base.ChatterBaseActivity
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import kotlinx.android.synthetic.main.activity_schedule_request.leaveRecordsView
import kotlinx.android.synthetic.main.activity_schedule_request.requestBack
import kotlinx.android.synthetic.main.activity_schedule_request.requestLeaveView
import kotlinx.android.synthetic.main.activity_schedule_request.shiftChangeView

/**
 * @Desc: 排班功能页
 * @Created: Quan
 * @Date: 2024/11/18
 */
class ScheduleRequestActivity : ChatterBaseActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_schedule_request)
    }

    override fun initBaseContent() {
        super.initBaseContent()
        setViewUI()
        setListener()
    }

    private fun setViewUI() {
        requestLeaveView.setTitle(getString(R.string.text_request_leave))
        shiftChangeView.setTitle(getString(R.string.text_shift_change))
        leaveRecordsView.setTitle(getString(R.string.text_leave_records))
    }

    private fun setListener() {
        requestBack.clickWithTrigger {
            finish()
        }
        requestLeaveView.clickWithTrigger {
            routeAction(ActionType.actionScheduleRequestLeave)
        }

        shiftChangeView.clickWithTrigger {
            routeAction(ActionType.actionScheduleShiftChange)
        }

        leaveRecordsView.clickWithTrigger {
            routeAction(ActionType.actionScheduleLeaveRecords)
        }
    }

    override fun immersiveIntoStatusBar(): Boolean {
        return false
    }

    override fun isShowTitleBar(): Boolean {
        return false
    }
}