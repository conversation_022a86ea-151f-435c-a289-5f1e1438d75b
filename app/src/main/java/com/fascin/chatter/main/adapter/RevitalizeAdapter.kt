package com.fascin.chatter.main.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.bean.OnlineTagEntity
import com.fascin.chatter.bean.RevitalizeItemEntity
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.GlideLoader.loadImage
import com.iandroid.allclass.lib_common.beans.UserEntity
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import kotlinx.android.synthetic.main.item_revitalize.view.ivModel
import kotlinx.android.synthetic.main.item_revitalize.view.ivSelect
import kotlinx.android.synthetic.main.item_revitalize.view.ivUser
import kotlinx.android.synthetic.main.item_revitalize.view.tvAge
import kotlinx.android.synthetic.main.item_revitalize.view.tvName
import kotlinx.android.synthetic.main.item_revitalize.view.userTags

/**
 * @Desc: 会员老用户召回弹窗适配器
 * @Created: Quan
 * @Date: 2024/10/18
 */
class RevitalizeAdapter : RecyclerView.Adapter<RevitalizeAdapter.ViewHolder>() {

    private val dataList = mutableListOf<RevitalizeItemEntity>()
    var selectIds: ArrayList<String> = arrayListOf<String>()// 选中的model id

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

    fun updateData(data: List<RevitalizeItemEntity>?) {
        dataList.clear()
        selectIds.clear()
        if (!data.isNullOrEmpty()) {
            dataList.addAll(data)
            // 默认全选
            selectIds.addAll(data.map { it.targetId })
        }
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutInflater.from(parent.context).inflate(
                R.layout.item_revitalize,
                parent,
                false
            )
        )
    }

    override fun getItemCount(): Int {
        return dataList.size
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = dataList[position]
        holder.itemView.run {
            ivUser.loadImage(AppContext.context, item.userEntity.avatarUrl)
            ivModel.loadImage(AppContext.context, item.modelAvatar)
            userTags.setOnlineTag(getTagsEntity(item.userEntity))
            tvName.text = item.userEntity.nickname
            if (item.userEntity.age > 0) tvAge.text = buildString {
                append(",")
                append(item.userEntity.age)
            }
            ivSelect.setImageResource(
                if (selectIds.contains(item.targetId)) R.drawable.ic_revitalize_select
                else R.drawable.ic_revitalize_unselect
            )
            clickWithTrigger {
                if (!selectIds.contains(item.targetId)) {
                    selectIds.add(item.targetId)
                } else {
                    selectIds.remove(item.targetId)
                }
                ivSelect.setImageResource(
                    if (selectIds.contains(item.targetId)) R.drawable.ic_revitalize_select
                    else R.drawable.ic_revitalize_unselect
                )
            }
        }
    }

    private fun getTagsEntity(user: UserEntity): OnlineTagEntity {
        return OnlineTagEntity().apply {
            this.imid = user.imId
            this.nickName = user.nickname
            this.vip = user.vip
            this.prime = user.tagPrime
            this.rising = user.tagRising
            this.unlock = user.unlockNum
        }
    }
}