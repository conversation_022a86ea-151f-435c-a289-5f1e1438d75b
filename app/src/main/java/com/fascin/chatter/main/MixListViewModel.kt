package com.fascin.chatter.main

import androidx.lifecycle.MutableLiveData
import com.fascin.chatter.repository.HomeRepository
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemInfo
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.Values
import com.iandroid.allclass.lib_common.base.BaseViewModel
import com.iandroid.allclass.lib_common.beans.MixBaseRvItemInfo
import com.iandroid.allclass.lib_common.network.ErrorCodeCheckUtils

/**
created by  kun
on 2020/9/12.
 */
class MixListViewModel : BaseViewModel() {
    val mixBaseRvItemInfo = MutableLiveData<MixBaseRvItemInfo>()

    fun fetchMixData(
        url: String,
        page: Int = Values.fristPage,
        callBack: Any? = null
    ) {
        compositeDisposable?.add(
            HomeRepository.fetchMixData(url, page).subscribe({ mixData ->
                mixBaseRvItemInfo.postValue(MixBaseRvItemInfo(mixData?.run {
                    val mixViewDataList = ArrayList<BaseRvItemInfo>()
                    for (item in this.likeUserList.orEmpty()) {

                    }
                    mixViewDataList
                }, page))
            }, {
                apiErrMsg.postValue(ErrorCodeCheckUtils.getError(it))
            })
        )

    }

    /**
     * 获取messageSet列表数据
     */
    fun fetchMessageSettingMixData(
        url: String,
        type: Int,
        callBack: Any? = null
    ) {
        compositeDisposable?.add(
            HomeRepository.fetchMessageSettingMixData(url).subscribe({ mixData ->
                mixBaseRvItemInfo.postValue(MixBaseRvItemInfo(mixData?.run {
                    val mixViewDataList = ArrayList<BaseRvItemInfo>()
                    for (item in this) {
                        mixViewDataList.add(
                            BaseRvItemInfo(
                                item.also {
                                    item.type = type
                                },
                                AppViewType.messageSettingItemView,
                                callBack
                            )
                        )
                    }
                    mixViewDataList
                }, 1))
            }, {
                apiErrMsg.postValue(ErrorCodeCheckUtils.getError(it))
            })
        )
    }

    /**
     * 获取messageSet指定model列表数据
     */
    fun fetchUserMessageSettingMixData(
        url: String,
        userId: String,
        callBack: Any? = null
    ) {
        compositeDisposable?.add(
            HomeRepository.fetchUserMessageSettingMixData(url, userId).subscribe({ mixData ->
                mixBaseRvItemInfo.postValue(MixBaseRvItemInfo(mixData?.run {
                    val mixViewDataList = ArrayList<BaseRvItemInfo>()
                    for (item in this) {
                        mixViewDataList.add(
                            BaseRvItemInfo(
                                item.also {
                                    it.mid = userId
                                },
                                AppViewType.userMessageSettingItemView,
                                callBack
                            )
                        )
                    }
                    mixViewDataList
                }, 1))
            }, {
                apiErrMsg.postValue(ErrorCodeCheckUtils.getError(it))
            })
        )
    }

}
