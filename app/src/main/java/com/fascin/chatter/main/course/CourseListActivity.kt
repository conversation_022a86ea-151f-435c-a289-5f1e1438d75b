package com.fascin.chatter.main.course

import android.os.Bundle
import androidx.core.widget.NestedScrollView.OnScrollChangeListener
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.bean.CourseEntity
import com.fascin.chatter.bean.CourseListIntent
import com.fascin.chatter.bean.EmptyEntity
import com.fascin.chatter.main.IRvItemAction
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemInfo
import com.iandroid.allclass.lib_basecore.view.recyclerview.PullBaseView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RecyclerViewSupport
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.GlideLoader.loadImage
import com.iandroid.allclass.lib_common.Values
import com.iandroid.allclass.lib_common.base.ChatterBaseActivity
import com.iandroid.allclass.lib_common.beans.EventCourseChange
import com.iandroid.allclass.lib_common.event.CourseChangeEvent
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.immersiveStatusBarWithMargin
import com.iandroid.allclass.lib_common.utils.exts.objFromIntentParam
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.toPx
import kotlinx.android.synthetic.main.activity_course_list.bgTitle
import kotlinx.android.synthetic.main.activity_course_list.courseRv
import kotlinx.android.synthetic.main.activity_course_list.courseScroll
import kotlinx.android.synthetic.main.activity_course_list.ivBack
import kotlinx.android.synthetic.main.activity_course_list.ivCover
import kotlinx.android.synthetic.main.activity_course_list.topTitle
import kotlinx.android.synthetic.main.activity_course_list.tvDesc
import kotlinx.android.synthetic.main.activity_course_list.tvTitle

/**
 * @Desc: 课程列表页
 * @Created: Quan
 * @Date: 2023/9/20
 */
class CourseListActivity : ChatterBaseActivity(), IRvItemAction {

    private var recyclerViewSupport: RecyclerViewSupport? = null
    private var courseIntent: CourseListIntent? = null
    private var lastId: Int = 0
    private var changeCourseItem: CourseEntity? = null
    private var categoryDesc = ""
    private var categoryImg = ""
    private var isRefreshing = false

    private val viewModel by lazy {
        ViewModelProvider(
            this, CourseViewModel.ViewModeFactory()
        )[CourseViewModel::class.java]
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_course_list)
    }

    override fun initBaseContent() {
        super.initBaseContent()
        AppModule.userActive()
        courseIntent = intent.objFromIntentParam<CourseListIntent>(Values.intentJsonParam)
        if (courseIntent == null) return
        immersiveStatusBarWithMargin(ivBack)
        showTitleBar(false)
        setTitleContent()
        initRv()
        // 设置骨骼视图
        setPreView()
        setListener()
        fetchPageData(true)
    }

    private fun setTitleContent() {
        topTitle.text = courseIntent?.name.orEmpty()
        tvTitle.text = courseIntent?.name.orEmpty()
        ivBack.clickWithTrigger {
            finish()
        }
    }

    private fun initRv() {
        recyclerViewSupport = RecyclerViewSupport(supportFragmentManager, courseRv, null).also {
            it.setCanPullDown(true)
            it.setCanPullUp(true)
            it.recyclerView.setOnRefreshListener(object : PullBaseView.OnRefreshListener {
                override fun onHeaderRefresh() {
                    fetchPageData(true)
                }

                override fun onFooterRefresh() {
                    fetchPageData(false)
                }
            })
        }
    }

    private fun setListener() {
        var scrollYNum = 0
        var titleBgShowed = false
        courseScroll.setOnScrollChangeListener(OnScrollChangeListener { v, _, scrollY, _, oldScrollY ->
            scrollYNum += scrollY - oldScrollY
            titleBgShowed = if (scrollYNum > 172.toPx) {
                if (!titleBgShowed) bgTitle.show(true)
                true
            } else {
                if (titleBgShowed) bgTitle.show(false)
                false
            }
            if (v.getChildAt(v.childCount - 1) != null) {
                if ((scrollY >= (v.getChildAt(v.childCount - 1).measuredHeight - v.measuredHeight))
                    && scrollY > oldScrollY
                ) {
                    // 滑动到底部
                    recyclerViewSupport?.startFooterRefresh()
                }
            }
        })
        viewModel.coursesResult.observe(this) { courses ->
            recyclerViewSupport?.onHeaderRefreshComplete()
            recyclerViewSupport?.onFooterRefreshComplete()
            val coursesList = ArrayList<CourseEntity>().also { list ->
                courses.forEach { courseClass ->
                    // 设置课程分类信息
                    if (categoryDesc.isEmpty()) {
                        categoryDesc = courseClass.categoryDesc
                        tvDesc.text = categoryDesc
                    }
                    // 设置课程分类封面
                    if (categoryImg.isEmpty()) {
                        categoryImg = courseClass.categoryIcon
                        ivCover.loadImage(this, categoryImg)
                    }
                    courseClass.courses?.forEach { course ->
                        course.titleInfo = courseClass.category
                        list.add(course)
                    }
                }
            }
            updateData(
                ArrayList<BaseRvItemInfo?>().also { list ->
                    coursesList.forEach { course ->
                        list.add(BaseRvItemInfo(course, AppViewType.courseItemView, this))
                    }
                }, lastId == 0
            )
            if (coursesList.isNotEmpty()) {
                lastId = coursesList.last().id
            } else {
                recyclerViewSupport?.setNoMoreData(true)
            }
            isRefreshing = false
        }

        viewModel.coursesError.observe(this) {
            recyclerViewSupport?.onHeaderRefreshComplete()
            recyclerViewSupport?.onFooterRefreshComplete()
            // 第一页数据请求错误时
            if (lastId == 0) addErrorView()
            isRefreshing = false
        }

        // 课程状态发生了改变，更新该课程
        viewModel.compositeDisposable?.add(SimpleRxBus.observe(CourseChangeEvent::class) {
            if (it.course != null && it.course is CourseEntity)
                changeCourseItem = it.course as CourseEntity
        })

        viewModel?.compositeDisposable?.add(
            SimpleRxBus.observe(EventCourseChange::class) {
                fetchPageData(true)
            }
        )
    }

    private fun setPreView() {
        updateData(ArrayList<BaseRvItemInfo?>().also {
            it.add(BaseRvItemInfo(Any(), AppViewType.coursePlaceItemView, this))
        })
    }

    private fun fetchPageData(refresh: Boolean) {
        if (!isRefreshing) {
            isRefreshing = true
            if (refresh) lastId = 0
            viewModel.getCourses(lastId, courseIntent?.id ?: 0)
        } else {
            recyclerViewSupport?.onHeaderRefreshComplete()
            recyclerViewSupport?.onFooterRefreshComplete()
        }
    }

    private fun updateData(itemTemp: ArrayList<BaseRvItemInfo?>, needClear: Boolean = true) {
        if (itemTemp.isNotEmpty()) recyclerViewSupport?.updateData(itemTemp, needClear)
        else if (needClear) addEmptyView()
    }

    private fun addEmptyView() {
        val emptyEntity = EmptyEntity().also {
            it.title = getString(R.string.page_data_tips)
            it.icRes = R.mipmap.ic_msg_setting_nodata
        }

        emptyEntity.also { data ->
            updateData(ArrayList<BaseRvItemInfo?>().also {
                it.add(BaseRvItemInfo(data, AppViewType.comEmptyView, this))
            })
        }
    }

    private fun addErrorView() {
        updateData(ArrayList<BaseRvItemInfo?>().also {
            it.add(BaseRvItemInfo(Any(), AppViewType.exceptionView, this))
        })
    }

    override fun onResume() {
        super.onResume()
        notifyItem()
        changeCourseItem = null
    }

    private fun notifyItem() {
        val courses = recyclerViewSupport?.infos
        if (changeCourseItem != null && courses?.isNotEmpty() == true) {
            for (i in courses.indices) {
                if ((courses[i]?.data is CourseEntity) &&
                    (changeCourseItem?.id == (courses[i].data as CourseEntity).id)
                ) {
                    courses[i].data = changeCourseItem
                    recyclerViewSupport?.adapter?.notifyItemChanged(i)
                }
            }
        }
    }

    // ErrorView try again
    override fun onRefresh() {
        fetchPageData(true)
    }

    override fun immersiveIntoStatusBar(): Boolean {
        return true
    }

    override fun isShowTitleBar(): Boolean {
        return false
    }
}