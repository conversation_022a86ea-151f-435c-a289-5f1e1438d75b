package com.fascin.chatter.main.profile.view

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.fascin.chatter.R
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RvItem
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.beans.ProfitListBean
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.castObject
import kotlinx.android.synthetic.main.itemview_balance.view.ivIcon
import kotlinx.android.synthetic.main.itemview_balance.view.tvMoney
import kotlinx.android.synthetic.main.itemview_balance.view.tvTime
import kotlinx.android.synthetic.main.itemview_balance.view.tvTitle

/**
 * @Created: QuanZH
 */
@RvItem(id = AppViewType.balanceItemView, spanCount = 1)
class BalanceItemView(context: Context, fm: FragmentManager, parent: ViewGroup) :
    BaseRvItemView(context, fm, parent) {

    override fun attachLayoutId(): Int {
        return R.layout.itemview_balance
    }

    override fun initView(context: Context?, view: View?) {

    }

    override fun setView() {
        val entity = getItemData() ?: return
        itemView?.run {
            tvTitle.text = when(entity.vtype) {
                "gift_receive" -> entity.details?.desc.orEmpty()
                else -> entity.title
            }
            tvTime.text = entity.createdAt
            tvMoney.text = buildString {
                if (entity.payDirection == "in") append("+")
                else append("-")
                append(entity.moneySymbol)
                append(entity.money)
            }
            ivIcon.setImageResource(
                when (entity.payDirection) {
                    "in" -> R.drawable.ic_balance_income
                    "out" -> {
                        if (entity.vtype == "penalty") R.drawable.ic_balance_deduct_failed
                        else R.drawable.ic_balance_failed
                    }

                    else -> R.drawable.ic_balance_income
                }
            )
            clickWithTrigger {
                if (entity.vtype == "penalty"){
                    context.routeAction(ActionType.actionPenalties)
                    return@clickWithTrigger
                }
                when (entity.payDirection) {
                    "in" -> {
                        //收入
                        context.routeAction(ActionType.actionRewardDetail){
                            it.param = entity
                        }
                    }
                    "out" -> {
                        //支出
                        context.routeAction(ActionType.actionDeductDetail){
                            it.param = entity
                        }
                    }
                }
            }
        }
    }

    private fun getItemData(): ProfitListBean? = data?.castObject<ProfitListBean>()
}