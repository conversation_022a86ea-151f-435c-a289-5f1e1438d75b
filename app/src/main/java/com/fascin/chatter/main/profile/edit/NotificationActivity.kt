package com.fascin.chatter.main.profile.edit

import android.os.Bundle
import com.fascin.chatter.R
import com.fascin.chatter.config.Config
import com.fascin.chatter.repository.AppRepository
import com.iandroid.allclass.lib_common.base.ChatterBaseActivity
import com.iandroid.allclass.lib_common.utils.SPConstants
import com.iandroid.allclass.lib_common.utils.SPUtils
import com.iandroid.allclass.lib_common.views.CommonAlertDialog
import kotlinx.android.synthetic.main.activity_edit_notification.newMatchClose
import kotlinx.android.synthetic.main.activity_edit_notification.newMsgClose

/**
 * 通知设置
 */
class NotificationActivity : ChatterBaseActivity() {
    private var pushConfig = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_edit_notification)
    }

    override fun initBaseContent() {
        super.initBaseContent()
        setTitle(R.string.profile_notification_setting)

        pushConfig = SPUtils.getInt(this, SPConstants.KEY_PUSH_CONFIG, 0)
        newMsgClose.isChecked = (pushConfig and 0x02) != Config.closeOnlinePush
        newMatchClose.isChecked = (pushConfig and 0x01) != Config.closeInAppPush

        newMsgClose.setOnCheckedChangeListener { buttonView, isChecked ->
            //防止初始化时触发监听
            if (!buttonView.isPressed) return@setOnCheckedChangeListener
            pushConfig = pushConfig xor (Config.closeOnlinePush)
            AppRepository.notifyChange(pushConfig)
            if (!isChecked) {
                offDialog(canCelCallback = {
                    newMatchClose.isChecked = true
                    pushConfig = pushConfig xor (Config.closeOnlinePush)
                    AppRepository.notifyChange(pushConfig)
                })
            }
        }
        newMatchClose.setOnCheckedChangeListener { buttonView, isChecked ->
            //防止初始化时触发监听
            if (!buttonView.isPressed) return@setOnCheckedChangeListener
            pushConfig = pushConfig xor (Config.closeInAppPush)
            AppRepository.notifyChange(pushConfig, 1)
            if (!isChecked) {
                offDialog(canCelCallback = {
                    newMatchClose.isChecked = true
                    pushConfig = pushConfig xor (Config.closeInAppPush)
                    AppRepository.notifyChange(pushConfig, 1)
                })
            }
        }
    }

    /**
     * 关闭通知的时候弹出提示
     */
    private fun offDialog(canCelCallback: () -> Unit = {}) {
        CommonAlertDialog.Builder().setTitle(getString(R.string.notification_title))
            .setContext(getString(R.string.notification_content))
            .setCancel(getString(com.iandroid.allclass.lib_common.R.string.btn_cancel)) {
                canCelCallback.invoke()
            }
            .setConfirm(getString(com.iandroid.allclass.lib_common.R.string.btn_sure)) {}
            .create()
            .show(supportFragmentManager, CommonAlertDialog::class.java.name)
    }

    override fun onBackPressed() {
        finish()
    }
}