package com.fascin.chatter.main.profile

import android.content.Context
import android.view.View
import android.widget.RelativeLayout
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.R
import com.fascin.chatter.bean.EmptyEntity
import com.fascin.chatter.bean.event.MessageSettingChangeEvent
import com.fascin.chatter.config.TabConfig
import com.fascin.chatter.main.MixListFragment
import com.fascin.chatter.main.profile.edit.MessageSettingEditDialog
import com.iandroid.allclass.lib_basecore.view.FontTextView
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemInfo
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.Values
import com.iandroid.allclass.lib_common.Values.Companion.messageSettingTypeGreeting
import com.iandroid.allclass.lib_common.Values.Companion.messageSettingTypeMessage
import com.iandroid.allclass.lib_common.beans.MsgContentEntity
import com.iandroid.allclass.lib_common.beans.UserEntity
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.exts.castObject
import com.iandroid.allclass.lib_common.utils.exts.objFromBundleParam
import com.iandroid.allclass.lib_common.utils.exts.toPx

/**
 * @Created: QuanZH
 * @Date: 2023/7/11
 */
class UserMessageSettingFragment : MixListFragment(), IMessageSettingRvItemAction {

    private val viewModel by lazy {
        ViewModelProvider(
            this,
            ProfileViewModel.ViewModeFactory()
        )[ProfileViewModel::class.java]
    }

    private var curUser: UserEntity? = null
    private var type = messageSettingTypeGreeting

    override fun initView(view: View?) {
        super.initView(view)
        if (mixPageEntity?.tabId == TabConfig.userMessageSettingMsg)
            type = messageSettingTypeMessage
        curUser = arguments?.objFromBundleParam<UserEntity>(Values.userJsonParam)
        showAddBtn()
        // 此页面数据一次性加载完成，无需刷新与加载更多
        recyclerViewSupport?.setCanPullDown(true)
        recyclerViewSupport?.setCanPullUp(false)
        //骨骼
        updateData(
            ArrayList<BaseRvItemInfo>().also {
                it.add(BaseRvItemInfo(Any(), AppViewType.userMessageSettingPlaceItemView))
                it.add(BaseRvItemInfo(Any(), AppViewType.userMessageSettingPlaceItemView))
                it.add(BaseRvItemInfo(Any(), AppViewType.userMessageSettingPlaceItemView))
                it.add(BaseRvItemInfo(Any(), AppViewType.userMessageSettingPlaceItemView))
            },
            true
        )
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        // 删除成功
        viewModel.messageSettingDelUser.observe(this@UserMessageSettingFragment) {
            delMessageSuccess(it)
        }
    }

    override fun requestData(refresh: Boolean) {

        mixPageEntity?.api_url?.let {
            lastFetchDataTime = System.currentTimeMillis()
            recyclerViewSupport?.showLoading()
            if (refresh) pageIndex = Values.fristPage
            curUser?.userId?.let { userId ->
                mixListViewModel?.fetchUserMessageSettingMixData(
                    it,
                    userId,
                    this@UserMessageSettingFragment
                )
            }
        }
    }

    override fun addEmptyView() {
        val emptyEntity: EmptyEntity?
        when (mixPageEntity?.tabId) {
            TabConfig.userMessageSettingGreeting -> {
                emptyEntity = EmptyEntity().also {
                    it.content = getString(R.string.page_nodata_message_setting_greet)
                    it.icRes = R.mipmap.ic_msg_setting_nodata
                }
            }

            TabConfig.userMessageSettingMsg -> {
                emptyEntity = EmptyEntity().also {
                    it.content = getString(R.string.page_nodata_message_setting_message)
                    it.icRes = R.mipmap.ic_msg_setting_nodata
                }
            }

            else -> {
                emptyEntity = EmptyEntity().also {
                    it.title = getString(R.string.page_data_tips)
                    it.icRes = R.mipmap.ic_msg_setting_nodata
                }
            }
        }

        emptyEntity.also { data ->
            updateData(
                ArrayList<BaseRvItemInfo?>().also {
                    it.add(BaseRvItemInfo(data, AppViewType.comEmptyView, this))
                }, true
            )
        }
    }

    override fun addMessage(userID: String, nickName: String) {
        // 跳转添加页
        curUser?.let { user ->
            MessageSettingEditDialog(
                MsgContentEntity("", -1, 0, userID),
                user.nickname,
                type
            ) {
                addMessageSuccess(it)
            }.show(childFragmentManager, MessageSettingEditDialog::javaClass.name)
        }
    }

    override fun delMessage(msgID: Int, userID: String) {
        // 删除指定消息
        viewModel.delMessageOrGreeting(userID, msgID, false)
    }

    override fun modifyMessage(msgID: Int, msgContent: String, userID: String, nickName: String) {
        // 跳转添加页,修改指定消息
        curUser?.let { user ->
            MessageSettingEditDialog(
                MsgContentEntity(msgContent, msgID, 0, userID),
                user.nickname,
                type
            ) {
                modifyMessageSuccess(it)
            }.show(childFragmentManager, MessageSettingEditDialog::javaClass.name)
        }
    }

    override fun onRefresh() {
        requestData(true)
    }

    /**
     * 添加成功
     */
    private fun addMessageSuccess(contentEntity: MsgContentEntity) {
        // 通知MessageSettingFragment更新
        SimpleRxBus.post(MessageSettingChangeEvent(type))
        recyclerViewSupport?.infos?.also { rvItems ->
            for (index in rvItems.indices) {
                val item = rvItems[index]
                if (item.viewType == AppViewType.userMessageSettingItemView) {
                    val tempData = item.data?.castObject<MsgContentEntity>()
                    if (contentEntity.mid.isNotEmpty() && tempData?.mid == contentEntity.mid) {
                        rvItems.add(
                            0,
                            BaseRvItemInfo(
                                contentEntity,
                                AppViewType.userMessageSettingItemView
                            )
                        )
                    }
                    recyclerViewSupport?.adapter?.notifyDataSetChanged()
                    break
                }
            }
        }
    }

    /**
     * 修改成功
     */
    private fun modifyMessageSuccess(contentEntity: MsgContentEntity) {
        // 通知MessageSettingFragment更新
        SimpleRxBus.post(MessageSettingChangeEvent(type))
        recyclerViewSupport?.infos?.also { rvItems ->
            for (index in rvItems.indices) {
                val item = rvItems[index]
                if (item.viewType == AppViewType.userMessageSettingItemView) {
                    val tempData = item.data?.castObject<MsgContentEntity>()
                    if (tempData?.id == contentEntity.id) {
                        tempData.content = contentEntity.content
                        // 修改为审核状态
                        tempData.status = 1
                        recyclerViewSupport?.adapter?.notifyItemChanged(index)
                        break
                    }

                }
            }
        }
    }

    /**
     * 删除成功
     */
    private fun delMessageSuccess(contentEntity: MsgContentEntity) {
        // 通知MessageSettingFragment更新
        SimpleRxBus.post(MessageSettingChangeEvent(type))
        recyclerViewSupport?.infos?.also { rvItems ->
            for (index in rvItems.indices) {
                val item = rvItems[index]
                if (item.viewType == AppViewType.userMessageSettingItemView) {
                    val tempData = item.data?.castObject<MsgContentEntity>()
                    if (tempData?.id == contentEntity.id) {
                        rvItems.removeAt(index)
                        recyclerViewSupport?.adapter?.notifyItemRemoved(index)
                        if (recyclerViewSupport?.adapter?.itemCount!! <= 0)
                            addEmptyView()
                        break
                    }
                }
            }
        }
    }

    override fun addErrorView() {
        updateData(
            ArrayList<BaseRvItemInfo?>().also {
                it.add(BaseRvItemInfo(Any(), AppViewType.exceptionView, this))
            }, true
        )
    }

    /**
     * 显示添加按钮
     */
    private fun showAddBtn() {
        bottomView.removeAllViews()
        val addBtn = layoutInflater.inflate(R.layout.include_message_setting_add_btn, null)
        addBtn.layoutParams = RelativeLayout.LayoutParams(
            RelativeLayout.LayoutParams.MATCH_PARENT,
            164.toPx
        )
        addBtn.findViewById<FontTextView>(R.id.rllFlashChat).apply {
            text =
                if (type == messageSettingTypeGreeting) getString(R.string.message_setting_btn_add_greeting)
                else getString(
                    R.string.message_setting_btn_add_msg
                )
            setOnClickListener {
                curUser?.let { user -> addMessage(user.userId, user.nickname) }
            }
        }
        bottomView.addView(addBtn)
    }

    override fun fragmentVisible(visible: Boolean) {
        super.fragmentVisible(visible)
//        if (visible) {
//            // 当前fragment状态变成显示，可以进行一些操作
//        }
    }
}