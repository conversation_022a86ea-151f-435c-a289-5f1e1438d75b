package com.fascin.chatter.main.schedule.view

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import com.fascin.chatter.R
import com.fascin.chatter.bean.SchedulePerTabItemEntity
import com.fascin.chatter.main.schedule.ScheduleDate
import com.iandroid.allclass.lib_common.utils.exts.textColorResource
import kotlinx.android.synthetic.main.view_schedule_per_tab.view.llTabChild
import kotlinx.android.synthetic.main.view_schedule_per_tab.view.tvTabDate
import kotlinx.android.synthetic.main.view_schedule_per_tab.view.tvTabTitle

/**
 * @Desc: 排班预览子tab
 * @Created: Quan
 * @Date: 2024/11/15
 */
class SchedulePerTabView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    init {
        View.inflate(context, R.layout.view_schedule_per_tab, this)
    }

    fun setViewData(entity: SchedulePerTabItemEntity?, title: String) {
        tvTabTitle.text = title
        tvTabDate.text = getWeekDay(entity?.startDay.orEmpty(), entity?.endDay.orEmpty())
    }

    fun updateViewBg(isSelected: Boolean) {
        llTabChild.isSelected = isSelected
        tvTabTitle.textColorResource =
            if (isSelected) R.color.white else com.iandroid.allclass.lib_common.R.color.cl_595959
        tvTabDate.textColorResource =
            if (isSelected) R.color.white else com.iandroid.allclass.lib_common.R.color.cl_595959
    }

    private fun getWeekDay(startDay: String, endDay: String): String {
        val start = ScheduleDate.formatToMonthDay(startDay)
        val end = ScheduleDate.formatToMonthDay(endDay)
        return "$start ~ $end"
    }
}