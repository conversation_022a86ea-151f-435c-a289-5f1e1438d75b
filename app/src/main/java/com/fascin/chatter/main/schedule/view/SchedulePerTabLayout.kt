package com.fascin.chatter.main.schedule.view

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import com.fascin.chatter.R
import com.fascin.chatter.bean.SchedulePerTabEntity
import com.fascin.chatter.main.schedule.ScheduleConst
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import kotlinx.android.synthetic.main.layout_schedule_per_tab.view.sptView1
import kotlinx.android.synthetic.main.layout_schedule_per_tab.view.sptView2
import kotlinx.android.synthetic.main.layout_schedule_per_tab.view.sptView3

/**
 * @Desc: 排班预览页tab
 * @Created: Quan
 * @Date: 2024/11/15
 */
class SchedulePerTabLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private var tabData: SchedulePerTabEntity? = null

    private var selectedId = ScheduleConst.TAB_THIS // 默认选中一个,0第一个 1第二个 2第三个
    private var selectListener: ((Int) -> Unit)? = null

    init {
        View.inflate(context, R.layout.layout_schedule_per_tab, this)
        sptView1.clickWithTrigger {
            selectedId = ScheduleConst.TAB_LAST
            updateViewBg()
        }
        sptView2.clickWithTrigger {
            selectedId = ScheduleConst.TAB_THIS
            updateViewBg()
        }
        sptView3.clickWithTrigger {
            selectedId = ScheduleConst.TAB_NEXT
            updateViewBg()
        }
    }


    fun setViewData(data: SchedulePerTabEntity, defaultTab: Int) {
        tabData = data
        tabData?.let {
            selectedId = defaultTab
            sptView1.setViewData(it.lastWeek, context.getString(R.string.text_schedule_last_week))
            sptView2.setViewData(it.thisWeek, context.getString(R.string.text_schedule_this_week))
            sptView3.setViewData(it.nextWeek, context.getString(R.string.text_schedule_next_week))
            updateViewBg()
        }
    }

    fun setSelectListener(listener: (Int) -> Unit) {
        selectListener = listener
    }

    private fun updateViewBg() {
        when (selectedId) {
            0 -> {
                sptView1.updateViewBg(true)
                sptView2.updateViewBg(false)
                sptView3.updateViewBg(false)
            }

            1 -> {
                sptView1.updateViewBg(false)
                sptView2.updateViewBg(true)
                sptView3.updateViewBg(false)
            }

            2 -> {
                sptView1.updateViewBg(false)
                sptView2.updateViewBg(false)
                sptView3.updateViewBg(true)
            }
        }
        selectListener?.invoke(selectedId)
    }
}