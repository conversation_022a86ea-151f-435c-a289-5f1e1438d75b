package com.fascin.chatter.main.profile.guide

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.fascin.chatter.R
import com.fascin.chatter.bean.FeatureGuideEntity
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RvItem
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.castObject
import com.iandroid.allclass.lib_common.utils.exts.show
import kotlinx.android.synthetic.main.item_feature_directory.view.tvContent
import kotlinx.android.synthetic.main.item_feature_directory.view.tvTitle

@RvItem(id = AppViewType.featureDirectoryItemView, spanCount = 1)
class FeatureDirectoryItemView(context: Context, fm: FragmentManager, parent: ViewGroup) :
    BaseRvItemView(context, fm, parent) {
    override fun attachLayoutId(): Int {
        return R.layout.item_feature_directory
    }

    override fun initView(context: Context?, view: View?) {
    }

    override fun setView() {
        val entity = getItemData() ?: return
        itemView?.run {
            tvTitle.show(entity.title.isNotEmpty())
            tvTitle.text = entity.title
            tvContent.text = entity.content
            clickWithTrigger {
                getAction()?.onDirectoryItemClick(position)
            }
        }
    }

    private fun getItemData(): FeatureGuideEntity? = data?.castObject<FeatureGuideEntity>()

    private fun getAction(): DirectoryItemAction? {
        return info?.callBack?.castObject<DirectoryItemAction>()
    }
}