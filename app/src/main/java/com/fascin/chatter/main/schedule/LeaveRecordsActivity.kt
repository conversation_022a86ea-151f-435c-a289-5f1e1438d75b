package com.fascin.chatter.main.schedule

import android.os.Bundle
import android.view.Gravity
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.R
import com.fascin.chatter.bean.EmptyEntity
import com.fascin.chatter.bean.LeaveRecordEntity
import com.fascin.chatter.bean.event.UIRefreshScheduleEvent
import com.fascin.chatter.main.IRvItemAction
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemInfo
import com.iandroid.allclass.lib_basecore.view.recyclerview.PullBaseView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RecyclerViewSupport
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.base.ChatterBaseActivity
import com.iandroid.allclass.lib_common.beans.ComContentEntity
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.ToastUtils
import com.iandroid.allclass.lib_common.views.CommonAlertDialog
import kotlinx.android.synthetic.main.activity_leave_records.recordsRv

/**
 * @Desc: 请假申请记录
 * @Created: Quan
 * @Date: 2024/11/20
 */
class LeaveRecordsActivity : ChatterBaseActivity(), IRvItemAction, LeaveRecordCallback {

    private var viewModel: ScheduleModel? = null
    private var recyclerViewSupport: RecyclerViewSupport? = null
    private var refreshing: Boolean = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_leave_records)
    }

    override fun initBaseContent() {
        super.initBaseContent()
        setTitle(getString(R.string.text_leave_records))
        initRv()
        // 设置骨骼视图
        setPreView()
        setViewModel()
        fetchPageData()
    }

    private fun initRv() {
        recyclerViewSupport = RecyclerViewSupport(supportFragmentManager, recordsRv, null).also {
            it.setCanPullDown(true)
            it.setCanPullUp(false)
            it.recyclerView.setOnRefreshListener(object : PullBaseView.OnRefreshListener {
                override fun onHeaderRefresh() {
                    if (!refreshing) fetchPageData()
                    else recyclerViewSupport?.onHeaderRefreshComplete()
                }

                override fun onFooterRefresh() {

                }
            })
        }
    }

    private fun setPreView() {
        updateData(ArrayList<BaseRvItemInfo>().also {
            it.add(BaseRvItemInfo(Any(), AppViewType.leaveRecordsPlaceItemView, this))
            it.add(BaseRvItemInfo(Any(), AppViewType.leaveRecordsPlaceItemView, this))
            it.add(BaseRvItemInfo(Any(), AppViewType.leaveRecordsPlaceItemView, this))
            it.add(BaseRvItemInfo(Any(), AppViewType.leaveRecordsPlaceItemView, this))
        })
    }

    private fun setViewModel() {
        viewModel = ViewModelProvider(this).get(ScheduleModel::class.java)
        viewModel?.leaveRecordsResult?.observe(this) {
            refreshing = false
            recyclerViewSupport?.onHeaderRefreshComplete()
            recyclerViewSupport?.onFooterRefreshComplete()
            if (it.leaveRecords.isNotEmpty()) {
                updateData(
                    ArrayList<BaseRvItemInfo>().also { list ->
                        it.leaveRecords.forEach { leave ->
                            list.add(BaseRvItemInfo(leave, AppViewType.leaveRecordsItemView, this))
                        }
                    })
            } else {
                addEmptyView()
            }
        }
        viewModel?.leaveRecordsError?.observe(this) {
            refreshing = false
            recyclerViewSupport?.onHeaderRefreshComplete()
            recyclerViewSupport?.onFooterRefreshComplete()
            addErrorView()
        }

        viewModel?.leaveSubmitResult?.observe(this) {
            if (it == "1") {
                // 提交成功
                ToastUtils.showToast(getString(com.iandroid.allclass.lib_common.R.string.share_success))
                SimpleRxBus.post(UIRefreshScheduleEvent())
                fetchPageData()
            } else {
                ToastUtils.showToast(it)
            }
        }

    }

    private fun fetchPageData() {
        if (!refreshing) {
            refreshing = true
            viewModel?.getLeaveRecords()
        }
    }

    private fun updateData(itemTemp: ArrayList<BaseRvItemInfo>?) {
        if (itemTemp?.isNotEmpty() == true) recyclerViewSupport?.updateData(itemTemp, true)
        else addEmptyView()
    }

    /**
     * 数据错误视图
     */
    private fun addErrorView() {
        updateData(ArrayList<BaseRvItemInfo>().also {
            it.add(BaseRvItemInfo(Any(), AppViewType.exceptionView, this))
        })
    }

    /**
     * 空视图
     */
    private fun addEmptyView() {
        updateData(ArrayList<BaseRvItemInfo>().also { list ->
            list.add(BaseRvItemInfo(EmptyEntity().also {
                it.content = getString(R.string.text_leave_records_empty)
                it.icRes = R.mipmap.ic_public_album_empty
            }, AppViewType.comEmptyView, this))
        })
    }

    override fun onRefresh() {
        fetchPageData()
    }

    override fun onItemClick(leave: LeaveRecordEntity) {
        // 点击item中的按钮
//        viewModel?.submitLeave(leave.id)
        if (leave.status == ScheduleConst.LEAVE_STATUS_ONGOING) {
            showSuspendDialog(leave)
        } else if (leave.status == ScheduleConst.LEAVE_STATUS_NOT_START) {
            showCancelDialog(leave)
        }
    }

    /**
     * 点击suspend
     */
    private fun showSuspendDialog(leave: LeaveRecordEntity) {
        CommonAlertDialog.Builder()
            .setCancelable(false)
            .setCancel(getString(com.iandroid.allclass.lib_basecore.R.string.cancel)) {}
            .setConfirm(getString(com.iandroid.allclass.lib_common.R.string.btn_sure)) {
                // 提交暂停申请
                viewModel?.submitLeaveSuspend(leave.id)
            }
            .setContextGravity(Gravity.CENTER)
            .setTitle(getString(R.string.text_suspend_dialog_title))
            .setContext(getString(R.string.text_suspend_dialog_subtitle))
            .create()
            .show(supportFragmentManager, CommonAlertDialog::javaClass.name)
    }

    /**
     * 点击cancel
     */
    private fun showCancelDialog(leave: LeaveRecordEntity) {
        CommonAlertDialog.Builder()
            .setCancelable(false)
            .setCancel(getString(com.iandroid.allclass.lib_basecore.R.string.cancel)) {}
            .setConfirm(getString(com.iandroid.allclass.lib_common.R.string.btn_sure)) {
                // 提交取消申请
                viewModel?.submitLeaveCancel(leave.id)
            }
            .setContextGravity(Gravity.CENTER)
            .setTitle(getString(R.string.text_leave_cancel_title))
            .setContext(getString(R.string.text_leave_cancel_content))
            .create()
            .show(supportFragmentManager, CommonAlertDialog::javaClass.name)
    }

    /**
     * 获取对应的天列表
     */
    private fun getSuspendDialogContent(leave: LeaveRecordEntity): List<ComContentEntity> {
        val shifts = arrayListOf<ComContentEntity>().apply {
            val difference =
                (ScheduleDate.getDaysDifference(
                    ScheduleDate.SERVER_CURRENT_DATE,
                    leave.endDate
                ) + 1).toInt()
            val days = ScheduleDate.getNextXDays(leave.startDate, difference)
            days.forEach { day ->
                add(
                    ComContentEntity().apply {
                        content = ScheduleDate.getMDWStr(day)
                        ymdwMap = ScheduleDate.splitDateString(day)
                    }
                )
            }
        }
        return shifts
    }
}