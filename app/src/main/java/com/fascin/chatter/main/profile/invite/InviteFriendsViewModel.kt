package com.fascin.chatter.main.profile.invite

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.bean.InviteFriendEntity
import com.fascin.chatter.bean.InviteHistoryListEntity
import com.fascin.chatter.repository.AppRepository
import com.iandroid.allclass.lib_common.base.BaseViewModel
import com.iandroid.allclass.lib_common.network.ErrorCodeCheckUtils
import com.iandroid.allclass.lib_common.utils.ToastUtils

/**
 *  @author: LXL
 *  @description: description
 *  @date: 2024/9/3 16:37
 */
class InviteFriendsViewModel : BaseViewModel() {
    val inviteFriendResult = MutableLiveData<InviteFriendEntity>()
    val inviteFriendError = MutableLiveData<Any>()

    val inviteHistoryResult = MutableLiveData<InviteHistoryListEntity>()
    val inviteHistoryError = MutableLiveData<Any>()

    /**
     * 老主播邀请好友
     */
    fun inviteFriends() {
        compositeDisposable?.add(
            AppRepository.inviteFriends()
                .subscribe({
                    inviteFriendResult.postValue(it)
                }, {
                    inviteFriendError.postValue(Any())
                    ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
                })
        )
    }

    /**
     * 历史邀请成功记录
     */
    fun invitationHistory() {
        compositeDisposable?.add(
            AppRepository.invitationHistory()
                .subscribe({
                    inviteHistoryResult.postValue(it)
                }, {
                    inviteHistoryError.postValue(Any())
                    ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
                })
        )
    }

    class ViewModeFactory : ViewModelProvider.Factory {
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            return InviteFriendsViewModel() as T
        }
    }
}