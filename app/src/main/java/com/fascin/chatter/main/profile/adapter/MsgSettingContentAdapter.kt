package com.fascin.chatter.main.profile.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.main.profile.MessageSettingContentCallBack
import com.iandroid.allclass.lib_common.beans.MsgContentEntity
import kotlinx.android.synthetic.main.itemview_message_setting_content.view.btnDelete
import kotlinx.android.synthetic.main.itemview_message_setting_content.view.btnModify
import kotlinx.android.synthetic.main.itemview_message_setting_content.view.content
import kotlinx.android.synthetic.main.itemview_message_setting_content.view.tipExamine
import kotlinx.android.synthetic.main.itemview_message_setting_content.view.tipReject

/**
 * @Desc: MessageSetting列表item content adapter
 * @Created: QuanZH
 * @Date: 2023/7/12
 */
class MsgSettingContentAdapter :
    RecyclerView.Adapter<MsgSettingContentAdapter.ViewHolder>() {

    private val dataList = mutableListOf<MsgContentEntity>()
    private var callBack: MessageSettingContentCallBack? = null

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

    fun setCallBack(callBack: MessageSettingContentCallBack) {
        this.callBack = callBack
    }

    fun updateData(data: List<MsgContentEntity>?) {
        dataList.clear()
        if (!data.isNullOrEmpty()) {
            dataList.addAll(data)
        }
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.itemview_message_setting_content, parent, false)
        )
    }

    override fun getItemCount(): Int = dataList.size

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = dataList[position]
        holder.itemView.run {
            content.text = item.content
            val passStatus = item.status == MsgContentEntity.EXAMINED
            val examineStatus = item.status == MsgContentEntity.IN_REVIEW
            val rejectStatus = item.status == MsgContentEntity.REJECT
            // 根据审核状态设置按钮的显隐
            btnDelete.visibility = if (passStatus || rejectStatus) View.VISIBLE else View.GONE
            btnModify.visibility = if (passStatus || rejectStatus) View.VISIBLE else View.GONE
            tipExamine.visibility = if (examineStatus) View.VISIBLE else View.GONE
            tipReject.visibility = if (rejectStatus) View.VISIBLE else View.GONE
            // 点击事件处理
            btnModify.setOnClickListener {
                // 修改
                callBack?.modifyMessage(item.id, item.content)
            }
            btnDelete.setOnClickListener {
                // 删除
                callBack?.delMessage(item.id)
            }
        }
    }
}