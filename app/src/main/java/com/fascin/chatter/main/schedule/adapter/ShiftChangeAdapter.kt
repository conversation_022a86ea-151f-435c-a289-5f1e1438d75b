package com.fascin.chatter.main.schedule.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.bean.ScheduleDateItemEntity
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.textColorResource
import kotlinx.android.synthetic.main.itemview_shift_change.view.tvContent

/**
 * @Desc: 换班班次选择列表
 * @Created: Quan
 * @Date: 2024/11/19
 */
class ShiftChangeAdapter : RecyclerView.Adapter<ShiftChangeAdapter.ViewHolder>() {

    private var selectedShiftId = -1 // 选中的item
    private var selectHolder: ViewHolder? = null
    private val dataList = mutableListOf<ScheduleDateItemEntity>()
    private var onItemClickListener: ((ScheduleDateItemEntity) -> Unit)? = null

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

    fun setOnItemClickListener(listener: (ScheduleDateItemEntity) -> Unit) {
        this.onItemClickListener = listener
    }

    fun updateData(data: List<ScheduleDateItemEntity>) {
        selectedShiftId = -1
        selectHolder = null
        dataList.clear()
        if (!data.isNullOrEmpty()) {
            dataList.addAll(data)
        }
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.itemview_shift_change, parent, false)
        )

    }

    override fun getItemCount(): Int = dataList.size

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = dataList[position]
        holder.itemView?.also { itemView ->
            itemView.tvContent.text = item.shiftDate.replace(":00", "")
            if (selectedShiftId == item.shiftId) {
                selectHolder = holder
                itemView.tvContent.textColorResource = R.color.white
                itemView.tvContent.isSelected = true
            } else {
                itemView.tvContent.textColorResource = com.iandroid.allclass.lib_common.R.color.cl_262626
                itemView.tvContent.isSelected = false
            }
        }
        holder.itemView.clickWithTrigger {
            if (selectedShiftId != item.shiftId) {
                selectedShiftId = item.shiftId
                selectHolder?.also { select ->
                    select.itemView.tvContent.textColorResource = com.iandroid.allclass.lib_common.R.color.cl_262626
                    select.itemView.tvContent.isSelected = false
                }
                holder.itemView.tvContent.textColorResource = R.color.white
                holder.itemView.tvContent.isSelected = true
                selectHolder = holder
                onItemClickListener?.invoke(item)
            }
        }
    }

}