package com.fascin.chatter.main.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.bean.ModelUserEntity
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.GlideLoader.loadImageCircleCrop
import com.iandroid.allclass.lib_common.utils.SPConstants
import com.iandroid.allclass.lib_common.utils.SPUtils
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.getCompatColor
import com.iandroid.allclass.lib_common.utils.exts.show
import kotlinx.android.synthetic.main.itemview_chatter_model.view.modelHeadImg
import kotlinx.android.synthetic.main.itemview_chatter_model.view.modelHeadImgBorder
import kotlinx.android.synthetic.main.itemview_chatter_model.view.modelNickName
import kotlinx.android.synthetic.main.itemview_chatter_model.view.modelUnreadTv
import kotlinx.android.synthetic.main.itemview_chatter_model.view.rootModelView
import kotlinx.android.synthetic.main.itemview_chatter_model.view.tvOnlineNum
import kotlinx.android.synthetic.main.itemview_chatter_model.view.viewModelLeftLine
import java.util.Collections

/**
 * @Desc: 会话列表model adapter
 * @Created: Quan
 * @Date: 2023/12/13
 */
class ChattersModelListAdapter(private val block: (String, Boolean) -> Unit) :
    RecyclerView.Adapter<ChattersModelListAdapter.ViewHolder>(), ItemTouchHelperAdapter {
    var modelList: ArrayList<ModelUserEntity> = ArrayList()
    private var _onlineNum: HashMap<String, Int> = HashMap()
    private var _activeNum: HashMap<String, Int> = HashMap()
    var curSelUserId: String = ""
    private var showUnreadNum: Boolean = true

    fun updateModelUnread(unreadData: HashMap<String, Int>) {
        for (item in modelList) {
            if (unreadData.containsKey(item.userId)) {
                item.unreadnum = unreadData[item.userId] ?: 0
            } else {
                item.unreadnum = 0
            }
        }
        notifyDataSetChanged()
    }

    fun setImOnlineStatus(onlineNum: HashMap<String, Int>, activeNum: HashMap<String, Int>) {
        this._onlineNum = onlineNum
        this._activeNum = activeNum
        notifyDataSetChanged()
    }

    val onlineNum: HashMap<String, Int>
        get() = _onlineNum

    val activeNum: HashMap<String, Int>
        get() = _activeNum

    fun isShowUnreadNum(show: Boolean) {
        showUnreadNum = show
    }

    fun updateData(data: ArrayList<ModelUserEntity>, resetCurModel: Boolean = false) {
        if (data.isNullOrEmpty()) return
        if (resetCurModel) curSelUserId = ""
        modelList.clear()
        modelList.addAll(data)
        for (item in modelList) {
            if (curSelUserId.isNullOrEmpty()) {
                curSelUserId = item.userId
                block.invoke(curSelUserId, false)
                break
            }
        }
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutInflater.from(parent.context).inflate(
                R.layout.itemview_chatter_model, parent, false
            )
        )
    }

    override fun getItemCount(): Int = modelList.size

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val modelItem = modelList[position]
        holder.itemView.run {
            context?.let {
                modelHeadImg.loadImageCircleCrop(
                    it, modelItem.avatarUrl, grayscale = modelItem.status == 1
                )
            }
            viewModelLeftLine.show(position == 0)
            modelNickName.text = modelItem.nickname
            if (curSelUserId == modelItem.userId) {
                modelNickName.setBackgroundResource(R.drawable.bg_f9bc20_r99)
                modelNickName.setTextColor(context.getCompatColor(R.color.white))
            } else {
                modelNickName.setBackgroundResource(R.drawable.bg_white_r99)
                modelNickName.setTextColor(context.getCompatColor(R.color.black))
            }
            modelHeadImgBorder.show(curSelUserId == modelItem.userId)

            tvOnlineNum.show(false)
            if (curSelUserId != modelItem.userId) {
                if (onlineNum.containsKey(modelItem.userId) && onlineNum[modelItem.userId]!! > 0) {
                    tvOnlineNum.show(true)
                    tvOnlineNum.text = onlineNum[modelItem.userId]!!.toString()
                    tvOnlineNum.setBackgroundResource(com.iandroid.allclass.lib_common.R.color.color_24C004)
                } else if (activeNum.containsKey(modelItem.userId) && activeNum[modelItem.userId]!! > 0) {
                    tvOnlineNum.show(true)
                    tvOnlineNum.text = activeNum[modelItem.userId]!!.toString()
                    tvOnlineNum.setBackgroundResource(com.iandroid.allclass.lib_basecore.R.color.cl_9370DB)
                }
            }
            modelUnreadTv.show(showUnreadNum && modelItem.unreadnum > 0)
            modelUnreadTv.text =
                if (modelItem.unreadnum > 99) "99+" else "${modelItem.unreadnum}"
            rootModelView.clickWithTrigger {
                selModel(modelItem.userId)
            }
        }
    }

    private fun selModel(userId: String) {
        if (curSelUserId != userId) {
            curSelUserId = userId
            notifyDataSetChanged()
            block?.invoke(userId, false)
        } else {
            block?.invoke(userId, true)
        }
    }

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

    override fun onItemMove(fromPosition: Int, toPosition: Int) {
        // 如果拖动的是 status 为 1 的项，则不允许拖动
        if (modelList[fromPosition].status == 1 || modelList[toPosition].status == 1) {
            return
        }
        // 根据目标位置调整顺序，保证 status 为 1 的项在 status 为 0 的项之后
        if (fromPosition < toPosition) {
            for (i in fromPosition until toPosition) {
                Collections.swap(modelList, i, i + 1)
            }
        } else {
            for (i in fromPosition downTo toPosition + 1) {
                Collections.swap(modelList, i, i - 1)
            }
        }
        notifyItemMoved(fromPosition, toPosition)

    }

    override fun onMoveCompleted(lastFromPosition: Int, lastToPosition: Int) {
        notifyDataSetChanged()
        var result = ""
        modelList.forEach { mode ->
            if (mode.status == 0) {
                result += "${mode.userId},"
            }
        }
        if (result.isNotEmpty()) {
            result = result.dropLast(1)
        }
        SPUtils.put(AppContext.context, SPConstants.KEY_SORT_MODE, result)
    }
}

class ItemTouchHelperCallback(private val adapter: ItemTouchHelperAdapter) : ItemTouchHelper.Callback() {

    private var lastFromPosition: Int = -1
    private var lastToPosition: Int = -1

    override fun isLongPressDragEnabled(): Boolean {
        return true
    }

    override fun isItemViewSwipeEnabled(): Boolean {
        return false
    }

    override fun getMovementFlags(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder): Int {
        val dragFlags = ItemTouchHelper.LEFT or ItemTouchHelper.RIGHT
        return makeMovementFlags(dragFlags, 0)
    }

    override fun onMove(
        recyclerView: RecyclerView,
        viewHolder: RecyclerView.ViewHolder,
        target: RecyclerView.ViewHolder
    ): Boolean {
        adapter.onItemMove(viewHolder.bindingAdapterPosition, target.bindingAdapterPosition)
        lastFromPosition = viewHolder.bindingAdapterPosition
        lastToPosition = target.bindingAdapterPosition
        return true
    }

    override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {
        // Not needed for drag-and-drop
    }

    override fun clearView(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder) {
        super.clearView(recyclerView, viewHolder)
        // 在手指释放后执行操作
        if (lastFromPosition != -1 && lastToPosition != -1) {
            recyclerView.postDelayed({
                adapter.onMoveCompleted(lastFromPosition, lastToPosition)
            }, 100)
        }
        lastFromPosition = -1
        lastToPosition = -1
    }
}

interface ItemTouchHelperAdapter {
    fun onItemMove(fromPosition: Int, toPosition: Int)

    //位置更换完成
    fun onMoveCompleted(lastFromPosition: Int, lastToPosition: Int)
}

/**
 * 拖动位置之后 下次启动按照更改位置后的顺序排序
 * @param modeList Mode列表
 */
fun sortByMode(modeList: List<ModelUserEntity>): ArrayList<ModelUserEntity> {
    val idString = SPUtils.getString(AppContext.context, SPConstants.KEY_SORT_MODE, "")
    val userIDs: List<String> = idString.split(",").map { it.trim() }
    val orderedList = modeList.sortedBy { user ->
        when (user.userId) {
            in userIDs -> userIDs.indexOf(user.userId)
            else -> Int.MAX_VALUE
        }
    }
    return ArrayList(orderedList)
}

/**
 * 解绑后移除排序过的modeID
 */
fun removeUnbindModeId(usersToRemove: ArrayList<String>) {
    val idString = SPUtils.getString(AppContext.context, SPConstants.KEY_SORT_MODE, "")
    val removeIdsResult = idString.split(",").map { it.trim() }.filter { it !in usersToRemove }
    SPUtils.put(AppContext.context, SPConstants.KEY_SORT_MODE, removeIdsResult.joinToString(separator = ","))
}
