package com.fascin.chatter.main.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.bean.ModelUserEntity
import com.iandroid.allclass.lib_common.GlideLoader.loadImageCircleCrop
import com.iandroid.allclass.lib_common.utils.exts.show
import kotlinx.android.synthetic.main.itemview_bonus_model.view.bonusLeftMagin
import kotlinx.android.synthetic.main.itemview_bonus_model.view.bonusModelHeadImg
import kotlinx.android.synthetic.main.itemview_bonus_model.view.bonusModelNickName
import kotlinx.android.synthetic.main.itemview_bonus_model.view.bonusRightMagin
import kotlinx.android.synthetic.main.itemview_bonus_model.view.ivBonusModelSelect

/**
 *  @author: LXL
 *  @description: 定向建联Model
 *  @date: 2024/4/18 11:48
 */
class BonusModelListAdapter(val context: Context) : RecyclerView.Adapter<BonusModelListAdapter.ViewHolder>() {

    private val dataList = mutableListOf<ModelUserEntity>()
    private var selectedPosition = 0
    private var onItemClick: ((modelId: String, position: Int) -> Unit)? = null

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

    fun updateData(data: List<ModelUserEntity>?) {
        dataList.clear()
        if (!data.isNullOrEmpty()) {
            dataList.addAll(data)
        }
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.itemview_bonus_model, parent, false)
        )

    }

    override fun getItemCount(): Int = dataList.size

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val data = dataList[position]
        holder.itemView.apply {
            this.bonusLeftMagin.show(position == 0)
            this.bonusRightMagin.show(position == dataList.size - 1)
            this.bonusModelNickName.text = data.nickname
            this.bonusModelHeadImg.loadImageCircleCrop(context, data.avatarUrl)
            this.ivBonusModelSelect.show(position == selectedPosition, true)
            this.setOnClickListener {
                onItemClick?.invoke(data.userId, position)
            }
        }
    }

    fun setSelectedItem(position: Int) {
        selectedPosition = position
        notifyDataSetChanged()
    }

    fun setOnItemClickListener(listener: (modelId: String, position: Int) -> Unit) {
        onItemClick = listener
    }
}