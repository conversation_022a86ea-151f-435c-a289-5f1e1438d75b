package com.fascin.chatter.main.schedule

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.bean.LeaveRecordsEntity
import com.fascin.chatter.bean.LeaveRuleEntity
import com.fascin.chatter.bean.SchedulePerContentEntity
import com.fascin.chatter.bean.SchedulePerRulesEntity
import com.fascin.chatter.bean.SchedulePerTabEntity
import com.fascin.chatter.bean.ShiftChangeListEntity
import com.fascin.chatter.bean.ShiftChangeRuleEntity
import com.fascin.chatter.repository.AppRepository
import com.iandroid.allclass.lib_common.base.BaseViewModel

/**
 * @Desc: 排班相关接口viewModel
 * @Created: Quan
 * @Date: 2024/11/18
 */
class ScheduleModel : BaseViewModel() {

    val scheduleTabResult = MutableLiveData<SchedulePerTabEntity>()
    val scheduleTabError = MutableLiveData<String>()

    val scheduleContentRuleResult = MutableLiveData<SchedulePerRulesEntity>()
    val scheduleContentRuleError = MutableLiveData<String>()

    val scheduleContentResult = MutableLiveData<SchedulePerContentEntity>()
    val scheduleContentError = MutableLiveData<String>()

    val shiftResult = MutableLiveData<ShiftChangeListEntity>()
    val shiftError = MutableLiveData<String>()

    val shiftSubmitResult = MutableLiveData<String>()

    val requestLeaveRuleResult = MutableLiveData<LeaveRuleEntity>()
    val requestLeaveRuleError = MutableLiveData<String>()

    val requestLeaveResult = MutableLiveData<Any>()
    val requestLeaveError = MutableLiveData<String>()

    val shiftChangeRuleResult = MutableLiveData<ShiftChangeRuleEntity>()
    val shiftChangeRuleError = MutableLiveData<String>()

    val leaveRecordsResult = MutableLiveData<LeaveRecordsEntity>()
    val leaveRecordsError = MutableLiveData<String>()

    val leaveSubmitResult = MutableLiveData<String>()

    /**
     * 排班预览页获取tab最近三周时间
     */
    fun getSchedulePreTab() {
        compositeDisposable?.add(
            AppRepository.getSchedulePreTab()
                .subscribe({
                    scheduleTabResult.postValue(it)
                }, {
                    scheduleTabError.postValue(it.message)
                })
        )
    }

    /**
     * 获取班次内容
     */
    fun getSchedulePreContent(startDay: String, endDay: String) {
        compositeDisposable?.add(
            AppRepository.getSchedulePreContent(startDay, endDay)
                .subscribe({
                    scheduleContentResult.postValue(it)
                }, {
                    scheduleContentError.postValue(it.message)
                })
        )
    }

    /**
     * 获取排班预览页下方文案
     */
    fun getSchedulePreRule() {
        compositeDisposable?.add(
            AppRepository.getSchedulePreRule()
                .subscribe({
                    scheduleContentRuleResult.postValue(it)
                }, {
                    scheduleContentRuleError.postValue(it.message)
                })
        )
    }

    /**
     * 获取请假页下方文案
     */
    fun getLeaveRule() {
        compositeDisposable?.add(
            AppRepository.getLeaveRule()
                .subscribe({
                    requestLeaveRuleResult.postValue(it)
                }, {
                    requestLeaveRuleError.postValue(it.message)
                })
        )
    }

    /**
     * 请假
     */
    fun requestLeave(startDate: String, endDate: String) {
        compositeDisposable?.add(
            AppRepository.requestLeave(startDate, endDate)
                .subscribe({
                    requestLeaveResult.postValue(true)
                }, {
                    requestLeaveError.postValue(it.message)
                })
        )
    }

    /**
     * 获取可换班次列表
     */
    fun getShiftList(changeDay: String, type: Int) {
        compositeDisposable?.add(
            AppRepository.getChangeShiftData(changeDay, type)
                .subscribe({
                    shiftResult.postValue(it)
                }, {
                    shiftError.postValue(it.message)
                })
        )
    }

    /**
     * 获取请假页下方文案
     */
    fun getShiftChangeRule() {
        compositeDisposable?.add(
            AppRepository.getShiftChangeRule()
                .subscribe({
                    shiftChangeRuleResult.postValue(it)
                }, {
                    shiftChangeRuleError.postValue(it.message)
                })
        )
    }

    /**
     * 换班提交
     */
    fun shiftChangeSubmit(
        type: Int,
        startDate: String,
        endDate: String,
        weekDay: String,
        oldShiftId: Int,
        newShiftId: Int
    ) {
        compositeDisposable?.add(
            AppRepository.shiftChangeSubmit(
                type,
                startDate,
                endDate,
                weekDay,
                oldShiftId,
                newShiftId
            )
                .subscribe({
                    shiftSubmitResult.postValue("1")
                }, {
                    shiftSubmitResult.postValue(it.message)
                })
        )
//        shiftSubmitResult.postValue(1)
    }

    /**
     * 获取请假记录列表数据
     */
    fun getLeaveRecords() {
        compositeDisposable?.add(
            AppRepository.getLeaveRecords()
                .subscribe({
                    leaveRecordsResult.postValue(it)
                }, {
                    leaveRecordsError.postValue(it.message)
                })
        )
//        leaveRecordsResult.postValue(
//            ArrayList<LeaveRecordsEntity>().apply {
//                for (i in 1..4) {
//                    add(LeaveRecordsEntity().apply {
//                        id = i
//                        startDate = "2024-11-21"
//                        endDate = "2024-11-28"
//                        submitTime = "2024/11/21 10:00"
//                        status = i
//                    })
//                }
//            }
//        )
    }

    /**
     * 提交 取消请假
     */
    fun submitLeaveCancel(id: Int) {
        compositeDisposable?.add(
            AppRepository.submitLeaveCancel(id)
                .subscribe({
                    leaveSubmitResult.postValue("1")
                }, {
                    leaveSubmitResult.postValue(it.message)
                })
        )
//        leaveSubmitResult.postValue(true)
    }

    /**
     * 提交 暂停请假
     */
    fun submitLeaveSuspend(id: Int) {
        compositeDisposable?.add(
            AppRepository.submitLeaveSuspend(id)
                .subscribe({
                    leaveSubmitResult.postValue("1")
                }, {
                    leaveSubmitResult.postValue(it.message)
                })
        )
//        leaveSubmitResult.postValue(true)
    }

    class ViewModeFactory : ViewModelProvider.Factory {
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            return ScheduleModel() as T
        }
    }
}