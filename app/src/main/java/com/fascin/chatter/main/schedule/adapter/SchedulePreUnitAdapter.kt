package com.fascin.chatter.main.schedule.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.bean.SchedulePerItemEntity

/**
 * @Desc: 排班预览表格单元基础item
 * @Created: Quan
 * @Date: 2024/11/18
 */
class SchedulePreUnitAdapter : RecyclerView.Adapter<SchedulePreUnitAdapter.ViewHolder>() {

    private val dataList = mutableListOf<SchedulePerItemEntity>()

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

    fun updateData(data: List<SchedulePerItemEntity>?) {
        dataList.clear()
        if (!data.isNullOrEmpty()) {
            dataList.addAll(data)
        }
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.itemview_schedule_pre_unit, parent, false)
        )

    }

    override fun getItemCount(): Int = dataList.size

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = dataList[position]
        holder.itemView?.also {
        }
    }
}