package com.fascin.chatter.main.profile.invite

import android.os.Bundle
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.iandroid.allclass.lib_common.base.ChatterBaseActivity
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.immersiveStatusBarWithMargin
import com.iandroid.allclass.lib_common.utils.exts.show
import kotlinx.android.synthetic.main.activity_invite_history.ivInviteHistoryBack
import kotlinx.android.synthetic.main.activity_invite_history.llInviteSuccessHistory
import kotlinx.android.synthetic.main.activity_invite_history.rvInviteSuccessHistory
import kotlinx.android.synthetic.main.activity_invite_history.tvSuccessHistoryEmpty

/**
 *  @author: LXL
 *  @description: 邀请历史
 *  @date: 2024/9/4 11:00
 */
class InviteHistoryActivity : ChatterBaseActivity() {
    private var inviteHistoryAdapter: InviteHistoryAdapter? = null

    private val viewModel by lazy {
        ViewModelProvider(this, InviteFriendsViewModel.ViewModeFactory())[InviteFriendsViewModel::class.java]
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_invite_history)
    }

    override fun initBaseContent() {
        super.initBaseContent()
        AppModule.userActive()
        showTitleBar(false)
        immersiveStatusBarWithMargin(ivInviteHistoryBack)

        inviteHistoryAdapter = InviteHistoryAdapter()
        rvInviteSuccessHistory.adapter = inviteHistoryAdapter
        rvInviteSuccessHistory.layoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)

        viewModel.invitationHistory()
        viewModel.inviteHistoryResult.observe(this) { entity ->
            inviteHistoryAdapter?.updateData(entity.list)
            llInviteSuccessHistory.show(!entity.list.isNullOrEmpty())
            tvSuccessHistoryEmpty.show(entity.list.isNullOrEmpty())
        }

        ivInviteHistoryBack.clickWithTrigger {
            finish()
        }
    }

    override fun immersiveIntoStatusBar(): Boolean {
        return true
    }
}