package com.fascin.chatter.main.profile.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.bean.ModelUserEntity
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.GlideLoader.loadImage
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.toPx
import kotlinx.android.synthetic.main.itemview_profile_flash_chat_model.view.ivSelect
import kotlinx.android.synthetic.main.itemview_profile_flash_chat_model.view.nickName
import kotlinx.android.synthetic.main.itemview_profile_flash_chat_model.view.userHead

/**
 * @Desc: 个人主页flash chat 弹窗model列表
 * @Created: Quan
 * @Date: 2023/9/18
 */
class ProfileFlashChatModelAdapter(var context: Context?) :
    RecyclerView.Adapter<ProfileFlashChatModelAdapter.ViewHolder>() {

    private val dataList = mutableListOf<ModelUserEntity>()
    var selectId: String = ""// 选中的model id
    private var selectHolder: ViewHolder? = null// 选中的model item

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

    fun updateData(data: List<ModelUserEntity>?) {
        dataList.clear()
        if (!data.isNullOrEmpty()) {
            dataList.addAll(data)
        }
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutInflater.from(context).inflate(
                R.layout.itemview_profile_flash_chat_model,
                parent,
                false
            )
        )
    }

    override fun getItemCount(): Int {
        return dataList.size
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = dataList[position]
        holder.itemView.run {
            userHead.loadImage(AppContext.context, item.avatarUrl, 8.toPx)
            nickName.text = item.nickname
            // 默认选中第一个
            if (selectId.isEmpty()) {
                selectId = item.userId
                selectHolder = holder
            }
            ivSelect.setImageResource(
                if (selectId == item.userId) R.mipmap.ic_model_selected
                else R.mipmap.ic_model_unselect
            )
            clickWithTrigger {
                if (selectId != item.userId) {
                    selectId = item.userId
                    holder.itemView.ivSelect.setImageResource(R.mipmap.ic_model_selected)
                    selectHolder?.itemView?.ivSelect?.setImageResource(R.mipmap.ic_model_unselect)
                    selectHolder = holder
                }
            }
        }
    }
}