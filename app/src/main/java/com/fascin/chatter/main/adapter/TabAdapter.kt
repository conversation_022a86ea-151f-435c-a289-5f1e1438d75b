package com.fascin.chatter.main.adapter

import android.os.Bundle
import androidx.annotation.NonNull
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.fascin.chatter.main.MixListFragment
import com.iandroid.allclass.lib_common.Values
import com.iandroid.allclass.lib_common.beans.HomeTabEntity
import com.iandroid.allclass.lib_common.beans.MixPageEntity
import com.iandroid.allclass.lib_common.utils.exts.objToJsonString


class TabAdapter(
    list: List<HomeTabEntity>?,
    fm: FragmentManager,
    @NonNull lifecycle: Lifecycle
) :
    FragmentStateAdapter(fm, lifecycle) {
    var tabList: ArrayList<HomeTabEntity> = ArrayList()
    var iTabFragmentCreator: IFragmentTabCreator? = null
    private var offsetIndex = 1L
    var fragmentHashList: HashMap<Int, Fragment> = HashMap()

    init {
        if (!list.isNullOrEmpty()) {
            tabList.addAll(list)
        }
    }

    fun getPositionByTabId(tabId: Int): Int {
        for (index in tabList.indices) {
            if (tabList[index].id == tabId) {
                return index
            }
        }
        return -1
    }

    fun updateData(list: List<HomeTabEntity>?) {
        tabList.clear()
        notifyDataSetChanged()
        if (!list.isNullOrEmpty()) {
            offsetIndex += list.size
            tabList.addAll(list)
            notifyDataSetChanged()
        }
    }

    interface IFragmentTabCreator {
        fun onCreateTabFragment(tab: HomeTabEntity): Fragment?
    }

    override fun getItemCount(): Int {
        return tabList.size
    }

    fun getFragment(position: Int): Fragment? {
        return fragmentHashList?.takeIf { it.containsKey(position) }?.run {
            this.getValue(position)
        } ?: null
    }

    override fun getItemId(position: Int): Long {
        return offsetIndex + position
    }

    override fun containsItem(itemId: Long): Boolean {
        for (tabItem in tabList) {
            if (getItemId(tabList.indexOf(tabItem)) == itemId) {
                return true
            }
        }
        return false
    }

    override fun createFragment(position: Int): Fragment {
        var tabItem = tabList[position]
        var fragment = iTabFragmentCreator?.onCreateTabFragment(tabItem)
        if (fragment == null)
            fragment = MixListFragment().also { fragment ->
                fragment.arguments = Bundle().also {
                    it.putString(
                        Values.intentJsonParam,
                        MixPageEntity(
                            tabItem.title,
                            tabItem.url,
                            tabItem.id
                        ).objToJsonString<MixPageEntity>()
                    )
                }
            }

        if (fragment != null) {
            fragmentHashList[position] = fragment
        }

        return fragment
    }

}