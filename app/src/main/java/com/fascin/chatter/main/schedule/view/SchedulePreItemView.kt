package com.fascin.chatter.main.schedule.view

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import com.fascin.chatter.R
import com.fascin.chatter.main.schedule.ScheduleConst
import com.iandroid.allclass.lib_common.utils.exts.show
import kotlinx.android.synthetic.main.itemview_schedule_pre.view.itemBg
import kotlinx.android.synthetic.main.itemview_schedule_pre.view.ivItem

/**
 * @Desc: 排班展示页item view
 * @Created: Quan
 * @Date: 2024/11/18
 */
class SchedulePreItemView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    init {
        View.inflate(context, R.layout.itemview_schedule_pre, this)
    }

    fun setViewData(abnormalStatus: Int, status: Int, isToDay: <PERSON>ole<PERSON>) {
        if (status == ScheduleConst.STATUS_CANCELED) {
            // 被取消的
            itemBg.setBackgroundResource(com.iandroid.allclass.lib_common.R.color.color_F74E57)
            ivItem.setBackgroundResource(R.drawable.ic_schedule_canceled)
            ivItem.show(true)
        } else if (abnormalStatus == ScheduleConst.STATUS_LATE) {
            // 迟到
            itemBg.setBackgroundResource(com.iandroid.allclass.lib_basecore.R.color.cl_9370DB)
            ivItem.setBackgroundResource(R.drawable.ic_schedule_late_work)
            ivItem.show(true)
        } else if (abnormalStatus == ScheduleConst.STATUS_EARLY) {
            // 早退
            itemBg.setBackgroundResource(com.iandroid.allclass.lib_basecore.R.color.cl_9370DB)
            ivItem.setBackgroundResource(R.drawable.ic_schedule_early_left)
            ivItem.show(true)
        } else if (status == ScheduleConst.STATUS_WORK) {
            // 有班次
            itemBg.setBackgroundResource(
                if (isToDay) R.color.color_6ad84a
                else com.iandroid.allclass.lib_common.R.color.color_d9d9d9
            )
            ivItem.show(false)
        } else if (status == ScheduleConst.STATUS_LEAVE) {
            // 请假了
            itemBg.setBackgroundResource(R.color.cr_bfbfbf)
            ivItem.setBackgroundResource(R.drawable.ic_schedule_leave_request)
            ivItem.show(true)
        } else {
            itemBg.setBackgroundResource(com.iandroid.allclass.lib_common.R.color.transparent)
            ivItem.show(false)
        }
    }
}