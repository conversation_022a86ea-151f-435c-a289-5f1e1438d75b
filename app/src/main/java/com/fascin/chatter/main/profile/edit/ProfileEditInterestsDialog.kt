package com.fascin.chatter.main.profile.edit

import android.graphics.Color
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.view.children
import androidx.core.view.forEach
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.R
import com.fascin.chatter.component.views.SUButtonStatus
import com.fascin.chatter.main.profile.ProfileViewModel
import com.iandroid.allclass.lib_common.base.BaseDialogFragment
import com.iandroid.allclass.lib_common.beans.ProfileParams
import com.iandroid.allclass.lib_common.beans.ProfileSettingStep
import com.iandroid.allclass.lib_common.beans.ProfileTagEntity
import com.iandroid.allclass.lib_common.utils.ToastUtils
import com.iandroid.allclass.lib_common.utils.exts.toPx
import kotlinx.android.synthetic.main.dialog_profile_edit_interests.*

class ProfileEditInterestsDialog(
    private val completeBlock: () -> Unit
) : BaseDialogFragment() {

    private val viewModel by lazy {
        ViewModelProvider(
            this,
            ProfileViewModel.ViewModeFactory()
        )[ProfileViewModel::class.java]
    }
    private val profileParams by lazy {
        ProfileParams.get().apply {
            resetByCache()
        }
    }

    override fun onStart() {
        super.onStart()
        setBottomPopupAttr(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
        )
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_profile_edit_interests, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        profile_edit_interests_root.setOnClickListener {
            dismissAllowingStateLoss()
        }
        initProfileTagView(profileParams.optionTags)
        profile_edit_interests_save.setText(getString(R.string.profile_edit_save))
        profile_edit_interests_save.setButtonStatus(SUButtonStatus.Activated)
        profile_edit_interests_save.setOnClickListener {
            if (!checkMaxInterests()) return@setOnClickListener
            profile_edit_interests_save.setButtonStatus(SUButtonStatus.Loading)
            profile_edit_interests_flow.isEnabled = false
            profileParams.step = ProfileSettingStep.Tag
            profileParams.tag = getSelectedTags()
        }
        viewModel.profileSettingResult.observe(this) {
            profile_edit_interests_save.setButtonStatus(SUButtonStatus.Activated)
            profile_edit_interests_flow.isEnabled = true
            dismissAllowingStateLoss()
            completeBlock()
        }

        viewModel.profileSettingError.observe(this) {
            profile_edit_interests_save.setButtonStatus(SUButtonStatus.Activated)
            profile_edit_interests_flow.isEnabled = true
        }

        viewModel.profileTagList.observe(this) {
            profileParams.optionTags = it
            initProfileTagView(it)
        }

        viewModel.getTagList()
    }

    private fun initProfileTagView(optionTags: List<ProfileTagEntity>) {
        profile_edit_interests_flow.removeAllViews()
        val tagTitles = profileParams.tagTitles.split(",")
        optionTags.forEach {
            val tagView = makeProfileTagView(it)
            profile_edit_interests_flow.addView(
                tagView,
                ViewGroup.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, 34.toPx)
            )
            tagView.isSelected = tagTitles.contains(it.title)
        }
    }


    private fun makeProfileTagView(profileTag: ProfileTagEntity): AppCompatTextView {

        return AppCompatTextView(requireContext()).apply {
            setTextColor(Color.parseColor("#262626"))
            setBackgroundResource(R.drawable.bg_interest_tag)
            gravity = Gravity.CENTER
            isSingleLine = true
            setPadding(10.toPx, 5.toPx, 10.toPx, 5.toPx)
            text = profileTag.title
            tag = profileTag.id
            setOnClickListener {
                var selectNum = 0
                profile_edit_interests_flow.forEach { itemView ->
                    if (itemView.isSelected) {
                        selectNum++
                    }
                }
                if (selectNum >= 8 && !it.isSelected) {
                    ToastUtils.showToast(R.string.reg_too_much_tag)
                } else {
                    it.isSelected = !it.isSelected
                }
            }
        }
    }

    private fun getSelectedTags(): String {
        val tagIds = mutableListOf<String>()
        profile_edit_interests_flow.forEach {
            if (it.isSelected) {
                tagIds.add(it.tag.toString())
            }
        }
        return tagIds.joinToString(",")
    }

    private fun checkMaxInterests(): Boolean {
        if (profile_edit_interests_flow.children.filter { it.isSelected }.count() > 8) {
            ToastUtils.showToast(R.string.reg_too_much_tag)
            return false
        }
        return true
    }
}