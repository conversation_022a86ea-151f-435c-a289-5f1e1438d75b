package com.fascin.chatter.main.schedule

import android.util.Log
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale
import java.util.TimeZone

/**
 * @Desc: 排班时间转换器
 * @Created: Quan
 * @Date: 2024/11/19
 */
object ScheduleDate {

    // 服务端UTC-5当前日期, yyyy-MM-dd
    var SERVER_CURRENT_DATE = ""

    fun formatToMonthDay(date: String): String {
        if (date.isNullOrEmpty()) return ""
        val parts = date.split("-")
        val month = parts[1].toInt() // 转换为数字去掉前导零
        val day = parts[2].toInt()
        return "$month/$day"
    }


    /**
     * 通过起始日期，获取一周的日期
     */
    fun getWeekDates(startDate: String): List<String> {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val calendar = Calendar.getInstance()

        // 解析起始日期
        calendar.time = dateFormat.parse(startDate) ?: return emptyList()

        // 计算一周的日期
        val weekDates = mutableListOf<String>()
        for (i in 0 until 7) {
            weekDates.add(dateFormat.format(calendar.time))
            calendar.add(Calendar.DAY_OF_YEAR, 1)
        }
        return weekDates
    }

    /**
     * 获取 UTC-5 当前日期
     */
    fun getUtc5TodayDate(): String {
        // 使用 Calendar 获取 UTC-5 日期（兼容 Android 21 以下）
        val calendar = Calendar.getInstance(TimeZone.getTimeZone("UTC-5"))
        val year = calendar.get(Calendar.YEAR)
        val month = calendar.get(Calendar.MONTH) + 1 // Calendar.MONTH 是从0开始的
        val day = calendar.get(Calendar.DAY_OF_MONTH)
        return "$year-${"%02d".format(month)}-${"%02d".format(day)}"

    }

    /**
     * 用于获取给定起始日期后 30 天的日期和对应的星期几，并返回格式化的yyyy-MM-dd EEE列表
     */
    fun getNextXDays(startDate: String, days: Int): List<String> {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.US)
        val outputFormat = SimpleDateFormat("yyyy-MM-dd EEE", Locale.US)
        val calendar = Calendar.getInstance()

        // 设置起始日期
        calendar.time = dateFormat.parse(startDate) ?: return emptyList()

        val result = mutableListOf<String>()
        for (i in 0 until days) {
            val date = outputFormat.format(calendar.time)
            result.add(date)
            calendar.add(Calendar.DAY_OF_YEAR, 1)
        }
        return result
    }

    /**
     * 将yyyy-MM-dd EEE格式的数据转换为年月日周的map
     */
    fun splitDateString(dateString: String?, toInt: Boolean = true): Map<String, String>? {
        if (dateString.isNullOrEmpty()) return null
        val regex = """\d{4}-\d{2}-\d{2} \w{3}""".toRegex()
        if (!regex.matches(dateString)) {
            Log.e("splitDateString", "输入格式不正确：$dateString")
            return null
        }
        // 按空格分割字符串
        val parts = dateString.split(" ")
        // 按 "-" 分割年月日
        val dateParts = parts[0].split("-")

        // 转换月和日为整数以去掉前导零
        val year = dateParts[0]
        val month = if (toInt) {
            // 转为整数去掉前导零
            dateParts[1].toInt()
        } else dateParts[1]
        val day = if (toInt) {
            // 转为整数去掉前导零
            dateParts[2].toInt()
        } else dateParts[2]
        val weekday = parts[1]

        return mapOf(
            "y" to year,
            "m" to month.toString(),
            "d" to day.toString(),
            "w" to weekday
        )
    }


    /**
     * 获取今日字符串，格式为 yyyy-MM-dd
     */
    fun getTodayDateStr(): String {
        return SERVER_CURRENT_DATE.ifEmpty {
            getUtc5TodayDate()
        }
    }

    /**
     * 获取mdw字符串，格式为 MM/dd 周几
     * @param ymdw yyyy-MM-dd 周几
     */
    fun getMDWStr(ymdw: String?, toInt: Boolean = true): String {
        // 年月日周
        val ymdwMap = splitDateString(ymdw, toInt)
        if (ymdwMap.isNullOrEmpty() || ymdw.isNullOrEmpty()) return ""
        val currDate = getTodayDateStr()
        val week = if (ymdw.contains(currDate)) "Today" else ymdwMap["w"]
        return "${ymdwMap["m"]}/${ymdwMap["d"]} $week"
    }

    /**
     * 获取ymd字符串，格式为 yyyy-MM-dd
     */
    fun getYMDStr(ymdw: String?, toInt: Boolean = true): String {
        // 年月日周
        val ymdwMap = splitDateString(ymdw, toInt)
        if (ymdwMap.isNullOrEmpty() || ymdw.isNullOrEmpty()) return ""
        return "${ymdwMap["y"]}-${ymdwMap["m"]}-${ymdwMap["d"]}"
    }

    /**
     * 获取ymd字符串，格式为 yyyy-MM-dd
     */
    fun getYMDStr(ymdwMap: Map<String, String>?): String {
        // 年月日周
        if (ymdwMap.isNullOrEmpty()) return ""
        return "${ymdwMap["y"]}-${ymdwMap["m"]}-${ymdwMap["d"]}"
    }

    /**
     * 判断两个时间哪个更早
     * @return true: date1更早，false: date2更早
     */
    fun isEarlier(date1: String, date2: String): Boolean {
        val formatter = SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH) // 定义格式化器
        val parsedDate1 = formatter.parse(date1)
        val parsedDate2 = formatter.parse(date2)
        return parsedDate1?.before(parsedDate2) == true // 如果 date1 早于 date2 返回 true
    }

    /**
     * 获取月/日 周几字符串，格式为 MM/dd 周几
     * @param input yyyy-MM-dd
     */
    fun getMDWStrForYMD(input: String): String {
        // 定义输入和输出的格式化器
        val inputFormatter = SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH)
        val calendar = Calendar.getInstance()

        // 解析日期
        calendar.time = inputFormatter.parse(input)!!

        // 手动拼接 M/d EEE 格式
        val month = calendar.get(Calendar.MONTH) + 1 // Calendar.MONTH 从 0 开始
        val day = calendar.get(Calendar.DAY_OF_MONTH)
        val weekDay = SimpleDateFormat("EEE", Locale.ENGLISH).format(calendar.time)
        return "$month/$day $weekDay"
    }

    /**
     * 获取两个yyyy-MM-dd时间相差多少天
     */
    fun getDaysDifference(startDay: String, endDay: String): Long {
        // 定义日期格式化器
        val formatter = SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH)
        // 解析输入的日期字符串
        val startDate = formatter.parse(startDay)!!
        val endDate = formatter.parse(endDay)!!
        // 计算时间差（以毫秒为单位），再换算成天
        val diffInMillis = endDate.time - startDate.time
        return diffInMillis / (1000 * 60 * 60 * 24) // 转换为天
    }

    /**
     * 将 UTC-5 的时间段转换为设备时区的时间段 (支持 12 小时制 AM/PM 格式)
     * @param timeRange 输入的时间段，例如 "11-2 pm"
     * @param sourceTimeZone 源时区（如 "UTC-5"）
     * @return 转换后的设备时区时间段，例如 "12:00-3:00 pm"
     */
    fun convertTimeRangeToDeviceTimeZone12Hour(
        timeRange: String,
        sourceTimeZone: String
    ): String {
        try {
            val difference = getTimeDifferenceWithSign(sourceTimeZone, TimeZone.getDefault().id)
            // 分割时间段
            val parts = timeRange.split(" ") // ["6-9", "am"]
            val hours = parts[0].split("-")  // ["6", "9"]
            val period = parts[1]

            val startPeriod = if (hours[0].toInt() > hours[1].toInt()) {
                if (period == "am") "pm" else "am"
            } else period

            // 转成24小时制
            val startTimeStr = if (startPeriod == "pm") {
                if (hours[0].toInt() == 12) "0:00"
                else "${(hours[0].toInt() + 12) % 24}:00"
            } else {
                "${hours[0]}:00"
            }
            val endTimeStr = if (period == "pm") {
                if (hours[1].toInt() == 12) "0:00"
                else "${(hours[1].toInt() + 12) % 24}:00"
            } else {
                "${hours[1]}:00"
            }
            // "h a"格式的时间
            val startTime = convert24HourTimeTo12Hour(startTimeStr, difference)
            val endTime = convert24HourTimeTo12Hour(endTimeStr, difference)
            // 开始时间是半夜12点时，返回0 am
            if (startTime == "12 AM") return "0-${endTime.lowercase()}"
            // 结束时间是半夜12点时，返回12 pm
            if (endTime == "12 AM") return "${startTime.split(" ")[0]}-12 pm"
            // 结束时间是正午12点时，返回12 am
            if (endTime == "12 PM") return "${startTime.split(" ")[0]}-12 am"
            // 否则返回正常时间
            return "${startTime.split(" ")[0]}-${endTime.lowercase()}"
        } catch (e: Exception) {
            return ""
        }
    }

    /**
     * 获取两个时区之间的时间差(正负)
     */
    fun getTimeDifferenceWithSign(timeZone1: String, timeZone2: String): Int {
        // 明确设置时区偏移量，UTC-5 为 -5 小时，Asia/Shanghai 为 +8 小时
        val timeZone1Offset =
            if (timeZone1 == "UTC-5") -5 * 60 * 60 * 1000
            else TimeZone.getTimeZone(timeZone1).getOffset(System.currentTimeMillis())

        val timeZone2Offset = TimeZone.getTimeZone(timeZone2).getOffset(System.currentTimeMillis())

        // 计算时区之间的差异（单位：毫秒）
        val timeDifferenceMillis = timeZone2Offset - timeZone1Offset

        // 将差异转换为小时
        return (timeDifferenceMillis / (1000 * 60 * 60)).toInt()
    }

    /**
     * 将24小时制的A时区时间，通过时区时差，计算得到一个B时区的时间，并转成12小时制（h a）
     */
    fun convert24HourTimeTo12Hour(
        sourceTime: String,   // 24 小时制时间，格式为 "HH:mm"
        timeDifference: Int   // 时区差异（单位：小时）
    ): String {
        try {
            // Step 1: 解析 24 小时制的时间
            val inputFormat = SimpleDateFormat("HH:mm", Locale.ENGLISH)
            val date = inputFormat.parse(sourceTime) ?: null

            // Step 2: 加上时区差
            val calendar = Calendar.getInstance()
            calendar.time = date!!
            calendar.add(Calendar.HOUR, timeDifference) // 根据时区差调整时间

            // Step 3: 转换为 12 小时制的格式 "h:mm a"
            val outputFormat = SimpleDateFormat("h a", Locale.ENGLISH)
            return outputFormat.format(calendar.time)
        } catch (e: Exception) {
            return ""
        }
    }
}