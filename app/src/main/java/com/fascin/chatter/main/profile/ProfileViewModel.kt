package com.fascin.chatter.main.profile

import android.util.Log
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.bean.ChatNoticeEntity
import com.fascin.chatter.bean.IMPenaltiesData
import com.fascin.chatter.bean.ImproveEntity
import com.fascin.chatter.bean.MatchPolicyEntity
import com.fascin.chatter.bean.MineInfoEntity
import com.fascin.chatter.bean.MpcEntity
import com.fascin.chatter.bean.MsgEmojiEntity
import com.fascin.chatter.bean.MsgGreetTabEntity
import com.fascin.chatter.bean.NotMatchModelEntity
import com.fascin.chatter.bean.PenaltyHistoryEntity
import com.fascin.chatter.bean.PrivacyTabEntity
import com.fascin.chatter.bean.UIPrivateListResult
import com.fascin.chatter.bean.event.UIEventUserBlock
import com.fascin.chatter.im.IMFlashChatHelper
import com.fascin.chatter.repository.AppRepository
import com.fascin.chatter.repository.HomeRepository
import com.iandroid.allclass.lib_common.base.BaseViewModel
import com.iandroid.allclass.lib_common.beans.GreetItem
import com.iandroid.allclass.lib_common.beans.MessageSettingEntity
import com.iandroid.allclass.lib_common.beans.MsgContentEntity
import com.iandroid.allclass.lib_common.beans.ProfileSettingEntity
import com.iandroid.allclass.lib_common.beans.ProfileTagEntity
import com.iandroid.allclass.lib_common.beans.UserEntity
import com.iandroid.allclass.lib_common.event.EventProfileUpdate
import com.iandroid.allclass.lib_common.network.ErrorCodeCheckUtils
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.ToastUtils

/**
created by wangkm
on 2020/9/12.
 */
class ProfileViewModel : BaseViewModel() {
    val profileTagList = MutableLiveData<List<ProfileTagEntity>>()
    val profileTagError = MutableLiveData<Any>()
    val userInfoResult = MutableLiveData<UserEntity>()
    val userInfoError = MutableLiveData<Any>()

    val mineInfoResult = MutableLiveData<MineInfoEntity>()
    val mineInfoError = MutableLiveData<Any>()

    val profileSettingResult = MutableLiveData<ProfileSettingEntity>()
    val profileSettingError = MutableLiveData<Any>()

    var delAccountResult = MutableLiveData<Any>()

    val blockStatus = MutableLiveData<Int>()
    val privacyMediaList = MutableLiveData<UIPrivateListResult>()
    val privacyMediaListError = MutableLiveData<Any>()
    val privacyTagList = MutableLiveData<List<PrivacyTabEntity>>()
    val privacyTagListError = MutableLiveData<Any>()
    val privacyCollectError = MutableLiveData<Long>()

    val messageSettingAdd = MutableLiveData<MsgContentEntity>()
    val messageSettingAddError = MutableLiveData<Any>()
    val messageSettingModify = MutableLiveData<MsgContentEntity>()
    val messageSettingModifyError = MutableLiveData<Any>()
    val messageSettingDelUser = MutableLiveData<MsgContentEntity>()
    val messageSettingDelAll = MutableLiveData<MessageSettingEntity>()

    val notMatchModelsResult = MutableLiveData<NotMatchModelEntity>()
    val notMatchModelsError = MutableLiveData<Any>()

    val flashChatMsgResult = MutableLiveData<Boolean>()

    val msgGreetsResult = MutableLiveData<List<MsgGreetTabEntity>>()
    val msgEmojiResult = MutableLiveData<List<MsgEmojiEntity>>()
    val msgSayListHiResult = MutableLiveData<ArrayList<GreetItem>>()
    val sayHiResult = MutableLiveData<Boolean>()
    val chatMpcDataResult = MutableLiveData<MpcEntity>()
    val penaltyHistoryResult = MutableLiveData<PenaltyHistoryEntity>()
    val penaltyHistoryError = MutableLiveData<Any>()
    val improveResult = MutableLiveData<ImproveEntity>()
    val selectModelResult = MutableLiveData<Int>()

    fun getMineInfo() {
        compositeDisposable?.add(
            AppRepository.getMineInfo()
                .subscribe({
                    mineInfoResult.postValue(it)
                }, {
                    mineInfoError.postValue(Any())
                    ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
                })
        )
    }

    fun getUserInfo(userId: String, from: Int = 0) {
        compositeDisposable?.add(
            AppRepository.getUserInfo(userId, from)
                .subscribe({
                    userInfoResult.postValue(it)
                }, {
                    userInfoError.postValue(Any())
                    ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
                })
        )
    }

    fun userBan(userId: String, type: Int) {
        compositeDisposable?.add(
            HomeRepository.userBan(userId, type)
                .subscribe({
                    if (type == 1) SimpleRxBus.post(UIEventUserBlock(userId))
                    blockStatus.postValue(if (type == 1) 1 else 0)
                    ToastUtils.showToast(it)
                }, {
                    ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
                })
        )
    }

    fun getTagList() {
        compositeDisposable?.add(
            AppRepository.getTagList()
                .subscribe({
                    profileTagList.postValue(it)
                }, {
                    profileTagError.postValue(Any())
                })
        )
    }

    /**
     * 获取私密资源列表
     * @param mediaType 要获取资源类型 -1不区分  0：图片 1：视频
     * @param tag 按标签获取资源 -1:收藏的资源 0:全部资源  n：标签id
     */
    fun getAlbumMediaList(
        imId: String,
        lastId: String,
        type: Int,
        from: Int,
        mediaType: Int = -1,
        tag: Int = 0
    ) {
        compositeDisposable?.add(
            AppRepository.getAlbumMediaList(imId, lastId, type, from, mediaType, tag)
                .subscribe({
                    privacyMediaList.postValue(UIPrivateListResult(it, lastId))
                }, {
                    privacyMediaListError.postValue(Any())
                    ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
                })
        )
    }

    /**
     * 获取私密列表页tag数据
     */
    fun getPrivacyTagList() {
        compositeDisposable?.add(
            AppRepository.getPrivacyTagList()
                .subscribe({
                    privacyTagList.postValue(it)
                }, {
                    privacyTagListError.postValue(Any())
                })
        )
    }

    fun collectMedia(mediaId: Long, collect: Boolean) {
        compositeDisposable?.add(
            AppRepository.collectMedia(mediaId.toString(), collect)
                .subscribe({
                }, {
                    privacyCollectError.postValue(mediaId)
                    ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
                })
        )
    }

    /**
     * 添加messageSet中model的问候语/快捷消息
     */
    fun addMessageOrGreeting(userId: String, type: Int, content: String) {
        Log.e("addMessageOrGreeting", "type: $type")
        compositeDisposable?.add(
            AppRepository.addMessageOrGreeting(userId, type, content)
                .subscribe({
                    messageSettingAdd.postValue(it)
                }, {
                    ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
                    messageSettingAddError.postValue(ErrorCodeCheckUtils.getError(it))
                })
        )
    }

    /**
     * 修改messageSet中model的问候语/快捷消息
     */
    fun modifyMessageOrGreeting(userId: String, msgID: Int, content: String) {
        compositeDisposable?.add(
            AppRepository.modifyMessageOrGreeting(msgID, content)
                .subscribe({
                    it.mid = userId
                    it.id = msgID
                    it.content = content
                    messageSettingModify.postValue(it)
                }, {
                    ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
                    messageSettingModifyError.postValue(ErrorCodeCheckUtils.getError(it))
                })
        )
    }

    /**
     * 删除messageSet中model的问候语/快捷消息
     */
    fun delMessageOrGreeting(userId: String, msgID: Int, isAll: Boolean) {
        compositeDisposable?.add(
            AppRepository.delMessageOrGreeting(userId, msgID)
                .subscribe({
                    if (isAll)
                        messageSettingDelAll.postValue(it)
                    else
                        messageSettingDelUser.postValue(MsgContentEntity(msgID, userId))
                }, {
                    ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
                })
        )
    }

    fun openPrivacyMsg(to_msgid: String, from_msgid: String, media_id: Int) {
        compositeDisposable?.add(
            AppRepository.openPrivacyMsg(to_msgid, from_msgid, media_id)
                .subscribe({
                }, {
                    ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
                })
        )
    }

    fun openPrivacyMultipleMsg(to_msgid: String, from_msgid: String) {
        compositeDisposable?.add(
            AppRepository.openPrivacyMultipleMsg(to_msgid, from_msgid)
                .subscribe({
                }, {
                    ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
                })
        )
    }

    fun openGiftMessage(toMsgId: String, fromMsgId: String) {
        compositeDisposable?.add(
            AppRepository.openGift(fromMsgId, toMsgId)
                .subscribe({
                }, {
                    ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
                })
        )
    }

    /**
     * 发送flash chat msg
     */
    fun sendFlashChatMsg(
        userId: String?,
        modelId: String?,
        msg: String?,
        stamp: String = "",
        from: Int?,
    ) {
        compositeDisposable?.add(
            AppRepository.sendFlashChat(userId, modelId, msg, stamp, from)
                .subscribe({
                    if (it != null)
                        IMFlashChatHelper.fcCountChange(it.userId, it.fcCount)
                    flashChatMsgResult.postValue(true)
                }, {
                    ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
                    flashChatMsgResult.postValue(false)
                })
        )
    }

    fun getNotMatchModel(userId: String?) {
        compositeDisposable?.add(
            AppRepository.getNotMatchModel(userId)
                .subscribe({
                    notMatchModelsResult.postValue(it)
                }, {})
        )
    }

    fun requestGreet(userId: String?) {
        compositeDisposable?.add(
            AppRepository.requestGreet(userId)
                .subscribe({
                    msgGreetsResult.postValue(it)
                }, {
                    ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
                })
        )
    }

    fun requestEmojis() {
        compositeDisposable?.add(
            AppRepository.requestEmojis()
                .subscribe({
                    msgEmojiResult.postValue(it)
                }, {
                    ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
                })
        )
    }

    fun requestSayHiMsg(imId: String) {
        compositeDisposable?.add(
            AppRepository.requestSayHiMsg(imId)
                .subscribe({
                    msgSayListHiResult.postValue(it.list)
                }, {
                    ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
                })
        )
    }

    fun matchDialogSayHi(
        imId: String, gid: String, content: String
    ) {
        compositeDisposable?.add(
            AppRepository.matchDialogSayHi(imId, gid, content)
                .subscribe({
                    sayHiResult.postValue(true)
                }, {
                    ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
                    sayHiResult.postValue(false)
                })
        )
    }

    /**
     * 更新会话隐身、在线、免打扰开关状态
     */
    fun updateChatFlag(imUid: String, chatFlag: Long, onComplete: (List<String>?) -> Unit) {
        compositeDisposable?.add(
            AppRepository.updateChatFlag(imUid, chatFlag)
                .subscribe({
                    onComplete(it.imIdList)
                }, {
                })
        )
    }

    /**
     * 获取聊天页Chat Notice
     */
    fun getChatNotice(userId: String, onComplete: (List<ChatNoticeEntity>) -> Unit) {
        compositeDisposable?.add(
            AppRepository.getChatNotice(userId)
                .subscribe({
                    onComplete.invoke(it)
                }, {

                })
        )
    }

    /**
     * 聊天页 关闭指定Chat Notice 通知
     */
    fun chatNoticeClose(userId: String, id: Int) {
        compositeDisposable?.add(
            AppRepository.chatNoticeClose(userId, id)
                .subscribe({}, {})
        )
    }

    /**
     * 通知权限开关状态
     */
    fun pushFlagUpdate(pushFlag: Int) {
        compositeDisposable?.add(
            AppRepository.pushFlagUpdate(pushFlag)
                .subscribe({}, {})
        )
    }

    /**
     * 获取低流量配置
     */
    fun matchPolicyGet(callback: (MatchPolicyEntity) -> Unit = {}) {
        compositeDisposable?.add(
            AppRepository.matchPolicyGet()
                .subscribe({
                    callback.invoke(it)
                }, {
                    ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
                })
        )
    }

    /**
     * 设置低流量
     */
    fun matchPolicySet(enable: Int, callback: (Boolean) -> Unit = {}) {
        compositeDisposable?.add(
            AppRepository.matchPolicySet(enable)
                .subscribe({
                    callback.invoke(true)
                }, {
                    ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
                    callback.invoke(false)
                })
        )
    }

    /**
     * 获取improve method弹窗的数据
     */
    fun getImproveMethod(id: String) {
        compositeDisposable?.add(
            AppRepository.getImproveMethod(id)
                .subscribe({
                    improveResult.postValue(it)
                }, {
                    ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
                })
        )
    }

    /**
     * 主播绩效中心
     */
    fun chatMpcData() {
        compositeDisposable?.add(
            AppRepository.chatMpcData()
                .subscribe({
                    chatMpcDataResult.postValue(it)
                }, {
                    ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
                })
        )
    }

    /**
     * 检查model是否被用户拉黑
     */
    fun checkIsBlock(imID: String, isBlock: (Boolean) -> Unit = {}) {
        compositeDisposable?.add(
            AppRepository.checkIsBlock(imID)
                .subscribe({
                    isBlock.invoke(it.isBlock == 1)
                }, {

                })
        )
    }

    /**
     * 获取质检处罚历史
     */
    fun getPenaltyHistory() {
        compositeDisposable?.add(
            AppRepository.getPenaltyHistory().subscribe({ entity ->
                penaltyHistoryResult.postValue(entity)
            }, {
                penaltyHistoryError.postValue(Any())
            })
        )
    }

    /**
     * 获取质检处罚通知
     *  @param penaltyId 不为空时，则获取指定id的质检处罚通知
     */
    fun getPenaltyLatestNotify(imID: String, penaltyId: String, getPenaltyLatestNotify: (IMPenaltiesData) -> Unit = {}) {
        compositeDisposable?.add(
            AppRepository.getPenaltyLatestNotify(imID,penaltyId)
                .subscribe({
                    getPenaltyLatestNotify.invoke(it)
                }, {

                })
        )
    }

    /**
     * 新主播"恭喜"通知已读
     */
    fun newReadCongrats(callback: (Boolean) -> Unit = {}) {
        compositeDisposable?.add(
            AppRepository.newReadCongrats()
                .subscribe({
                    callback.invoke(true)
                }, {
                    ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
                })
        )
    }

    /**
     * 新账号新手任务选择model
     */
    fun selectModel(modelId: String) {
        compositeDisposable?.add(
            AppRepository.selectModel(modelId)
                .subscribe({
                    selectModelResult.postValue(1)
                }, {
                    selectModelResult.postValue(ErrorCodeCheckUtils.getErrorCode(it))
                    ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
                })
        )
    }

    /**
     * 标记主播已经点击邀请好友Tab
     */
    fun invitationRead() {
        compositeDisposable?.add(
            AppRepository.invitationRead()
                .subscribe({
                    SimpleRxBus.post(EventProfileUpdate())
                }, {})
        )
    }

    /**
     * 新主播绑定邀请码
     */
    fun newBindCode(code: String, callback: () -> Unit = {}) {
        compositeDisposable?.add(
            AppRepository.newBindCode(code)
                .subscribe({
                    ToastUtils.showToast("Success")
                    callback.invoke()
                }, {
                    ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
                })
        )
    }

    /**
     * 跳过绑定邀请码
     */
    fun skipBindCode(callback: () -> Unit) {
        compositeDisposable?.add(
            AppRepository.skipBindCode()
                .subscribe({
                    callback.invoke()
                }, {
                    ToastUtils.showToast(ErrorCodeCheckUtils.getError(it))
                })
        )
    }


    /**
     * 排班入口红点
     */
    fun readNewSchedule() {
        compositeDisposable?.add(
            AppRepository.readNewSchedule()
                .subscribe({
                }, {
                })
        )
    }

    class ViewModeFactory : ViewModelProvider.Factory {
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            return ProfileViewModel() as T
        }
    }
}