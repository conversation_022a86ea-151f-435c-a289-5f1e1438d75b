package com.fascin.chatter.main.profile.view

import android.content.Context
import android.graphics.Rect
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.iandroid.allclass.lib_basecore.utils.DeviceUtils
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RvItem
import com.iandroid.allclass.lib_common.AppViewType
import kotlinx.android.synthetic.main.itemview_block_matched_place.view.ShimmerLayout

/**
 * @Created: QuanZH
 * @Date: 2023/7/11
 */
@RvItem(id = AppViewType.userMessageSettingPlaceItemView, spanCount = 1)
class UserMessageSettingPlaceItemView(context: Context, fm: FragmentManager, parent: ViewGroup) :
    BaseRvItemView(context, fm, parent) {

    override fun setView() {
        itemView?.run {
            ShimmerLayout.startShimmerAnimation()
        }
    }

    override fun attachLayoutId(): Int {
        return R.layout.itemview_user_message_setting_place
    }

    override fun initView(context: Context?, view: View?) {
    }

    override fun getItemOffsets(
        recyclerView: RecyclerView,
        view: View,
        outRect: Rect,
        position: Int
    ): Boolean {
        val edge: Int = DeviceUtils.dp2px(context, 16.0f)
        outRect.bottom = edge / 2
        outRect.top = edge / 2

        outRect.left = edge
        outRect.right = edge
        return true
    }
}