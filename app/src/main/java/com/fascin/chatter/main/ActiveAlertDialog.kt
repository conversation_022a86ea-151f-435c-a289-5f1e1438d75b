package com.fascin.chatter.main

import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.utils.StatusHeartbeatManager
import com.iandroid.allclass.lib_basecore.utils.DeviceUtils
import com.iandroid.allclass.lib_common.base.BaseDialogFragment
import com.iandroid.allclass.lib_common.utils.exts.show
import kotlinx.android.synthetic.main.dialog_active_alert.alert_dialog_confirm
import kotlinx.android.synthetic.main.dialog_active_alert.alert_dialog_content
import kotlinx.android.synthetic.main.dialog_active_alert.alert_dialog_title

/**
 * Created by david on 2020/9/2.
 */
class ActiveAlertDialog : BaseDialogFragment() {
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(
            R.layout.dialog_active_alert, container, false
        )
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_FRAME, com.iandroid.allclass.lib_common.R.style.com_anim_dialog)
    }

    override fun onStart() {
        super.onStart()
        setCenterPopupAttr(
            (DeviceUtils.getScreenWidth(context) * 0.803).toInt(),
            WindowManager.LayoutParams.WRAP_CONTENT
        )
    }

    override fun onViewCreated(
        view: View, savedInstanceState: Bundle?
    ) {
        isActiveDialogShow = true
        alert_dialog_title.text =
            getText(com.iandroid.allclass.lib_common.R.string.general_reminder_title)
        alert_dialog_content.text =
            getText(com.iandroid.allclass.lib_common.R.string.active_tip_content)

        alert_dialog_confirm.text =
            getText(com.iandroid.allclass.lib_common.R.string.active_tip_btn)

        alert_dialog_title.show(true)

        alert_dialog_confirm.show(true)

        alert_dialog_confirm.setOnClickListener {
            AppModule.userActive()
            StatusHeartbeatManager.stopHeartbeat()
            StatusHeartbeatManager.startHeartbeat()
            dismissAllowingStateLoss()
        }

        isCancelable = false
    }

    override fun onPause() {
        super.onPause()
        //弹窗活跃提醒弹窗 退到后台 在回来就隐藏提醒
        dismissAllowingStateLoss()
    }


    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        isActiveDialogShow = false
    }

    companion object {
        var isActiveDialogShow = false
    }
}