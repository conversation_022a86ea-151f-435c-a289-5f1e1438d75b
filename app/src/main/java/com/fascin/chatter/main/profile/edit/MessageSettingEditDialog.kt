package com.fascin.chatter.main.profile.edit

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.R
import com.fascin.chatter.component.views.SUButtonStatus
import com.fascin.chatter.main.profile.ProfileViewModel
import com.iandroid.allclass.lib_common.Values.Companion.messageSettingTypeGreeting
import com.iandroid.allclass.lib_common.base.BaseDialogFragment
import com.iandroid.allclass.lib_common.beans.MsgContentEntity
import com.iandroid.allclass.lib_common.utils.keyboard.KeyboardUtils
import kotlinx.android.synthetic.main.dialog_message_setting_edit.iv_back
import kotlinx.android.synthetic.main.dialog_message_setting_edit.message_setting_edit_count
import kotlinx.android.synthetic.main.dialog_message_setting_edit.message_setting_edit_input
import kotlinx.android.synthetic.main.dialog_message_setting_edit.message_setting_edit_root
import kotlinx.android.synthetic.main.dialog_message_setting_edit.message_setting_edit_title
import kotlinx.android.synthetic.main.dialog_message_setting_edit.message_setting_save

class MessageSettingEditDialog(
    private val contentEntity: MsgContentEntity,
    private val nickName: String,
    private val type: Int,
    private val completeBlock: (contentEntity: MsgContentEntity) -> Unit
) : BaseDialogFragment() {

    private val viewModel by lazy {
        ViewModelProvider(
            this, ProfileViewModel.ViewModeFactory()
        )[ProfileViewModel::class.java]
    }

    private var isAdd: Boolean = false

    override fun onStart() {
        super.onStart()
        setBottomPopupAttr(
            WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT
        )
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_message_setting_edit, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        message_setting_edit_root.setOnClickListener {
            dismissAllowingStateLoss()
        }
        iv_back.setOnClickListener {
            dismissAllowingStateLoss()
        }
        // 修改时的状态
        if (contentEntity.content.isNotEmpty()) {
            message_setting_edit_input.setText(contentEntity.content)
            message_setting_edit_input.setSelection(contentEntity.content.length)
        } else {
            isAdd = true
        }
        // 初始化字数标识
        message_setting_edit_count.text =
            String.format(
                getString(R.string.message_setting_edit_count),
                message_setting_edit_input.text?.trim().toString().length
            )
        // title文案
        if (isAdd) {
            message_setting_edit_title.text = if (type == messageSettingTypeGreeting) String.format(
                getString(R.string.message_setting_edit_title_add_greet),
                nickName
            )
            else String.format(getString(R.string.message_setting_edit_title_add_message), nickName)
        } else {
            message_setting_edit_title.text = if (type == messageSettingTypeGreeting) String.format(
                getString(R.string.message_setting_edit_title_edit_greet),
                nickName
            )
            else String.format(
                getString(R.string.message_setting_edit_title_edit_message),
                nickName
            )
        }
        // 初始化提交按钮状态
        message_setting_save.setButtonStatus(SUButtonStatus.Disabled)
        // 提交按钮文案
        if (isAdd) {
            message_setting_save.setText(
                if (type == messageSettingTypeGreeting) getString(R.string.message_setting_btn_add_greeting)
                else getString(R.string.message_setting_btn_add_msg)
            )
        } else {
            message_setting_save.setText(getString(R.string.login_continue))
        }

        // 监听edit内容变化
        message_setting_edit_input.addTextChangedListener {
            setButtonStatus()
        }

        // 提交
        message_setting_save.setOnClickListener {
            KeyboardUtils.hideKeyboard(message_setting_edit_input)
            message_setting_edit_input.isEnabled = false
            message_setting_save.setButtonStatus(SUButtonStatus.Loading)
            if (isAdd) {
                viewModel.addMessageOrGreeting(
                    contentEntity.mid, type, message_setting_edit_input.text.toString().trim()
                )
            } else {
                viewModel.modifyMessageOrGreeting(
                    contentEntity.mid,
                    contentEntity.id,
                    message_setting_edit_input.text.toString().trim()
                )
            }
        }

        message_setting_edit_input.postDelayed({
            KeyboardUtils.showKeyboard(message_setting_edit_input)
        }, 200L)

        viewModel.apply {
            messageSettingAdd.observe(this@MessageSettingEditDialog) {
                endProgressButtonLoading()
                dismissAllowingStateLoss()
                completeBlock(it)
                dismiss()
            }
            messageSettingAddError.observe(this@MessageSettingEditDialog) {
                endProgressButtonLoading()
            }

            // 修改完成
            messageSettingModify.observe(this@MessageSettingEditDialog) {
                endProgressButtonLoading()
                dismissAllowingStateLoss()
                completeBlock(it)
                dismiss()
            }
            messageSettingModifyError.observe(this@MessageSettingEditDialog) {
                endProgressButtonLoading()
            }
        }
    }

    private fun endProgressButtonLoading() {
        message_setting_save.setButtonStatus(SUButtonStatus.Activated)
        message_setting_edit_input.isEnabled = true
    }

    private fun setButtonStatus() {
        val content = message_setting_edit_input.text?.trim().toString()
        message_setting_edit_count.text =
            String.format(getString(R.string.message_setting_edit_count), content.length)
        val isEnabled = content.isNotEmpty() && content != contentEntity.content
        message_setting_save.setButtonStatus(if (isEnabled) SUButtonStatus.Activated else SUButtonStatus.Disabled)
    }

}