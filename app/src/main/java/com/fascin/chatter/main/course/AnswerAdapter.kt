package com.fascin.chatter.main.course

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import kotlinx.android.synthetic.main.itemview_question_answer.view.itemRoot
import kotlinx.android.synthetic.main.itemview_question_answer.view.ivSelect
import kotlinx.android.synthetic.main.itemview_question_answer.view.tvAnswer

/**
 * @Desc: 课程问卷答案
 * @Created: Quan
 * @Date: 2023/9/21
 */
class AnswerAdapter : RecyclerView.Adapter<AnswerAdapter.ViewHolder>() {

    private val dataList = mutableListOf<String>()
    private var type: Boolean = false
    private var selectIds = mutableListOf<Int>()// 选中的model position
    private var selectHolder: ViewHolder? = null// 单选：选中的model
    private var callBack: OnSelectChangeCallback? = null


    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

    fun updateData(data: List<String>?) {
        dataList.clear()
        if (!data.isNullOrEmpty()) {
            dataList.addAll(data)
        }
        notifyDataSetChanged()
    }

    fun initSelected() {
        selectIds.clear()
        selectHolder = null
    }

    /**
     * 设置问题类型
     * @param type: false:单选   true:多选
     */
    fun setType(type: Boolean) {
        this.type = type
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutInflater.from(parent.context).inflate(
                R.layout.itemview_question_answer,
                parent,
                false
            )
        )
    }

    override fun getItemCount(): Int {
        return dataList.size
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = dataList[position]
        holder.itemView.run {
            tvAnswer.text = item
            ivSelect.setImageResource(getSelectIcon(selectIds.contains(position)))
            // 单选才需要记录
            if (!type && selectIds.contains(position)) selectHolder = holder
            itemRoot.clickWithTrigger {
                if (!type) {
                    // 单选
                    if (!selectIds.contains(position)) {
                        selectIds.clear()
                        selectIds.add(position)
                        ivSelect.setImageResource(getSelectIcon(true))
                        selectHolder?.itemView?.ivSelect?.setImageResource(getSelectIcon(false))
                        selectHolder = holder
                        callBack?.onSelected(selectIds)
                    }
                } else {
                    // 多选
                    if (!selectIds.contains(position)) {
                        selectIds.add(position)
                        ivSelect.setImageResource(getSelectIcon(true))
                    } else {
                        selectIds.remove(position)
                        ivSelect.setImageResource(getSelectIcon(false))
                    }
                    callBack?.onSelected(selectIds)
                }
            }
        }
    }

    private fun getSelectIcon(isSelect: Boolean): Int {
        // 单选
        return if (!type) {
            if (isSelect) R.mipmap.ic_answer_single
            else R.mipmap.ic_answer_single_un
        } else {
            // 多选
            if (isSelect) R.mipmap.ic_answer_multiple
            else R.mipmap.ic_answer_multiple_un
        }
    }

    fun setOnSelectChangeCallback(callBack: OnSelectChangeCallback) {
        this.callBack = callBack
    }

    interface OnSelectChangeCallback {
        fun onSelected(selects: List<Int>)
    }
}