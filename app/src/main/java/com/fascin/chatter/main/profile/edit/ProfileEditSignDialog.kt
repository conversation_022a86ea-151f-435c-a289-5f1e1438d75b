package com.fascin.chatter.main.profile.edit

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.R
import com.fascin.chatter.component.views.SUButtonStatus
import com.fascin.chatter.main.profile.ProfileViewModel
import com.iandroid.allclass.lib_common.base.BaseDialogFragment
import com.iandroid.allclass.lib_common.beans.ProfileParams
import com.iandroid.allclass.lib_common.beans.ProfileSettingStep
import com.iandroid.allclass.lib_common.utils.keyboard.KeyboardUtils
import kotlinx.android.synthetic.main.dialog_profile_edit_brief.*

class ProfileEditSignDialog(
    private val completeBlock: () -> Unit
) : BaseDialogFragment() {

    private val viewModel by lazy {
        ViewModelProvider(
            this,
            ProfileViewModel.ViewModeFactory()
        )[ProfileViewModel::class.java]
    }
    private val profileParams by lazy {
        ProfileParams.get().apply {
            resetByCache()
        }
    }

    override fun onStart() {
        super.onStart()
        setBottomPopupAttr(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT
        )
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_profile_edit_brief, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        profile_edit_brief_root.setOnClickListener {
            dismissAllowingStateLoss()
        }
        profile_edit_brief_save.setButtonStatus(SUButtonStatus.Activated)
        profile_edit_brief_input.setText(profileParams.sign)
        profile_edit_brief_input.setSelection(profileParams.sign.length)
        profile_edit_brief_save.setText(getString(R.string.profile_edit_save))
        profile_edit_brief_save.setOnClickListener {

            KeyboardUtils.hideKeyboard(profile_edit_brief_input)
            profile_edit_brief_input.isEnabled = false
            profile_edit_brief_save.setButtonStatus(SUButtonStatus.Loading)

            profileParams.step = ProfileSettingStep.Sign
            profileParams.sign = profile_edit_brief_input.text.toString()

        }

        profile_edit_brief_input.postDelayed({
            KeyboardUtils.showKeyboard(profile_edit_brief_input)
        }, 200L)

        viewModel.profileSettingResult.observe(this) {
            endProgressButtonLoading()

            dismissAllowingStateLoss()
            completeBlock()
        }
        viewModel.profileSettingError.observe(this) {
            endProgressButtonLoading()
        }
    }

    private fun endProgressButtonLoading() {
        profile_edit_brief_save.setButtonStatus(SUButtonStatus.Activated)
        profile_edit_brief_input.isEnabled = true
    }
}