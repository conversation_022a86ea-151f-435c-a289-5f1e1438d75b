package com.fascin.chatter.main.profile.view

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.fascin.chatter.R
import com.fascin.chatter.main.profile.IMessageSettingRvItemAction
import com.fascin.chatter.main.profile.MessageSettingContentCallBack
import com.fascin.chatter.main.profile.adapter.MsgSettingContentAdapter
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RvItem
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.GlideLoader.loadImageCircleCrop
import com.iandroid.allclass.lib_common.Values
import com.iandroid.allclass.lib_common.beans.MessageSettingEntity
import com.iandroid.allclass.lib_common.beans.UserEntity
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.routeActionByParam
import com.iandroid.allclass.lib_common.utils.exts.castObject
import kotlinx.android.synthetic.main.itemview_message_setting.view.btnAdd
import kotlinx.android.synthetic.main.itemview_message_setting.view.hasMoreView
import kotlinx.android.synthetic.main.itemview_message_setting.view.nickName
import kotlinx.android.synthetic.main.itemview_message_setting.view.nsRv
import kotlinx.android.synthetic.main.itemview_message_setting.view.userHead

/**
 * MessageSetting Item
 * @Created: QuanZH
 * @Date: 2023/7/11
 */
@RvItem(id = AppViewType.messageSettingItemView, spanCount = 1)
class MessageSettingItemView(context: Context, fm: FragmentManager, parent: ViewGroup) :
    BaseRvItemView(context, fm, parent) {

    var contentAdapter: MsgSettingContentAdapter? = null

    override fun attachLayoutId(): Int {
        return R.layout.itemview_message_setting
    }

    override fun initView(context: Context?, view: View?) {
        itemView.nsRv.layoutManager = LinearLayoutManager(context)
        contentAdapter = MsgSettingContentAdapter()
        itemView.nsRv.adapter = contentAdapter

    }

    override fun setView() {
        val entity = getItemData() ?: return
        itemView?.run {
            with(entity) {
                userHead.loadImageCircleCrop(context, avatarUrl)
                nickName.text = nickname
                // 设置添加状态按钮
                btnAdd.setTextColor(
                    if (isMax())
                        AppContext.getColor(R.color.cr_bfbfbf)
                    else
                        AppContext.getColor(R.color.black)
                )
                btnAdd.setBackgroundResource(
                    if (isMax())
                        R.drawable.bg_btn_border_bfbfbf_r15
                    else
                        R.drawable.bg_btn_border_black_r15
                )
                btnAdd.isEnabled = !isMax()
                btnAdd.text =
                    if (type == Values.messageSettingTypeGreeting)
                        AppContext.getString(R.string.message_setting_btn_add_greeting)
                    else
                        AppContext.getString(R.string.message_setting_btn_add_msg)
                nsRv.visibility = if (contentList.isEmpty()) View.GONE else View.VISIBLE
                contentAdapter?.updateData(contentList)

                // has more view
                if (hasMore()) {
                    hasMoreView.text = String.format(
                        context.getString(R.string.message_setting_content_more),
                        nickname
                    )
                    hasMoreView.visibility = View.VISIBLE
                } else {
                    hasMoreView.visibility = View.GONE
                }

                // 跳转个人主页
                userHead.setOnClickListener {
                    context.routeActionByParam<UserEntity>(ActionType.actionOtherProfile) {
                        it.userId = entity.userId
                    }
                }

                // hasMore跳转user messageSetting
                hasMoreView.setOnClickListener {
                    context.routeActionByParam<UserEntity>(ActionType.actionMessageSetting) {
                        it.userId = userId
                        it.nickname = nickname
                    }
                }

                // 添加问候语/消息
                btnAdd.setOnClickListener {
                    if (!isMax())
                        getAction()?.addMessage(userId, nickname)
                }
                contentAdapter?.setCallBack(object : MessageSettingContentCallBack {
                    override fun delMessage(msgID: Int) {
                        // 删除消息
                        getAction()?.delMessage(msgID, userId)
                    }

                    override fun modifyMessage(msgID: Int, msgContent: String) {
                        // 修改消息
                        getAction()?.modifyMessage(msgID, msgContent, userId, nickname)
                    }
                })
            }
        }
    }

    private fun getItemData(): MessageSettingEntity? = data?.castObject<MessageSettingEntity>()

    private fun getAction(): IMessageSettingRvItemAction? {
        return info?.callBack?.castObject<IMessageSettingRvItemAction>()
    }

}