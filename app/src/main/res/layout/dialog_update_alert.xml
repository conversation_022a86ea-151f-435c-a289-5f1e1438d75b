<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/alert_dialog_rootview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_dialog_gradient"
        android:padding="24dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/alert_dialog_title"
            style="@style/bold_20_75"
            android:paddingBottom="12dp"
            android:text="Version Update"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/dividerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:paddingBottom="12dp"
            android:src="@drawable/shape_com_divider"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/alert_dialog_title" />

        <TextView
            android:id="@+id/alert_dialog_subtitle"
            style="@style/bold_14_75"
            android:layout_width="0dp"
            android:layout_marginBottom="12dp"
            android:gravity="left"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/dividerView" />

        <TextView
            android:id="@+id/alert_dialog_content"
            style="@style/regular_14_75"
            android:layout_width="0dp"
            android:gravity="left"
            android:paddingBottom="18dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/alert_dialog_subtitle" />

        <TextView
            android:id="@+id/alert_dialog_cancel"
            style="@style/regular_14_75"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_btn_border_75"
            android:gravity="center"
            android:padding="10dp"
            android:text="@string/btn_cancel"
            app:layout_constraintEnd_toStartOf="@+id/alert_dialog_confirm"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/alert_dialog_content"
            app:layout_constraintWidth_percent="0.45" />

        <TextView
            android:id="@+id/alert_dialog_confirm"
            style="@style/regular_14_75"
            android:layout_width="0dp"
            android:background="@drawable/bg_btn_solid_75_black"
            android:gravity="center"
            android:padding="10dp"
            android:text="Update"
            android:textColor="@color/white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/alert_dialog_cancel"
            app:layout_constraintTop_toBottomOf="@+id/alert_dialog_content"
            app:layout_constraintWidth_percent="0.45" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>