<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.motion.widget.MotionLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/transparent"
    app:layoutDescription="@xml/view_chat_gift_scene">

    <ImageView
        android:id="@+id/bg"
        android:src="@mipmap/ic_gift_bg"
        android:layout_width="160dp"
        android:layout_height="150dp"
        android:scaleType="fitXY"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <View
        android:id="@+id/bgCover"
        android:background="@mipmap/ic_gift_white_cover"
        android:layout_width="148dp"
        android:layout_height="105dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <ImageView
        android:id="@+id/ivGift"
        android:layout_width="61dp"
        android:layout_height="61dp"
        android:layout_marginBottom="10dp"
        android:scaleType="fitXY"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <ImageView
        android:id="@+id/ivGiftBox"
        android:src="@mipmap/ic_gift_box"
        android:layout_width="90.75dp"
        android:layout_height="75dp"
        android:scaleType="fitXY"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <ImageView
        android:id="@+id/ivGiftBoxCover"
        android:src="@mipmap/ic_gift_box_cover"
        android:layout_width="90.75dp"
        android:layout_height="75dp"
        android:scaleType="fitXY"
        app:layout_constraintStart_toStartOf="@id/ivGiftBox"
        app:layout_constraintTop_toTopOf="@id/ivGiftBox"/>

    <TextView
        android:id="@+id/tvGiftName"
        android:textSize="10sp"
        android:textColor="#8F5A0A"
        android:fontFamily="@font/sail"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        tools:text="Gift Name"
        app:layout_constraintStart_toStartOf="@id/ivGiftBox"
        app:layout_constraintTop_toTopOf="@id/ivGiftBox"
        app:layout_constraintEnd_toEndOf="@id/ivGiftBox"
        app:layout_constraintBottom_toBottomOf="@id/ivGiftBox"/>

    <TextView
        android:id="@+id/tvClickView"
        android:textSize="10sp"
        android:textColor="#FF28069F"
        android:text="@string/click_to_view_gift"
        android:background="@mipmap/ic_gift_click_view_bg"
        android:gravity="center"
        android:paddingHorizontal="6dp"
        android:paddingVertical="2dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="5dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.motion.widget.MotionLayout>