<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/multipleRoot"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.exoplayer2.ui.StyledPlayerView
        android:id="@+id/multiple_video_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:use_controller="false" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/multiple_video_thumb"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/multiplePlay"
        android:layout_width="90dp"
        android:layout_height="90dp"
        android:padding="20dp"
        android:src="@mipmap/ic_video_play"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <include
        android:id="@+id/videoControl"
        layout="@layout/layout_video_control"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:layout_marginBottom="63dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/viewControlHeight"
        tools:visibility="visible" />

    <View
        android:id="@+id/viewControlHeight"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>