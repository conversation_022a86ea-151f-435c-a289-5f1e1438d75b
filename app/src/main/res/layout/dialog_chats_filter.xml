<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_white_bottom_round"
    android:paddingHorizontal="16dp"
    android:paddingBottom="32dp">

    <com.iandroid.allclass.lib_basecore.view.FontTextView
        android:id="@+id/ChatsFilterTitle"
        style="@style/bold_20_75"
        android:layout_width="match_parent"
        android:layout_height="55dp"
        android:layout_marginTop="25dp"
        android:gravity="center"
        android:text="Filter"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!--会员筛选-->
    <com.iandroid.allclass.lib_basecore.view.FontTextView
        android:id="@+id/SubFilterMember"
        style="@style/medium_18_75"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Member"
        android:textColor="@color/black"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ChatsFilterTitle" />

    <RadioGroup
        android:id="@+id/SubFilterRGMember"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/SubFilterMember">

        <RadioButton
            android:id="@+id/memberAll"
            style="@style/regular_14_75"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/selector_chats_filter_item"
            android:button="@null"
            android:gravity="center"
            android:paddingVertical="6dp"
            android:text="All"
            android:textColor="@color/selector_chats_filter_text_color" />

        <RadioButton
            android:id="@+id/memberMember"
            style="@style/regular_14_75"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="30dp"
            android:layout_weight="1"
            android:background="@drawable/selector_chats_filter_item"
            android:button="@null"
            android:gravity="center"
            android:paddingVertical="6dp"
            android:text="Member"
            android:textColor="@color/selector_chats_filter_text_color" />

        <RadioButton
            android:id="@+id/memberNoMember"
            style="@style/regular_14_75"
            android:layout_width="wrap_content"
            android:background="@drawable/selector_chats_filter_item"
            android:button="@null"
            android:gravity="center"
            android:paddingHorizontal="4dp"
            android:paddingVertical="6dp"
            android:text="Non-members"
            android:textColor="@color/selector_chats_filter_text_color" />
    </RadioGroup>

    <!--在线状态筛选-->
    <com.iandroid.allclass.lib_basecore.view.FontTextView
        android:id="@+id/SubFilterLine"
        style="@style/medium_18_75"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:text="Active"
        android:textColor="@color/black"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/SubFilterRGMember" />

    <RadioGroup
        android:id="@+id/SubFilterRGLine"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/SubFilterLine">

        <RadioButton
            android:id="@+id/lineAll"
            style="@style/regular_14_75"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/selector_chats_filter_item"
            android:button="@null"
            android:gravity="center"
            android:paddingVertical="6dp"
            android:text="All"
            android:textColor="@color/selector_chats_filter_text_color" />

        <RadioButton
            android:id="@+id/lineOnline"
            style="@style/regular_14_75"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="30dp"
            android:layout_weight="1"
            android:background="@drawable/selector_chats_filter_item"
            android:button="@null"
            android:gravity="center"
            android:paddingVertical="6dp"
            android:text="Online"
            android:textColor="@color/selector_chats_filter_text_color" />

        <RadioButton
            android:id="@+id/lineOffline"
            style="@style/regular_14_75"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/selector_chats_filter_item"
            android:button="@null"
            android:gravity="center"
            android:paddingVertical="6dp"
            android:text="Offline"
            android:textColor="@color/selector_chats_filter_text_color" />
    </RadioGroup>

    <!--等级筛选-->
    <com.iandroid.allclass.lib_basecore.view.FontTextView
        android:id="@+id/SubFilterLvl"
        style="@style/medium_18_75"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:text="Grade"
        android:textColor="@color/black"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/SubFilterRGLine" />

    <RadioGroup
        android:id="@+id/SubFilterRGLvl"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/SubFilterLvl">

        <RadioButton
            android:id="@+id/LvlAll"
            style="@style/regular_14_75"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/selector_chats_filter_item"
            android:button="@null"
            android:gravity="center"
            android:paddingVertical="6dp"
            android:text="All"
            android:textColor="@color/selector_chats_filter_text_color" />

        <RadioButton
            android:id="@+id/LvlLvl1"
            style="@style/regular_14_75"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_weight="1"
            android:background="@drawable/selector_chats_filter_item"
            android:button="@null"
            android:gravity="center"
            android:paddingVertical="6dp"
            android:text="LV.1"
            android:textColor="@color/selector_chats_filter_text_color" />

        <RadioButton
            android:id="@+id/LvlLvl2"
            style="@style/regular_14_75"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_weight="1"
            android:background="@drawable/selector_chats_filter_item"
            android:button="@null"
            android:gravity="center"
            android:paddingVertical="6dp"
            android:text="LV.2"
            android:textColor="@color/selector_chats_filter_text_color" />

        <RadioButton
            android:id="@+id/LvlLvl3"
            style="@style/regular_14_75"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_weight="1"
            android:background="@drawable/selector_chats_filter_item"
            android:button="@null"
            android:gravity="center"
            android:paddingVertical="6dp"
            android:text="LV.3"
            android:textColor="@color/selector_chats_filter_text_color" />
    </RadioGroup>

    <!--New标筛选-->
    <com.iandroid.allclass.lib_basecore.view.FontTextView
        android:id="@+id/SubFilterNew"
        style="@style/medium_18_75"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="New"
        android:textColor="@color/black"
        android:layout_marginTop="24dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/SubFilterRGLvl" />

    <RadioGroup
        android:id="@+id/SubFilterRGNew"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/SubFilterNew">

        <RadioButton
            android:id="@+id/newAll"
            style="@style/regular_14_75"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/selector_chats_filter_item"
            android:button="@null"
            android:gravity="center"
            android:paddingVertical="6dp"
            android:paddingHorizontal="33dp"
            android:text="All"
            android:textColor="@color/selector_chats_filter_text_color" />

        <RadioButton
            android:id="@+id/newNew"
            style="@style/regular_14_75"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="30dp"
            android:layout_weight="1"
            android:background="@drawable/selector_chats_filter_item"
            android:button="@null"
            android:gravity="center"
            android:paddingVertical="6dp"
            android:text="New"
            android:textColor="@color/selector_chats_filter_text_color" />

        <RadioButton
            android:id="@+id/newNonNew"
            style="@style/regular_14_75"
            android:layout_width="wrap_content"
            android:background="@drawable/selector_chats_filter_item"
            android:button="@null"
            android:gravity="center"
            android:paddingHorizontal="22dp"
            android:paddingVertical="6dp"
            android:text="Non-New"
            android:textColor="@color/selector_chats_filter_text_color" />
    </RadioGroup>

    <!--重置/确定筛选-->
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/FilteReset"
        style="@style/bold_16_75"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:layout_marginEnd="4dp"
        android:background="@drawable/selector_filter_item"
        android:button="@null"
        android:gravity="center"
        android:paddingVertical="10dp"
        android:text="Reset"
        app:layout_constraintEnd_toStartOf="@id/FilteviewCountChats"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/SubFilterRGNew" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/FilteviewCountChats"
        style="@style/bold_16_75"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:background="@drawable/bg_filter_checked"
        android:button="@null"
        android:gravity="center"
        android:paddingVertical="10dp"
        android:text="View 20 Chats"
        android:textColor="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/FilteReset"
        app:layout_constraintTop_toTopOf="@id/FilteReset" />

</androidx.constraintlayout.widget.ConstraintLayout>