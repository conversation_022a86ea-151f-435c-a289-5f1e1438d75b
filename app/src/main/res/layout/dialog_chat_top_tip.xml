<?xml version="1.0" encoding="utf-8"?>
<net.csdn.roundview.RoundRelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    app:rRadius="12dp">

    <View
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:background="@drawable/bg_dialog_gradient_top_r12" />

    <TextView
        android:id="@+id/tvTitle"
        style="@style/bold_20_75"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginHorizontal="24dp"
        android:layout_marginBottom="16dp"
        android:layout_marginTop="24dp"
        android:gravity="center"
        android:text="@string/conversation_top_tip_title"
        android:textColor="@color/black" />

    <TextView
        android:id="@+id/tvDesc"
        style="@style/regular_13_75"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tvTitle"
        android:layout_centerHorizontal="true"
        android:layout_marginHorizontal="24dp"
        android:layout_marginBottom="16dp"
        android:gravity="center"
        android:text="@string/conversation_top_tip_desc"
        android:textColor="@color/cl_262626" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/iv_image"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tvDesc"
        android:layout_marginHorizontal="24dp"
        android:layout_marginBottom="24dp"
        android:adjustViewBounds="true"
        android:src="@drawable/bg_chat_top_tip" />



    <TextView
        android:id="@+id/tvGot"
        style="@style/bold_16_75"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_centerHorizontal="true"
        android:layout_marginHorizontal="24dp"
        android:layout_marginBottom="24dp"
        android:layout_below="@+id/iv_image"
        android:background="@drawable/bg_progress_button_active"
        android:gravity="center"
        android:text="@string/btn_got_it"
        android:textColor="@color/white" />

</net.csdn.roundview.RoundRelativeLayout>