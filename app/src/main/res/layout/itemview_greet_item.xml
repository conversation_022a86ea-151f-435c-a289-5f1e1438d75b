<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/wrap_wrap"
    android:paddingEnd="8dp">

    <net.csdn.roundview.RoundLinearLayout
        android:id="@+id/llContent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@color/color_f5f5f5"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingVertical="5dp"
        android:paddingStart="14dp"
        android:paddingEnd="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:rRadius="99dp">

        <TextView
            android:id="@+id/id_name"
            style="@style/medium_13_75"
            android:layout_marginEnd="8dp"
            android:button="@null"
            android:singleLine="true"
            android:textColor="@color/black"
            tools:text="Hi how are u?" />

        <androidx.constraintlayout.utils.widget.ImageFilterView
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:src="@mipmap/ic_match_add" />
    </net.csdn.roundview.RoundLinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>