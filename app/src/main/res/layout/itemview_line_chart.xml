<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/bg_stroke_gray_r12"
    android:layout_marginTop="16dp">

    <com.iandroid.allclass.lib_basecore.view.FontTextView
        android:id="@+id/tvLineChartTitle"
        style="@style/medium_16_75"
        android:paddingHorizontal="16dp"
        android:paddingTop="16dp"
        android:textColor="@color/black"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="Online Time" />

    <com.github.mikephil.charting.charts.LineChart
        android:id="@+id/taskLineChart"
        android:layout_width="match_parent"
        android:layout_height="220dp"
        android:layout_marginHorizontal="8dp"
        app:layout_constraintTop_toBottomOf="@id/tvLineChartTitle"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="8dp" />
</androidx.constraintlayout.widget.ConstraintLayout>