<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_round_white_r12"
    android:paddingBottom="24dp">

    <net.csdn.roundview.RoundView
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:background="@mipmap/bg_traffic_top_gradient"
        app:layout_constraintTop_toTopOf="parent"
        app:rTopLeftRadius="12dp"
        app:rTopRightRadius="12dp" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivChatPenalty"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:layout_marginTop="24dp"
        android:src="@mipmap/ic_cs_quality"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvChatPenaltiesNoticeTitle"
        style="@style/bold_20_75"
        android:layout_width="match_parent"
        android:layout_marginTop="24dp"
        android:paddingHorizontal="24dp"
        android:gravity="center"
        android:text="@string/penalties_notice"
        android:textColor="@color/cl_262626"
        android:layout_marginHorizontal="24dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivChatPenalty" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_chat_penalties_reason_tips"
        style="@style/semiBold_16_75"
        android:layout_marginTop="16dp"
        android:text="@string/penalties_reason"
        android:textColor="@color/cl_262626"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvChatPenaltiesNoticeTitle" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvChatPenaltiesReason"
        style="@style/medium_16_75"
        android:layout_width="0dp"
        android:layout_marginTop="10dp"
        android:textColor="@color/cl_595959"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginHorizontal="24dp"
        app:layout_constraintTop_toBottomOf="@id/tv_chat_penalties_reason_tips"
        tools:text="Because you are in the model name and user name chat" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_chat_penalty_tips"
        style="@style/semiBold_16_75"
        android:layout_marginTop="16dp"
        android:text="@string/penalties_penalty"
        android:textColor="@color/cl_262626"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvChatPenaltiesReason" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvChatMsgPenalty"
        style="@style/medium_16_75"
        android:layout_width="0dp"
        android:layout_marginTop="8dp"
        android:layout_marginHorizontal="24dp"
        android:gravity="center_horizontal"
        android:textColor="@color/cl_595959"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_chat_penalty_tips"
        tools:text="$X Fine" />

    <net.csdn.roundview.RoundTextView
        android:id="@+id/btnPenaltyGoIt"
        style="@style/bold_16_75"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginTop="24dp"
        android:layout_marginHorizontal="24dp"
        android:background="@color/cl_262626"
        android:gravity="center"
        android:textColor="@color/white"
        app:layout_constraintTop_toBottomOf="@id/tvChatMsgPenalty"
        app:rRadius="16dp"
        android:text="@string/penalties_i_get_it" />

    <net.csdn.roundview.RoundTextView
        android:id="@+id/btnPenaltyContactUs"
        style="@style/bold_16_75"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginTop="12dp"
        android:layout_marginHorizontal="24dp"
        android:background="@color/white"
        android:gravity="center"
        android:textColor="@color/cl_262626"
        app:rStrokeWidth="1.5dp"
        app:rStrokeColor="@color/cl_262626"
        app:layout_constraintTop_toBottomOf="@id/btnPenaltyGoIt"
        app:rRadius="16dp"
        android:text="@string/penalties_contact_us" />


</androidx.constraintlayout.widget.ConstraintLayout>