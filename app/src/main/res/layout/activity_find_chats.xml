<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/rl_statusBar"
        android:layout_width="match_parent"
        android:layout_height="20dp"
        android:visibility="gone" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvModel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/color_f0f0f0"
        android:orientation="horizontal"
        android:overScrollMode="never"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintTop_toTopOf="parent"
        tools:itemCount="3"
        tools:listitem="@layout/itemview_chatter_model" />

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginRight="16dp"
        android:background="@drawable/bg_f0f0f0_r999"
        android:orientation="vertical"
        android:paddingStart="4dp"
        android:paddingEnd="4dp">

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabLayout"
            android:layout_width="match_parent"
            android:layout_height="42dp"
            android:background="@drawable/bg_f0f0f0_r999"
            app:tabIndicator="@drawable/shape_full_tab_indicator_white"
            app:tabIndicatorColor="@color/white"
            app:tabIndicatorFullWidth="true"
            app:tabIndicatorGravity="center"
            app:tabIndicatorHeight="38dp"
            app:tabMode="fixed"
            app:tabRippleColor="@color/transparent"
            app:tabSelectedTextColor="@color/cl_262626"
            app:tabTextColor="@color/cl_262626" />
    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/findChatVP"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="8dp"
        android:background="@color/transparent" />

</LinearLayout>