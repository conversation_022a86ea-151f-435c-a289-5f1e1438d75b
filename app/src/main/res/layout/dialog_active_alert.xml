<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/alert_dialog_rootview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:background="@drawable/bg_dialog_gradient"
        android:padding="24dp">

        <TextView
            android:id="@+id/alert_dialog_title"
            style="@style/bold_20_75"
            android:paddingBottom="15dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/alert_dialog_content"
            style="@style/regular_14_75"
            android:layout_width="0dp"
            android:gravity="left"
            android:paddingBottom="24dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/alert_dialog_title" />


        <TextView
            android:id="@+id/alert_dialog_confirm"
            style="@style/bold_16_75"
            android:layout_width="0dp"
            android:background="@drawable/bg_btn_solid_75_black"
            android:gravity="center"
            android:padding="10dp"
            android:text="@string/btn_sure"
            android:textColor="@color/white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/alert_dialog_content" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>