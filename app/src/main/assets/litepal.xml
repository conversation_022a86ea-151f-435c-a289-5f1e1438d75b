<?xml version="1.0" encoding="utf-8"?>
<litepal>
    <!--
    	Define the database name of your application.
    	By default each database name should be end with .db.
    	If you didn't name your database end with .db,
    	LitePal would plus the suffix automatically for you.
    -->
    <dbname value="DB_ChatterTool" />

    <!--数据库版本号，表的增减、表对应的实体内任何字段的增减，都需要升级数据库-->
    <version value="3" />

    <!--表对应的实体-->
    <list>
        <mapping class="com.fascin.chatter.bean.chat.SaveChatEntity" />
        <mapping class="com.fascin.chatter.bean.chat.PrivacyUnlockDBEntity" />
    </list>

    <!--
        Define where the .db file should be. "internal" means the .db file
        will be stored in the database folder of internal storage which no
        one can access. "external" means the .db file will be stored in the
        path to the directory on the primary external storage device where
        the application can place persistent files it owns which everyone
        can access. "internal" will act as default.
        For example:
        <storage value="external" />
    -->

</litepal>