<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/white"
    android:orientation="horizontal"
    android:paddingTop="@dimen/dp_10"
    android:paddingBottom="@dimen/dp_10">

    <ImageView
        android:id="@+id/cover"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_centerVertical="true"
        android:layout_gravity="center_vertical"
        android:layout_marginLeft="10dp"
        android:scaleType="centerCrop" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_gravity="center_vertical"
        android:layout_marginLeft="16dp"
        android:layout_toLeftOf="@+id/indicator"
        android:layout_toRightOf="@+id/cover"
        android:orientation="vertical">

        <TextView
            android:id="@+id/name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="#333333"
            android:textSize="16dp" />

        <TextView
            android:id="@+id/size"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="#333333"
            android:textSize="12sp" />

    </LinearLayout>

    <ImageView
        android:id="@+id/indicator"
        android:layout_width="18dp"
        android:layout_height="18dp"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_gravity="center_vertical"
        android:layout_marginLeft="@dimen/dp_10"
        android:layout_marginRight="20dp"
        android:src="@drawable/ic_default_check" />

</RelativeLayout>