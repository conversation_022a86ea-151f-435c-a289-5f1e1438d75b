<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_wrap"
    android:background="@drawable/bg_top_round_corner_12dp_white"
    android:paddingLeft="15dp"
    android:paddingRight="15dp">

    <TextView
        android:id="@+id/dialog_title"
        style="@style/wrap_wrap"
        android:layout_marginTop="20dp"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="2"
        android:text="@string/alert_notice"
        android:textColor="@color/cr_000000"
        android:textSize="18sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/sex_nic_gp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="iv_sex,nickName" />

    <ImageView
        android:id="@+id/user_photo"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_marginTop="26dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/dialog_title"
        app:layout_goneMarginTop="20dp" />

    <ImageView
        android:id="@+id/iv_sex"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="2dp"
        android:padding="@dimen/dp_2"
        android:scaleType="centerCrop"
        app:layout_constraintEnd_toStartOf="@id/nickName"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/user_photo" />

    <TextView
        android:id="@+id/nickName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/cl_666666"
        android:textSize="11sp"
        app:layout_constraintBottom_toBottomOf="@id/iv_sex"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_sex"
        app:layout_constraintTop_toTopOf="@id/iv_sex" />


    <TextView
        android:id="@+id/dialog_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginStart="40dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="40dp"
        android:gravity="center"
        android:textColor="@color/cl_666666"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_sex" />

    <TextView
        android:id="@+id/negativeButton"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_gravity="center_vertical"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="30dp"
        android:layout_marginRight="10dp"
        android:layout_marginBottom="40dp"
        android:background="@drawable/bg_comm_negative_btn"
        android:clickable="true"
        android:gravity="center"
        android:textColor="#fe7c39"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/positiveButton"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/dialog_message" />

    <TextView
        android:id="@+id/positiveButton"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_gravity="center_vertical"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="30dp"
        android:layout_marginRight="10dp"
        android:layout_marginBottom="40dp"
        android:background="@drawable/bg_comm_positive_btn"
        android:clickable="true"
        android:gravity="center"
        android:textColor="@color/cl_ffffff"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/negativeButton"
        app:layout_constraintTop_toBottomOf="@+id/dialog_message" />


</androidx.constraintlayout.widget.ConstraintLayout>