<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/itemView"
    android:layout_width="match_parent"
    android:layout_height="55dp"
    android:paddingLeft="16dp"
    android:paddingTop="1dp"
    android:paddingRight="16dp">

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#000000"
        android:textSize="16sp"
        app:layout_constraintBottom_toTopOf="@id/tvDes"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="我的等級" />

    <TextView
        android:id="@+id/tvDes"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="20dp"
        android:gravity="center_vertical|left"
        android:textColor="@color/cl_999999"
        android:textSize="12sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/btnSwitchOnOff"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvTitle"
        tools:text="我的等級" />

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/btnSwitchOnOff"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:theme="@style/lang_switch"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:showText="false"
        app:switchMinWidth="55dp" />

    <View
        android:id="@+id/divide_line"
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:background="#0e000000"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>