<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#00000000"
    android:padding="12dp">

    <LinearLayout
        android:id="@+id/action_sheet_rootview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/action_sheet_option_bg"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/action_sheet_maintitle"
            style="@style/bold_12_45"
            android:layout_marginTop="12dp"
            android:gravity="center"
            android:text="@string/general_reminder_title" />

        <TextView
            android:id="@+id/action_sheet_subtitle"
            android:paddingLeft="24dp"
            android:paddingRight="24dp"
            android:gravity="center"
            style="@style/regular_12_45"
            android:visibility="gone" />

        <View
            android:id="@+id/action_sheet_split"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="12dp"
            android:background="#f0f0f0" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/action_sheet_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:overScrollMode="never" />
    </LinearLayout>

    <TextView
        android:id="@+id/action_sheet_cancel"
        style="@style/bold_18_75"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="10dp"
        android:background="@drawable/action_sheet_cancel_bg"
        android:gravity="center"
        android:text="@string/btn_cancel"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/action_sheet_rootview" />

</androidx.constraintlayout.widget.ConstraintLayout>