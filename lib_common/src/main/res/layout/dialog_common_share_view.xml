<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.Group
        android:id="@+id/shareImgGp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="shareCloseBtn,ivImg" />

    <ImageView
        android:id="@+id/ivImg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:scaleType="centerInside"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/shareCloseBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="10dp"
        android:scaleType="centerCrop"
        android:src="@drawable/ic_close_screenshot_nor"
        app:layout_constraintEnd_toEndOf="@id/ivImg"
        app:layout_constraintTop_toTopOf="@id/ivImg" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="40dp"
        android:background="@drawable/bg_round_common_alert_dialog"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingTop="34dp"
        android:paddingBottom="55dp"
        android:paddingLeft="16dp"
        android:paddingRight="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivImg">

        <TextView
            android:id="@+id/desc_dialog_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/share_title"
            android:textColor="#333333"
            android:textSize="18sp"
            android:textStyle="bold" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/shareToIns"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="15dp"
                android:drawableTop="@drawable/ic_share_ins"
                android:drawablePadding="14dp"
                android:gravity="center_horizontal"
                android:text="@string/share_ins"
                android:textColor="#666666"
                android:textSize="12sp"
                android:textStyle="normal" />

            <TextView
                android:id="@+id/shareToTT"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="15dp"
                android:drawableTop="@drawable/ic_share_tt"
                android:drawablePadding="14dp"
                android:gravity="center_horizontal"
                android:text="@string/share_tt"
                android:textColor="#666666"
                android:textSize="12sp"
                android:textStyle="normal" />

            <TextView
                android:id="@+id/shareToFB"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="15dp"
                android:drawableTop="@drawable/ic_share_fb"
                android:drawablePadding="14dp"
                android:gravity="center_horizontal"
                android:text="@string/share_fb"
                android:textColor="#666666"
                android:textSize="12sp"
                android:textStyle="normal" />


            <TextView
                android:id="@+id/shareCopyLink"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="15dp"
                android:drawableTop="@drawable/ic_copy_link"
                android:drawablePadding="14dp"
                android:gravity="center_horizontal"
                android:text="@string/copy_link"
                android:textColor="#666666"
                android:textSize="12sp"
                android:textStyle="normal" />

        </LinearLayout>
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>