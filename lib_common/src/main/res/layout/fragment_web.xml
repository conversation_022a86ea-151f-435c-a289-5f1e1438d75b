<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/id_webrootView"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:id="@+id/fragmentWebViewContainer"
        style="@style/match_match"
        android:background="@drawable/bg_dialog_web"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.iandroid.allclass.lib_common.web.view.LangWebView
            android:id="@+id/id_content_webview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/bg_dialog_web"
            android:visibility="gone" />

    </RelativeLayout>

    <com.iandroid.allclass.lib_basecore.view.CommonPageStatusView
        android:id="@+id/id_com_status"
        style="@style/wrap_wrap"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>