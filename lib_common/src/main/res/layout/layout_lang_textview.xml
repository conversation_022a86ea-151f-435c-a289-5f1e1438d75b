<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/textLayout"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@drawable/selector_com_input"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingLeft="10dp"
        android:paddingTop="6dp"
        android:paddingBottom="6dp"
        app:hintTextColor="@color/cr_000000_25"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <EditText
            android:id="@+id/editView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/transparent"
            android:gravity="center_vertical"
            android:imeOptions="normal"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:singleLine="true"
            android:textColor="@color/cl_000000_75"
            android:textColorHint="@color/cr_000000_25"
            android:textCursorDrawable="@drawable/cursor_reg_input"
            android:textSize="14sp" />

    </com.google.android.material.textfield.TextInputLayout>

    <com.iandroid.allclass.lib_basecore.view.FontTextView
        android:id="@+id/tvWarnInfo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="8dp"
        android:layout_marginTop="6dp"
        android:lineSpacingExtra="5sp"
        android:textColor="#EA415A"
        android:textSize="12sp"
        android:translationY="-1sp"
        android:visibility="invisible"
        app:fontType="regular"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textLayout" />

</merge>