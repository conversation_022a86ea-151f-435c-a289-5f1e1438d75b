<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        android:id="@+id/iv_loading"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:visibility="gone"
        android:src="@drawable/ic_btn_loading"
        app:layout_constraintTop_toTopOf="@+id/btn_text"
        android:layout_marginEnd="2dp"
        app:layout_constraintBottom_toBottomOf="@id/btn_text"
        app:layout_constraintEnd_toStartOf="@id/btn_text" />

    <TextView
        android:id="@+id/btn_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@color/transparent"
        android:textSize="16sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>