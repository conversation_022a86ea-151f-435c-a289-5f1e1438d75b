<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.iandroid.allclass.lib_common.views.ImageShowViewPager
        android:id="@+id/image_pager"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:background="@color/transparent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/page_number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_margin="10dp"
        android:text="1/5"
        android:textColor="@color/app_list_name"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <ImageView
        android:id="@+id/page_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="30dp"
        android:background="@drawable/ic_activity_back_arrow_white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvShowNft"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="20dp"
        android:layout_marginBottom="50dp"
        android:background="@drawable/bg_show_nft_border"
        android:paddingHorizontal="20dp"
        android:paddingVertical="8dp"
        android:text="@string/view_nft_details"
        android:textColor="@color/white"
        android:textFontWeight="500"
        android:textSize="12sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/tvNftName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginBottom="100dp"
        android:text=""
        android:textColor="@color/white"
        android:textFontWeight="700"
        android:textSize="14sp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/tvShowNft"
        app:layout_constraintStart_toStartOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>