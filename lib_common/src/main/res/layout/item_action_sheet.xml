<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/action_sheet_icon"
        android:layout_width="32dp"
        android:layout_height="16dp"
        android:paddingHorizontal="8dp"
        app:layout_constraintBottom_toBottomOf="@id/action_sheet_item"
        app:layout_constraintEnd_toStartOf="@id/action_sheet_item"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/action_sheet_item"
        tools:src="@drawable/ic_close_black" />

    <TextView
        android:id="@+id/action_sheet_item"
        style="@style/medium_17_75"
        android:layout_width="wrap_content"
        android:layout_height="50dp"
        android:gravity="center"
        android:textColor="@color/cl_262626"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/action_sheet_icon"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Action Sheet" />

    <View
        android:id="@+id/action_sheet_split"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#f0f0f0"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/action_sheet_item" />

</androidx.constraintlayout.widget.ConstraintLayout>