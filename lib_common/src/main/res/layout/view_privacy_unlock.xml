<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/llLock"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_9370db_t10_r"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingHorizontal="6dp"
        android:paddingVertical="3dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.utils.widget.ImageFilterView
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:src="@drawable/ic_privacy_lock" />

        <TextView
            android:id="@+id/tvLockNum"
            style="@style/regular_12_45"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            tools:text="6" />

        <View
            android:layout_width="1px"
            android:layout_height="11dp"
            android:layout_marginHorizontal="4dp"
            android:background="@color/cl_black_10" />

        <androidx.constraintlayout.utils.widget.ImageFilterView
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:src="@drawable/ic_privacy_unlock" />

        <TextView
            android:id="@+id/tvUnlockNum"
            style="@style/regular_12_45"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            tools:text="3" />
    </LinearLayout>

    <TextView
        android:id="@+id/tvNewUnlock"
        style="@style/regular_13_75"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:text="@string/conversion_new_unlock"
        android:textColor="@color/cl_f5222d"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/llLock"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>