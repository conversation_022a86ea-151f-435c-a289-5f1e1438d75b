<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="20dp">

    <LinearLayout
        android:id="@+id/ll_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_bottom_popdialog"
        android:orientation="vertical"
        app:layout_constraintBottom_toTopOf="@+id/id_dialog_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/id_dialog_close"
        style="@style/medium_14_75"
        android:layout_width="match_parent"
        android:layout_marginTop="12dp"
        android:background="@drawable/bg_bottom_popdialog"
        android:gravity="center"
        android:paddingTop="15dp"
        android:paddingBottom="15dp"
        android:text="@string/cancel"
        android:textColor="@color/cl_000000_45"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_content" />

</androidx.constraintlayout.widget.ConstraintLayout>
