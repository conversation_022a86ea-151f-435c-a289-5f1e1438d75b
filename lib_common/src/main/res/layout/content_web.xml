<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/id_webrootView"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.iandroid.allclass.lib_common.web.view.LangWebView
        android:id="@+id/id_content_webview"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/bg_dialog_web"
        app:layout_constraintBottom_toTopOf="@+id/bottomView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:id="@+id/bottomView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <net.csdn.roundview.RoundTextView
            android:id="@+id/bottomBtn"
            style="@style/bold_16_75"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginHorizontal="40dp"
            android:layout_marginVertical="16dp"
            android:background="@color/color_d9d9d9"
            android:gravity="center"
            android:textColor="@color/white"
            app:rRadius="12dp"
            tools:text="Quiz" />
    </FrameLayout>


    <com.iandroid.allclass.lib_basecore.view.CommonPageStatusView
        android:id="@+id/id_com_status"
        style="@style/wrap_wrap"
        android:background="@drawable/bg_round_white"
        android:padding="15dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>