<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_user_level"
        style="@style/medium_11_75"
        android:layout_width="wrap_content"
        android:layout_height="20dp"
        android:layout_marginStart="4dp"
        android:background="@drawable/bg_stroke_gray_r4"
        android:paddingHorizontal="4dp"
        android:gravity="center"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:visibility="gone"
        tools:visibility="visible"
        tools:text="LV.3" />

</androidx.constraintlayout.widget.ConstraintLayout>