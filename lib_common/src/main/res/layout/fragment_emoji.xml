<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/emoji_list"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:overScrollMode="never"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <View
        android:id="@+id/emoji_delete_mask"
        android:layout_width="90dp"
        android:layout_height="80dp"
        android:background="@drawable/bg_chat_emoji_delete_mask"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />


    <ImageView
        android:id="@+id/emoji_delete"
        android:layout_width="60dp"
        android:layout_height="32dp"
        android:layout_marginEnd="12dp"
        android:layout_marginBottom="6dp"
        android:scaleType="fitXY"
        android:src="@drawable/ic_emoji_board_delete"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>