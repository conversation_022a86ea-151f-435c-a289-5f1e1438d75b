<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/alert_dialog_rootview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_dialog_gradient"
        android:padding="24dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/alert_dialog_title"
            style="@style/bold_20_75"
            android:layout_width="0dp"
            android:gravity="center"
            android:textColor="@color/black"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Title" />


        <TextView
            android:id="@+id/alert_dialog_content"
            style="@style/regular_14_75"
            android:layout_width="0dp"
            android:layout_marginTop="16dp"
            android:gravity="center"
            android:lineSpacingExtra="3dp"
            android:maxHeight="360dp"
            android:paddingBottom="24dp"
            android:textColor="@color/cl_262626"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/alert_dialog_title"
            tools:text="Content" />

        <TextView
            android:id="@+id/alert_dialog_confirm"
            style="@style/bold_16_75"
            android:layout_width="0dp"
            android:background="@drawable/bg_btn_solid_75_black"
            android:gravity="center"
            android:paddingVertical="14dp"
            android:text="Ok, I got it"
            android:textColor="@color/white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/alert_dialog_content" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/alert_dialog_close"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:padding="14dp"
        android:src="@drawable/ic_com_close_dialog"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>