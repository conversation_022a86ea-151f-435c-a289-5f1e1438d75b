<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/myExoPlayer"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:background="?attr/colorControlHighlight">

    <ImageButton
        android:id="@id/exo_play"
        style="@style/ExoMediaButton.Play"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        app:layout_constraintBottom_toTopOf="@id/exo_position"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <ImageButton
        android:id="@id/exo_pause"
        style="@style/ExoMediaButton.Pause"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        app:layout_constraintBottom_toTopOf="@id/exo_position"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <ImageButton
        android:id="@+id/exo_fullscreen_button"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:layout_marginEnd="12dp"
        android:layout_marginBottom="10dp"
        android:background="@null"
        android:padding="2dp"
        android:src="@drawable/ic_video_full"
        app:layout_constraintBottom_toTopOf="@id/exo_position"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/exo_position"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginBottom="16dp"
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent" />

    <com.google.android.exoplayer2.ui.DefaultTimeBar
        android:id="@+id/exo_progress"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@+id/exo_position"
        app:layout_constraintLeft_toRightOf="@id/exo_position"
        app:layout_constraintRight_toLeftOf="@+id/exo_duration"
        app:layout_constraintTop_toTopOf="@+id/exo_position"
        app:unplayed_color="@android:color/black" />

    <TextView
        android:id="@+id/exo_duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="@+id/exo_position"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/exo_position" />

</androidx.constraintlayout.widget.ConstraintLayout>