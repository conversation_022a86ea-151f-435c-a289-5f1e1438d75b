<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/alert_dialog_rootview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_dialog_gradient"
        android:padding="24dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/alert_dialog_title"
            style="@style/bold_20_75"
            android:paddingBottom="15dp"
            android:gravity="center"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/dividerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:paddingBottom="13dp"
            android:src="@drawable/shape_com_divider"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/alert_dialog_title" />

        <TextView
            android:id="@+id/alert_dialog_subtitle"
            style="@style/bold_14_75"
            android:layout_width="0dp"
            android:layout_marginBottom="12dp"
            android:gravity="left"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/dividerView" />

        <TextView
            android:id="@+id/alert_dialog_content"
            style="@style/regular_14_75"
            android:layout_width="0dp"
            android:gravity="center_horizontal"
            android:paddingBottom="24dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/alert_dialog_subtitle" />


        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/ll_btn_bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/alert_dialog_content">

            <TextView
                android:id="@+id/alert_dialog_cancel"
                style="@style/bold_16_75"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/bg_btn_border_75"
                android:gravity="center"
                android:padding="10dp"
                android:textColor="@color/cl_262626"
                android:text="@string/btn_cancel" />

            <View
                android:id="@+id/btn_margin"
                android:layout_width="16dp"
                android:layout_height="1px" />

            <TextView
                android:id="@+id/alert_dialog_confirm"
                style="@style/bold_16_75"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:background="@drawable/bg_btn_solid_75_black"
                android:gravity="center"
                android:padding="10dp"
                android:text="@string/btn_sure"
                android:textColor="@color/white" />

        </androidx.appcompat.widget.LinearLayoutCompat>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/alert_dialog_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:padding="10dp"
        android:src="@drawable/ic_com_alert_close"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/alert_dialog_rootview" />

</androidx.constraintlayout.widget.ConstraintLayout>