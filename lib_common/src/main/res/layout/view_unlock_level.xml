<?xml version="1.0" encoding="utf-8"?>
<net.csdn.roundview.RoundConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clUnlockLevelRoot"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:paddingHorizontal="8dp"
    android:paddingVertical="4dp"
    android:background="@color/color_F74E57_a10"
    app:rRadius="30dp">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_unlock_level"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:src="@drawable/ic_mpc_med"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tv_unlock_level" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_unlock_level"
        style="@style/bold_11_75"
        android:textColor="@color/color_F74E57"
        app:layout_constraintStart_toEndOf="@id/iv_unlock_level"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginStart="4dp"
        tools:text="Med" />

</net.csdn.roundview.RoundConstraintLayout>