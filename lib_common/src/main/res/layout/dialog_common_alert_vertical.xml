<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/alert_dialog_rootview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:background="@drawable/bg_dialog_gradient"
        android:padding="24dp">

        <TextView
            android:id="@+id/alert_dialog_title"
            style="@style/bold_20_75"
            android:paddingBottom="15dp"
            android:layout_width="0dp"
            android:gravity="center"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Title" />

        <ImageView
            android:id="@+id/dividerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:paddingBottom="13dp"
            android:src="@drawable/shape_com_divider"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/alert_dialog_title" />

        <TextView
            android:id="@+id/alert_dialog_subtitle"
            style="@style/bold_14_75"
            android:layout_width="0dp"
            android:layout_marginBottom="12dp"
            android:gravity="left"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/dividerView" />

        <TextView
            android:id="@+id/alert_dialog_content"
            style="@style/regular_14_75"
            android:layout_width="0dp"
            android:gravity="left"
            android:paddingBottom="24dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/alert_dialog_subtitle"
            tools:text="Content" />

        <TextView
            android:id="@+id/alert_dialog_confirm"
            style="@style/bold_16_75"
            android:layout_width="0dp"
            android:background="@drawable/bg_btn_solid_75_black"
            android:gravity="center"
            android:paddingVertical="14dp"
            android:text="@string/btn_sure"
            android:textColor="@color/white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/alert_dialog_content" />

        <TextView
            android:id="@+id/alert_dialog_cancel"
            style="@style/bold_13_75"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/btn_cancel"
            android:textColor="@color/cl_262626"
            android:layout_marginTop="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/alert_dialog_confirm" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/alert_dialog_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_com_alert_close"
        android:layout_marginTop="20dp"
        android:visibility="gone"
        android:padding="10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/alert_dialog_rootview" />

</androidx.constraintlayout.widget.ConstraintLayout>