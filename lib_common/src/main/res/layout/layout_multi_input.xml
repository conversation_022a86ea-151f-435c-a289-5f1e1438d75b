<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="#ffffff"
        android:clickable="true"
        app:layout_constraintBottom_toTopOf="@+id/multi_board_layout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/multi_input_edit"
        android:layout_width="0dp"
        android:layout_height="36dp"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/bg_multi_input_edit"
        android:gravity="center_vertical"
        android:hint="@string/multi_input_hint"
        android:imeOptions="flagNoExtractUi|flagNoFullscreen|actionSend"
        android:maxLength="60"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:singleLine="true"
        android:textColor="#000000"
        android:textColorHint="#999999"
        android:textSize="14sp"
        app:layout_constraintBottom_toTopOf="@+id/multi_board_layout"
        app:layout_constraintEnd_toStartOf="@+id/multi_input_mode"
        app:layout_constraintStart_toStartOf="parent" />

    <ImageView
        android:id="@+id/multi_input_mode"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginEnd="8dp"
        android:padding="4dp"
        android:src="@drawable/bg_chat_input_emoji"
        app:layout_constraintBottom_toBottomOf="@+id/multi_input_send"
        app:layout_constraintEnd_toStartOf="@+id/multi_input_picture"
        app:layout_constraintTop_toTopOf="@+id/multi_input_send" />

    <ImageView
        android:id="@+id/multi_input_picture"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginEnd="8dp"
        android:padding="4dp"
        android:src="@drawable/ic_chat_input_image"
        app:layout_constraintBottom_toBottomOf="@+id/multi_input_send"
        app:layout_constraintEnd_toStartOf="@+id/multi_input_send"
        app:layout_constraintTop_toTopOf="@+id/multi_input_send" />

    <ImageView
        android:id="@+id/multi_input_send"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginEnd="12dp"
        android:layout_marginBottom="10dp"
        android:padding="4dp"
        android:src="@drawable/bg_chat_input_send"
        app:layout_constraintBottom_toTopOf="@+id/multi_board_layout"
        app:layout_constraintEnd_toEndOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/multi_board_layout"
        android:layout_width="match_parent"
        android:layout_height="250dp"
        android:background="#ffffff"
        android:clickable="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#f0f0f0"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/multi_board_pager"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginBottom="44dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginBottom="44dp"
            android:background="#f0f0f0"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <ImageView
            android:id="@+id/multi_board_emoji"
            android:layout_width="44dp"
            android:layout_height="32dp"
            android:layout_marginStart="8dp"
            android:layout_marginBottom="6dp"
            android:background="@drawable/bg_chat_emoji_selector"
            android:paddingStart="11dp"
            android:paddingTop="5dp"
            android:paddingEnd="11dp"
            android:paddingBottom="5dp"
            android:scaleType="fitXY"
            android:src="@drawable/ic_emoji_board_icon"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <ImageView
            android:id="@+id/multi_board_collect"
            android:layout_width="44dp"
            android:layout_height="32dp"
            android:layout_marginStart="4dp"
            android:layout_marginBottom="6dp"
            android:background="@drawable/bg_chat_emoji_selector"
            android:paddingStart="11dp"
            android:paddingTop="5dp"
            android:paddingEnd="11dp"
            android:paddingBottom="5dp"
            android:scaleType="fitXY"
            android:src="@drawable/ic_emoji_board_collect"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/multi_board_emoji" />

        <TextView
            android:id="@+id/multi_board_send"
            android:layout_width="74dp"
            android:layout_height="32dp"
            android:layout_marginEnd="12dp"
            android:layout_marginBottom="6dp"
            android:background="@drawable/bg_comm_positive_btn"
            android:gravity="center"
            android:text="@string/action_send"
            android:textColor="#ffffff"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />


        <View
            android:id="@+id/multi_board_mask"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#ffffff"
            android:clickable="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>