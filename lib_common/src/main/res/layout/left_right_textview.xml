<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_vertical"
    android:orientation="horizontal">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/leftTextView"
        style="@style/medium_13_75"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/cl_595959"
        tools:text="Left" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/rightTextView"
        style="@style/medium_13_75"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="end"
        android:textColor="@color/cl_262626"
        tools:text="Right" />
</androidx.appcompat.widget.LinearLayoutCompat>