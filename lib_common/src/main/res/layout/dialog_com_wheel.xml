<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_round_white_r10">

    <View
        android:layout_width="match_parent"
        android:layout_height="90dp"
        android:background="@drawable/bg_dialog_gradient_top"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvTitle"
        style="@style/bold_20_75"
        android:gravity="center"
        android:padding="16dp"
        android:paddingHorizontal="30dp"
        android:paddingBottom="15dp"
        android:textColor="@color/black"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Select" />

    <TextView
        android:id="@+id/tvSubTitle"
        style="@style/regular_13_75"
        android:gravity="center"
        android:paddingHorizontal="16dp"
        android:textColor="@color/cl_262626"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle"
        tools:text="Select" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvDate"
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:layout_marginHorizontal="12dp"
        android:layout_marginTop="24dp"
        android:clipToPadding="false"
        android:overScrollMode="never"
        android:scrollbars="none"
        app:layout_constraintTop_toBottomOf="@+id/tvSubTitle" />

    <net.csdn.roundview.RoundView
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:background="@color/cl_747480"
        app:layout_constraintBottom_toBottomOf="@+id/rvDate"
        app:layout_constraintEnd_toEndOf="@+id/rvDate"
        app:layout_constraintStart_toStartOf="@+id/rvDate"
        app:layout_constraintTop_toTopOf="@+id/rvDate"
        app:rRadius="8dp" />

    <TextView
        android:id="@+id/tvCancel"
        style="@style/bold_16_75"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="26dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/bg_storke_262626_r16"
        android:gravity="center"
        android:padding="10dp"
        android:text="@string/btn_cancel"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/tvConfirm"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rvDate" />

    <TextView
        android:id="@+id/tvConfirm"
        style="@style/bold_16_75"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="16dp"
        android:background="@drawable/bg_btn_solid_75_black"
        android:gravity="center"
        android:padding="10dp"
        android:text="@string/confirm"
        android:textColor="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvCancel"
        app:layout_constraintTop_toTopOf="@+id/tvCancel" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivClose"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="8dp"
        android:src="@drawable/icon_close_no_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>