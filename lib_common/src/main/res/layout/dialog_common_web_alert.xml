<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_round_common_alert_dialog"
    android:padding="13dp">

    <ImageView
        android:id="@+id/alert_dialog_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_close_black"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/alert_dialog_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="10dp"
        android:textColor="#1a1d29"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.iandroid.allclass.lib_common.web.view.LangWebView
        android:id="@+id/alert_dialog_content"
        android:layout_width="0dp"
        android:layout_height="300dp"
        android:layout_marginStart="5dp"
        android:layout_marginTop="15dp"
        android:layout_marginEnd="5dp"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/alert_dialog_title" />

    <TextView
        android:id="@+id/alert_dialog_cancel"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="5dp"
        android:layout_marginTop="15dp"
        android:layout_marginEnd="5dp"
        android:background="@drawable/bg_comm_negative_btn"
        android:gravity="center"
        android:padding="10dp"
        android:text="@string/btn_cancel"
        android:textColor="#fe7c39"
        android:textSize="14sp"
        app:layout_constraintEnd_toStartOf="@+id/alert_dialog_confirm"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/alert_dialog_content" />

    <TextView
        android:id="@+id/alert_dialog_confirm"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="5dp"
        android:layout_marginTop="15dp"
        android:layout_marginEnd="5dp"
        android:background="@drawable/bg_comm_positive_btn"
        android:gravity="center"
        android:padding="10dp"
        android:text="@string/btn_sure"
        android:textColor="@color/ffffff_100"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/alert_dialog_cancel"
        app:layout_constraintTop_toBottomOf="@+id/alert_dialog_content" />

</androidx.constraintlayout.widget.ConstraintLayout>