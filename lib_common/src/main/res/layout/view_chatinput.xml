<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:id="@+id/vLine"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/cr_000000_10"
        app:layout_constraintBottom_toTopOf="@+id/chat_input_edit"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <EditText
        android:id="@+id/chat_input_edit"
        android:layout_width="0dp"
        android:layout_height="35dp"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="6dp"
        android:layout_marginRight="5dp"
        android:layout_marginBottom="6dp"
        android:singleLine="true"
        android:background="@drawable/bg_chat_input"
        android:imeOptions="flagNoExtractUi|flagNoFullscreen|actionSend"
        android:paddingLeft="15dp"
        android:paddingTop="5dp"
        android:paddingRight="15dp"
        android:maxLength="100"
        android:paddingBottom="5dp"
        android:textColor="@color/cr_000000_90"
        android:textColorHint="@color/cr_000000_40"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/chat_input_send"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vLine" />

    <TextView
        android:id="@+id/chat_input_send"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="5dp"
        android:paddingBottom="5dp"
        android:paddingLeft="10dp"
        android:paddingRight="15dp"
        android:text="@string/action_send"
        android:textColor="@drawable/selector_txtcr_chat_send"
        android:textSize="16sp"
        app:layout_constraintBaseline_toBaselineOf="@+id/chat_input_edit"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/chat_input_edit"
        app:layout_constraintTop_toBottomOf="@+id/vLine" />

</androidx.constraintlayout.widget.ConstraintLayout>