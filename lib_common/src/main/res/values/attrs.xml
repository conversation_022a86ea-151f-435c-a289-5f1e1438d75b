<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="ChatInputView">
        <attr name="rootView" format="reference" />
        <attr name="viewLayout" format="reference" />
        <attr name="hintText" format="string" />
        <attr name="textLength" format="integer" />
    </declare-styleable>

    <declare-styleable name="FlowLayout">
        <attr name="fl_horizontalChildGap" format="dimension" />
        <attr name="fl_verticalChildGap" format="dimension" />
        <attr name="fl_maxWidthReservedSpace" format="dimension" />
        <attr name="fl_isDistributionWhiteSpacing" format="boolean" />
        <attr name="fl_isSingleLineWrapWidth" format="boolean" />
    </declare-styleable>

    <declare-styleable name="LoadingButtonView">
        <attr name="lb_text" format="string" />
        <attr name="lb_text_color" format="reference|color" />
        <attr name="lb_text_size" format="dimension" />
        <attr name="lb_enable" format="boolean" />
    </declare-styleable>

    <declare-styleable name="TopBottomTypeTextView">
        <attr name="tbt_bottom_text" format="string" />
        <attr name="tbt_top_text_size" format="dimension" />
        <attr name="tbt_top_text_color" format="color|reference" />
        <attr name="tbt_bottom_text_color" format="color|reference" />
        <attr name="tbt_bottom_text_size" format="dimension" />
        <attr name="tbt_top_bottom_gap" format="dimension" />
    </declare-styleable>

    <declare-styleable name="SwitchCompatView">
        <attr name="sc_title" format="string" />
        <attr name="sc_des" format="string" />
        <attr name="sc_showDivide" format="boolean" />
        <attr name="sc_title_color" format="color" />
        <attr name="sc_des_color" format="color" />
    </declare-styleable>

    <declare-styleable name="ColorOvalTextView">
        <attr name="oval_solid_color" format="color" />
        <attr name="oval_stroke_color" format="color" />
        <attr name="oval_text_color" format="color" />
        <attr name="oval_stroke_size" format="dimension" />
    </declare-styleable>

    <declare-styleable name="RoundTextView">
        <attr name="rv_backgroundColor" />
        <attr name="rv_backgroundColorStart" />
        <attr name="rv_backgroundColorEnd" />
        <attr name="rv_backgroundPressColor" />
        <attr name="rv_cornerRadius" />
        <attr name="rv_strokeWidth" />
        <attr name="rv_strokeColor" />
        <attr name="rv_strokePressColor" />
        <attr name="rv_textPressColor" />
        <attr name="rv_isRadiusHalfHeight" />
        <attr name="rv_isWidthHeightEqual" />
        <attr name="rv_cornerRadius_TL" />
        <attr name="rv_cornerRadius_TR" />
        <attr name="rv_cornerRadius_BL" />
        <attr name="rv_cornerRadius_BR" />
        <attr name="rv_isRippleEnable" />
    </declare-styleable>


    <declare-styleable name="LangTextView">
        <attr name="startIcon" format="reference" />
        <attr name="hintTextValue" format="string" />
    </declare-styleable>

    <declare-styleable name="WidthScaleLayout">
        <attr name="mWidthScale" format="float" />
        <attr name="mHeightScale" format="float" />
    </declare-styleable>

    <declare-styleable name="SuRectIndicator">
        <attr name="selectColor" format="color" />
        <attr name="unselectColor" format="color" />
        <attr name="openWeight" format="boolean" />
    </declare-styleable>

    <declare-styleable name="LeftRightTextView">
        <attr name="leftText" format="string" />
        <attr name="rightText" format="string" />
        <attr name="leftTextColor" format="color" />
        <attr name="rightTextColor" format="color" />
    </declare-styleable>
</resources>
