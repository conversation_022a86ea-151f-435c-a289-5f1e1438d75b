<resources>
    <style name="bottom_pop_dialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@style/bottom_pop_anim</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:background">@null</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowSoftInputMode">stateUnchanged|adjustResize</item>
        <item name="android:configChanges">keyboardHidden|orientation|screenSize</item>
    </style>

    <style name="bottom_pop_anim"  parent="@android:style/Theme.Dialog">
        <item name="android:windowEnterAnimation">@anim/anim_translate_alpha_bottom_show</item>
        <item name="android:windowExitAnimation">@anim/anim_translate_alpha_bottom_hide</item>
    </style>

    <style name="top_pop_anim"  parent="@android:style/Theme.Dialog">
        <item name="android:windowEnterAnimation">@anim/anim_translate_alpha_top_show</item>
        <item name="android:windowExitAnimation">@anim/anim_translate_alpha_top_hide</item>
    </style>

    <style name="match_match">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
    </style>

    <style name="match_wrap">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
    </style>

    <style name="wrap_wrap">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
    </style>

    <style name="wrap_match">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">match_parent</item>
    </style>

    <style name="wrap_wrap_horizontal" parent="wrap_wrap">
        <item name="android:orientation">horizontal</item>
    </style>

    <style name="base_text_white_14_size" parent="@style/wrap_wrap">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/app_FFFFFF</item>
        <item name="android:singleLine">true</item>
        <item name="android:ellipsize">end</item>
    </style>

    <style name="base_text_white_13_size" parent="@style/wrap_wrap">
        <item name="android:textSize">13sp</item>
        <item name="android:textColor">@color/app_FFFFFF</item>
        <item name="android:singleLine">true</item>
        <item name="android:ellipsize">end</item>
    </style>

    <style name="base_text_white_12_size" parent="@style/wrap_wrap">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/app_FFFFFF</item>
        <item name="android:singleLine">true</item>
        <item name="android:ellipsize">end</item>
    </style>

    <style name="base_text_white_10_size" parent="@style/wrap_wrap">
        <item name="android:textSize">10sp</item>
        <item name="android:textColor">@color/app_FFFFFF</item>
        <item name="android:singleLine">true</item>
        <item name="android:ellipsize">end</item>
    </style>

    <style name="anim_com_dialog" parent="@android:style/Animation.Translucent">
        <item name="android:windowEnterAnimation">@anim/anim_dialog_show</item>
        <item name="android:windowExitAnimation">@anim/anim_dialog_hide</item>
    </style>

    <style name="anim_right_dialog" parent="@android:style/Animation.Translucent">
        <item name="android:windowEnterAnimation">@anim/anim_dialog_right_in</item>
        <item name="android:windowExitAnimation">@anim/anim_dialog_right_out</item>
    </style>

    <style name="anim_left_dialog" parent="@android:style/Animation.Translucent">
        <item name="android:windowEnterAnimation">@anim/anim_dialog_left_in</item>
        <item name="android:windowExitAnimation">@anim/anim_dialog_left_out</item>
    </style>

    <style name="caption_text">
        <item name="android:textColor">#333333</item>
        <item name="android:textSize">25sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="base_style_dialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@style/anim_com_dialog</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:background">@null</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <style name="com_anim_dialog" parent="base_style_dialog">
        <item name="android:windowAnimationStyle">@style/anim_com_dialog</item>
        <item name="android:windowSoftInputMode">stateUnchanged|adjustResize</item>
        <item name="android:configChanges">keyboardHidden|orientation|screenSize</item>
    </style>

    <style name="linear_ww_h" parent="@style/wrap_wrap">
        <item name="android:orientation">horizontal</item>
    </style>

    <style name="lang_switch" parent="Theme.AppCompat.Light">
        <!--On-->
        <item name="colorControlActivated">@color/cl_fe7c39</item>
        <!--Off-->
        <item name="colorSwitchThumbNormal">@color/cl_ffffff</item>
        <!--Off bg-->
        <item name="android:colorForeground">@color/cl_999999</item>
    </style>

    <style name="dialog_activity_styles" parent="@style/Theme.AppCompat.Dialog">
        <item name="android:windowBackground">@color/transparent</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowDisablePreview">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@style/bottom_pop_anim</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style>

    <style name="moment_item_image_rv">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">0dp</item>
        <item name="android:horizontalSpacing">7dp</item>
        <item name="android:listSelector">#00000000</item>
        <item name="android:numColumns">3</item>
        <item name="android:scrollbars">none</item>
        <item name="android:stretchMode">columnWidth</item>
        <item name="android:verticalSpacing">7dp</item>
    </style>

    <style name="room_game_rv">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:horizontalSpacing">5dp</item>
        <item name="android:listSelector">#00000000</item>
        <item name="android:numColumns">5</item>
        <item name="android:scrollbars">none</item>
        <item name="android:stretchMode">columnWidth</item>
        <item name="android:verticalSpacing">5dp</item>
    </style>

    <style name="CommonTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <!-- Customize your theme here. -->
    </style>

    <style name="CommonTranslucentTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <style name="FullScreenDialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>

</resources>
