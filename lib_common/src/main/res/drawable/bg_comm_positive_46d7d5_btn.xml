<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <corners android:radius="30dp" />
            <solid android:color="#46d7d5"/>

        </shape>
    </item>

    <item android:state_enabled="false">
        <shape android:shape="rectangle">
            <corners android:radius="30dp" />
            <solid android:color="@color/cl_999999"/>
        </shape>
    </item>

    <item android:state_pressed="false">
        <shape android:shape="rectangle">
            <corners android:radius="30dp" />
            <solid android:color="#46d7d5"/>
        </shape>
    </item>
</selector>